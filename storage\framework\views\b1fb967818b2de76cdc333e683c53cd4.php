

<?php $__env->startSection('content'); ?>
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    

    <section class="profile_page trip_management user_managment_show">
        <div class="container-fluid">
            <div class="row custom_row">
                <div class="col-md-8">
                    <form>
                        <div class="custom_cards_design icon_image">
                            <div class="row custom_row">
                                
                                <div class="col-md-12">
                                    <h2>Company Information</h2>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Company Name:</label>
                                        <input type="text" class="form-control myinput" id=""
                                            value="<?php echo e($user->company_name ?? ''); ?>" readonly required>
                                    </div>
                                </div>
                                <div class="col-md-6">mkae 
                                    <div class="txt_field">
                                        <label for="" class="form-label">Represented Name:</label>
                                        <input type="text" class="form-control myinput" id=""
                                            value="<?php echo e($user->name ?? ''); ?>" readonly required>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Email Address:</label>
                                        <input type="email" class="form-control myinput" id=""
                                            value="<?php echo e($user->email ?? ''); ?>" readonly required>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                    </form>
                </div>
                <div class="col-md-4">
                    <div class="user_trip_details">
                        <div class="row custom_row_card custom_row">
                            <div class="col-md-12 custom_column_card">
                                <div class="custom_card">
                                    <h2>Total Trips</h2>
                                    <h1 class="f_30"><?php echo e($totalTrips ?? 0); ?></h1>
                                </div>
                            </div>
                            <div class="col-md-12 custom_column_card">
                                <div class="custom_card">
                                    <h2>Active trips</h2>
                                    <h1 class="f_30"><?php echo e($activeTrips ?? 0); ?></h1>
                                </div>
                            </div>
                            <div class="col-md-6 custom_column_card">
                                <div class="custom_card">
                                    <h2>Active Subscription</h2>
                                    <h1 class="f_30"><?php echo e($mySubscription ?? '-'); ?></h1>
                                </div>
                            </div>
                            <div class="col-md-6 custom_column_card">
                                <div class="custom_card">
                                    <h2>Company Status</h2>
                                    <span class="success">Active</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="custom_table custom_cards_design">
                        <h1>Recent Trips</h1>
                        <div class="custom_scroll_tbl">
                            <table class="table without_pagination_tbl datatable">
                                <thead>
                                    <tr>
                                        <th>SR#</th>
                                        <th>Trip Name</th>
                                        <th>URL</th>
                                        <th>Duration</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentTrips; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($loop->iteration); ?></td>
                                            <td><?php echo e($item->name ?? ''); ?></td>
                                            <td><?php echo e(route('trip.itinerary.overview', [$item->company_slug ?? '', $item->url_slug ?? ''])); ?>

                                            </td>
                                            <td><?php echo e($item->duration ?? ''); ?> Days</td>
                                            <td><span
                                                    class="status-label <?php echo e($item->status === 'active' ? 'success' : 'danger'); ?>">
                                                    <?php echo e(ucfirst($item->status)); ?>

                                                </span></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\theme\user-management\users\show.blade.php ENDPATH**/ ?>