<!DOCTYPE html>

<html lang="en">
<!--begin::Head-->

<head>
    <base href="" />
    <title><PERSON> (Guest Trip) </title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="" />
    <meta property="og:url" content="" />
    <meta property="og:site_name" content="" />
    <link rel="canonical" href="" />
    <link rel="shortcut icon" href="<?php echo e(asset('website')); ?>/assets/images/header_logo.svg" />
    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />

    <!--end::Fonts-->
    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <link href="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" />
    <?php if(isset($trip)): ?>
        <?php if($trip->template->name == 'VariationOne'): ?>
            <link href="<?php echo e(asset('website')); ?>/assets/css/packages_style.css" rel="stylesheet" type="text/css" />
        <?php elseif($trip->template->name == 'VariationTwo'): ?>
            <link href="<?php echo e(asset('website')); ?>/assets/css/pack_variation_two.css" rel="stylesheet" type="text/css" />
        <?php elseif($trip->template->name == 'VariationThree'): ?>
            <link href="<?php echo e(asset('website')); ?>/assets/css/pack_variation_three.css" rel="stylesheet" type="text/css" />
        <?php elseif($trip->template->name == 'VariationFour'): ?>
            <link href="<?php echo e(asset('website')); ?>/assets/css/pack_variation_four.css" rel="stylesheet" type="text/css" />
        <?php elseif($trip->template->name == 'VariationFive'): ?>
            <link href="<?php echo e(asset('website')); ?>/assets/css/pack_variation_five.css" rel="stylesheet" type="text/css" />
        <?php endif; ?>
    <?php else: ?>
        <link href="<?php echo e(asset('website')); ?>/assets/css/packages_style.css" rel="stylesheet" type="text/css" />
    <?php endif; ?>
    <?php echo $__env->yieldPushContent('css'); ?>
</head>
<!--end::Head-->
<!--begin::Body-->

<body id="kt_body" data-bs-spy="scroll" data-bs-target="#kt_landing_menu" class="bg-body position-relative app-blank">
    <!--begin::Theme mode setup on page load-->
    <script>
        var defaultThemeMode = "light";
        var themeMode;
        if (document.documentElement) {
            if (document.documentElement.hasAttribute("data-bs-theme-mode")) {
                themeMode = document.documentElement.getAttribute("data-bs-theme-mode");
            } else {
                if (localStorage.getItem("data-bs-theme") !== null) {
                    themeMode = localStorage.getItem("data-bs-theme");
                } else {
                    themeMode = defaultThemeMode;
                }
            }
            if (themeMode === "system") {
                themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
            }
            document.documentElement.setAttribute("data-bs-theme", themeMode);
        }
    </script>
    <!--end::Theme mode setup on page load-->
    <!--begin::Root-->
    <div class="" id="kt_app_root">
        <!--begin::Header Section-->
        
        <nav class="navbar navbar-expand-lg packages_navbar">
            <div class="container custom_container">
                <button class="navbar-toggler" type="button" data-toggle="collapse"
                    data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
                    aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse custom_navbar_list" id="navbarSupportedContent">
                    <ul class="navbar-nav ">
                        <?php if($trip->sections->contains('id', 1)): ?>
                            <li class="nav-item <?php if(request()->route()->getName() == 'trip.itinerary.overview'): ?> active <?php endif; ?>">
                                <a class="nav-link"
                                    href="<?php echo e(route('trip.itinerary.overview', [$trip->company_slug, $trip->url_slug])); ?>">Itinerary
                                    Overview</a>
                            </li>
                            <li class="nav-item <?php if(request()->route()->getName() == 'trip.package.tripdetails'): ?> active <?php endif; ?>">
                                <a class="nav-link "
                                    href="<?php echo e(route('trip.package.tripdetails', [$trip->company_slug, $trip->url_slug])); ?>">Trip
                                    Details</a>
                            </li>
                        <?php endif; ?>
                        <?php if($trip->sections->contains('id', 2)): ?>
                            <li class="nav-item <?php if(request()->route()->getName() == 'trip.crew.members'): ?> active <?php endif; ?>">
                                <a class="nav-link"
                                    href="<?php echo e(route('trip.crew.members', [$trip->company_slug, $trip->url_slug])); ?>">Crew</a>
                            </li>
                        <?php endif; ?>
                        <?php if($trip->sections->contains('id', 3)): ?>
                            <li class="nav-item <?php if(request()->route()->getName() == 'trip.menu.plan'): ?> active <?php endif; ?>">
                                <a class="nav-link"
                                    href="<?php echo e(route('trip.menu.plan', [$trip->company_slug, $trip->url_slug])); ?>">Menu
                                    Plan</a>
                            </li>
                        <?php endif; ?>
                        <?php if($trip->sections->contains('id', 4)): ?>
                            <li class="nav-item <?php if(request()->route()->getName() == 'trip.spa.menu'): ?> active <?php endif; ?>">
                                <a class="nav-link"
                                    href="<?php echo e(route('trip.spa.menu', [$trip->company_slug, $trip->url_slug])); ?>">Spa
                                    Menu</a>
                            </li>
                        <?php endif; ?>
                        <?php if($trip->sections->contains('id', 5)): ?>
                            <li class="nav-item <?php if(request()->route()->getName() == 'trip.packages.activities'): ?> active <?php endif; ?>">
                                <a class="nav-link"
                                    href="<?php echo e(route('trip.packages.activities', [$trip->company_slug, $trip->url_slug])); ?>">Activities</a>
                            </li>
                        <?php endif; ?>
                        <?php if($trip->sections->contains('id', 6)): ?>
                            <li class="nav-item <?php if(request()->route()->getName() == 'trip.specifications'): ?> active <?php endif; ?>">
                                <a class="nav-link"
                                    href="<?php echo e(route('trip.specifications', [$trip->company_slug, $trip->url_slug])); ?>">Specifications</a>
                            </li>
                        <?php endif; ?>
                        <?php if($trip->sections->contains('id', 7)): ?>
                            <li class="nav-item <?php if(request()->route()->getName() == 'trip.safety.onboard'): ?> active <?php endif; ?>">
                                <a class="nav-link"
                                    href="<?php echo e(route('trip.safety.onboard', [$trip->company_slug, $trip->url_slug])); ?>">Safety
                                    Onboard</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </nav>
        
        <?php echo $__env->yieldContent('content'); ?>
        
        <footer class="footer_sec">
            <div class="container custom_container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="custom_space_between copy_rights">
                            <h6>©2024 GuestTrip. All Rights Reserved.</h6>
                            <div class="custom_flex terms_privacy">
                                <a href="javascript:void(0)">
                                    <h6>Privacy Policy</h6>
                                </a>
                                <a href="javascript:void(0)">
                                    <h6>Terms & Condition</h6>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
        
    </div>
    <!--end::Root-->
    <!--begin::Javascript-->
    <script>
        var hostUrl = "<?php echo e(asset('website')); ?>/assets/";
    </script>
    <!--begin::Global Javascript Bundle(mandatory for all pages)-->
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/js/scripts.bundle.js"></script>
    <!--end::Global Javascript Bundle-->
    <!--begin::Vendors Javascript(used for this page only)-->
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/custom/fslightbox/fslightbox.bundle.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/custom/typedjs/typedjs.bundle.js"></script>
    <!--end::Vendors Javascript-->
    <!--begin::Custom Javascript(used for this page only)-->
    <script src="<?php echo e(asset('website')); ?>/assets/js/custom/landing.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/js/custom/pages/pricing/general.js"></script>
    
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <!-- Initialize Swiper -->
    <script>
        var swiper = new Swiper(".crewCardSwiper", {
            slidesPerView: 4,
            spaceBetween: 30,
            loop: true,
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
        });

        var itenarySwiper = new Swiper(".itenarySwiper", {
            slidesPerView: 1,
            spaceBetween: 10,
            loop: true,
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
            },
        });
    </script>
    <?php echo $__env->yieldPushContent('js'); ?>
</body>

</html>
<?php /**PATH D:\guesttrip\resources\views/website/layout/master_trip.blade.php ENDPATH**/ ?>