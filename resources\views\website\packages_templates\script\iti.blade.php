{{-- fancybox cdn    --}}
{{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/fancybox/3.5.7/jquery.fancybox.min.js"></script> --}}
<script>
    $(document).ready(function() {
        $('[data-fancybox="gallery"]').fancybox({
            protect: false,
            clickOutside: false,
            closeExisting: false,
        });

        // read more functionality
        $(".pre_line_description").each(function() {
            var $this = $(this);
            var readMore = $this.next(".read_more");

            // Check if text is overflowing
            if (this.scrollHeight > $this.outerHeight()) {
                readMore.show(); // Only show Read More if text is overflowing
            } else {
                readMore.remove(); // Remove Read More if text fits within 5 lines
            }

            readMore.click(function() {
                if ($this.hasClass("expanded")) {
                    $this.removeClass("expanded").css({
                        display: "-webkit-box",
                        overflow: "hidden",
                        maxHeight: "20em"
                    });
                    $(this).text("Read More");
                } else {
                    $this.addClass("expanded").css({
                        display: "block",
                        overflow: "visible",
                        maxHeight: "none"
                    });
                    $(this).text("Read Less");
                }
            });
        });
    });
</script>
<script>
    var swiper = new Swiper(".itineryCardSwiper", {
        slidesPerView: "{{ $trip->itineraries->count() < 4 ? $trip->itineraries->count() : 4 }}",
        {{--            slidesPerView: "{{ $trip->itineraries->count() <5 ? $trip->itineraries->count() : 5 }}", --}}
        spaceBetween: 30,
        loop: false,
        navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
        },
        breakpoints: {
            1920: {
                slidesPerView: 4,
            },
            1600: {
                slidesPerView: 4,
            },
            1440: {
                slidesPerView: 4,
            },
            1336: {
                slidesPerView: 4,
            },
            1280: {
                slidesPerView: 3,
            },
            1024: {
                slidesPerView: 3,
            },
            991: {
                slidesPerView: 3,
            },
            800: {
                slidesPerView: 3,
            },
            768: {
                slidesPerView: 2,
            },
            767: {
                slidesPerView: 2,
            },
            600: {
                slidesPerView: 2,
            },
            515: {
                slidesPerView: 2,
            },
            474: {
                slidesPerView: 1,
            },
            424: {
                slidesPerView: 1,
            },
            374: {
                slidesPerView: 1,
            },
            320: {
                slidesPerView: 1,
            },
        },
    });

    //        var itenarySwiper = new Swiper(".itenarySwiper", {
    //            slidesPerView: 1,
    //            spaceBetween: 10,
    //            loop: true,
    //            pagination: {
    //                el: ".swiper-pagination",
    //                clickable: true,
    //            },
    //        });
</script>
<script>
    function generatePathCoordinates(itineraries) {
        const allPoints = [];
        const pathCoordinates = [];

        if (itineraries && itineraries.length > 0) {
            console.log('Processing itineraries for map:', itineraries.length);

            // Collect ALL locations from all itineraries
            itineraries.forEach((itinerary, index) => {
                const startLat = parseFloat(itinerary.start_lat);
                const startLng = parseFloat(itinerary.start_lng);
                const endLat = parseFloat(itinerary.end_lat);
                const endLng = parseFloat(itinerary.end_lng);
                const nightStay = itinerary.night_stay === 1 || itinerary.night_stay === true;
                const startPoint = itinerary.start_point;
                const endPoint = itinerary.end_point;

                console.log(`Itinerary ${index + 1}:`, {
                    startPoint,
                    endPoint,
                    nightStay,
                    startLat,
                    startLng,
                    endLat,
                    endLng
                });

                // Always add start point if it has valid coordinates
                if (startLat && startLng && startPoint && startPoint.trim() !== '') {
                    allPoints.push({
                        lat: startLat,
                        lng: startLng,
                        name: startPoint,
                        type: 'start',
                        itineraryIndex: index,
                        nightStay: nightStay
                    });
                }

                // Always add end point if it has valid coordinates (regardless of night stay for display)
                // Night stay logic affects the travel path, but we still want to show the location
                if (endLat && endLng && endPoint && endPoint.trim() !== '') {
                    allPoints.push({
                        lat: endLat,
                        lng: endLng,
                        name: endPoint,
                        type: 'end',
                        itineraryIndex: index,
                        nightStay: nightStay
                    });
                }
            });

            // Remove duplicates based on coordinates (with small tolerance for floating point comparison)
            const tolerance = 0.0001;
            allPoints.forEach((point) => {
                const isDuplicate = pathCoordinates.some(existingPoint => {
                    return Math.abs(existingPoint.lat - point.lat) < tolerance &&
                           Math.abs(existingPoint.lng - point.lng) < tolerance;
                });

                if (!isDuplicate) {
                    pathCoordinates.push({
                        lat: point.lat,
                        lng: point.lng,
                        name: point.name,
                        type: point.type,
                        itineraryIndex: point.itineraryIndex,
                        nightStay: point.nightStay
                    });
                }
            });

            console.log('All points collected:', allPoints);
            console.log('Deduplicated pathCoordinates:', pathCoordinates);
        } else {
            console.error("No itineraries found or data is invalid.");
        }

        return pathCoordinates;
    }

    // Generate travel path coordinates (for polyline) - this respects night stay logic
    function generateTravelPath(itineraries) {
        const travelPath = [];

        if (itineraries && itineraries.length > 0) {
            console.log('Generating travel path for polyline...');

            itineraries.forEach((itinerary, index) => {
                const startLat = parseFloat(itinerary.start_lat);
                const startLng = parseFloat(itinerary.start_lng);
                const endLat = parseFloat(itinerary.end_lat);
                const endLng = parseFloat(itinerary.end_lng);
                const nightStay = itinerary.night_stay === 1 || itinerary.night_stay === true;

                // Add start point for first itinerary
                if (index === 0 && startLat && startLng) {
                    travelPath.push({
                        lat: startLat,
                        lng: startLng
                    });
                }

                // Add end point only if night stay is not checked (actual travel destination)
                if (!nightStay && endLat && endLng) {
                    travelPath.push({
                        lat: endLat,
                        lng: endLng
                    });
                }
            });

            console.log('Travel path for polyline:', travelPath);
        }

        return travelPath;
    }

    const itineraries = @json($trip->itineraries); // Assuming this is coming from Laravel
    const pathCoordinates = generatePathCoordinates(itineraries); // All unique locations for markers
    const travelPath = generateTravelPath(itineraries); // Actual travel path for polyline

    function initMap() {
        const map = new google.maps.Map(document.getElementById("map"), {
            center: {
                lat: 24.8607343,
                lng: 67.0011364,
            }, // Center map around Karachi
            zoom: 6,
        });

        // Create polyline to show actual travel path (respects night stay logic)
        if (travelPath.length > 1) {
            console.log('Creating polyline with travel path:', travelPath);
            const polyline = new google.maps.Polyline({
                path: travelPath,
                geodesic: true,
                strokeColor: "#FF0000",
                strokeOpacity: 1.0,
                strokeWeight: 3,
                icons: [{
                    icon: {
                        path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                        scale: 3,
                        strokeColor: '#FF0000'
                    },
                    offset: '100%',
                    repeat: '200px'
                }]
            });
            polyline.setMap(map);
        } else {
            console.log('Not enough travel points for polyline. Travel path:', travelPath);
        }

        const apiKey = "02d522f701e289110d83898599ed9487"; // Replace with your OpenWeatherMap API key
        const weatherInfoSpan = document.getElementById("weather-info");

        // Function to fetch weather data
        function fetchWeather(location, targetElementId = null) {
            const url =
                `https://api.openweathermap.org/data/2.5/weather?lat=${location.lat}&lon=${location.lng}&appid=${apiKey}&units=metric`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.cod === 200) {
                        const temp = Math.round(data.main.temp); // Round temperature
                        const description = data.weather[0].description;
                        const humidity = data.main.humidity;
                        const windSpeed = data.wind.speed;

                        // Update main weather display
                        if (!targetElementId) {
                            weatherInfoSpan.textContent = `${temp} ℃`;
                        } else {
                            // Update marker-specific weather display
                            const targetElement = document.getElementById(targetElementId);
                            if (targetElement) {
                                targetElement.innerHTML = `
                                    <strong>${temp}°C</strong><br>
                                    <span style="text-transform: capitalize;">${description}</span><br>
                                    <small>Humidity: ${humidity}% | Wind: ${windSpeed} m/s</small>
                                `;
                            }
                        }
                    } else {
                        console.error("Error fetching weather data:", data.message);
                        if (!targetElementId) {
                            weatherInfoSpan.textContent = "unavailable.";
                        } else {
                            const targetElement = document.getElementById(targetElementId);
                            if (targetElement) {
                                targetElement.textContent = "Weather unavailable";
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error("Failed to fetch weather data:", error);
                    if (!targetElementId) {
                        weatherInfoSpan.textContent = "unavailable.";
                    } else {
                        const targetElement = document.getElementById(targetElementId);
                        if (targetElement) {
                            targetElement.textContent = "Weather unavailable";
                        }
                    }
                });
        }

        // Show weather for the first coordinate initially
        if (pathCoordinates.length > 0) {
            fetchWeather(pathCoordinates[0]);
        }

        // Add markers for each unique location
        console.log('Creating markers for', pathCoordinates.length, 'unique locations');
        pathCoordinates.forEach((coord, index) => {
            // Create marker with different colors for different types
            const markerColor = coord.nightStay ? '#FFA500' : '#FF0000'; // Orange for night stay, Red for regular

            const marker = new google.maps.Marker({
                position: { lat: coord.lat, lng: coord.lng },
                map: map,
                title: coord.name || `Location ${index + 1}`,
                label: {
                    text: (index + 1).toString(),
                    color: 'white',
                    fontWeight: 'bold'
                },
                icon: {
                    path: google.maps.SymbolPath.CIRCLE,
                    scale: 12,
                    fillColor: markerColor,
                    fillOpacity: 1,
                    strokeColor: '#FFFFFF',
                    strokeWeight: 2
                }
            });

            // Create info window with location details
            const nightStayText = coord.nightStay ? '<br><span style="color: orange; font-weight: bold;">🌙 Night Stay Location</span>' : '';
            const infoWindow = new google.maps.InfoWindow({
                content: `<div style="padding: 10px; min-width: 200px;">
                    <strong style="font-size: 14px;">${coord.name || `Location ${index + 1}`}</strong><br>
                    <span style="color: #666;">Stop ${index + 1} (${coord.type})</span>${nightStayText}<br>
                    <div id="weather-marker-${index}" style="margin-top: 8px; padding: 5px; background: #f5f5f5; border-radius: 3px; font-size: 12px;">
                        <span style="color: #888;">Loading weather...</span>
                    </div>
                </div>`
            });

            // On marker click, show info window and fetch weather
            marker.addListener("click", () => {
                // Close any open info windows
                if (window.openInfoWindow) {
                    window.openInfoWindow.close();
                }

                infoWindow.open(map, marker);
                window.openInfoWindow = infoWindow;

                // Fetch weather for this location
                fetchWeather(coord, `weather-marker-${index}`);

                // Also update the main weather display
                fetchWeather(coord);
            });
        });

        // Adjust map bounds to fit all markers
        if (pathCoordinates.length > 0) {
            const bounds = new google.maps.LatLngBounds();
            pathCoordinates.forEach(coord => bounds.extend({ lat: coord.lat, lng: coord.lng }));
            map.fitBounds(bounds);

            // Add some padding to the bounds and set reasonable zoom for single point
            if (pathCoordinates.length === 1) {
                map.setZoom(12); // Set a reasonable zoom for single point
            }
        } else {
            console.log("No coordinates to display on map.");
        }
    }
</script>
<!-- Include Google Maps API -->
<script
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAbi9IUF4TBu58oC9iGZexb045rMaQr2AQ&libraries=places&callback=initMap"
    async defer></script>
