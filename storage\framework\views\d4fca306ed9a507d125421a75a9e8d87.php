<!DOCTYPE html>

<html lang="en">
<!--begin::Head-->

<head>
    <base href="" />
    <title><PERSON> (Guest Trip) </title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="" />
    <meta property="og:url" content="" />
    <meta property="og:site_name" content="" />
    <link rel="canonical" href="" />
    <link rel="shortcut icon" href="<?php echo e(asset('website')); ?>/assets/images/header_logo.svg" />
    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />

    <!--end::Fonts-->
    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <link href="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" />
		<?php if(request()->is('itinerary_overview') || request()->is('package_trip_detail') || request()->is('crew_members') || request()->is('trips_specifications') || request()->is('menu_plan') || request()->is('spa_menu') || request()->is('packages_activities') || request()->is('safety_onboard')): ?>
        
        
        <link href="<?php echo e(asset('website')); ?>/assets/css/packages_style.css" rel="stylesheet" type="text/css" />
        <?php elseif(request()->is('itinerary_overview_two') || request()->is('package_trip_detail_two') || request()->is('crew_members_two') || request()->is('trips_specifications_two') || request()->is('menu_plan_two') || request()->is('spa_menu_two') || request()->is('packages_activities_two') || request()->is('safety_onboard_two')): ?>
        <link href="<?php echo e(asset('website')); ?>/assets/css/pack_variation_two.css" rel="stylesheet" type="text/css" />
        <?php elseif(request()->is('itinerary_overview_three') || request()->is('package_trip_detail_three') || request()->is('crew_members_three') || request()->is('trips_specifications_three') || request()->is('menu_plan_three') || request()->is('spa_menu_three') || request()->is('packages_activities_three') || request()->is('safety_onboard_three')): ?>
        <link href="<?php echo e(asset('website')); ?>/assets/css/pack_variation_three.css" rel="stylesheet" type="text/css" />
		<?php elseif(request()->is('itinerary_overview_four') || request()->is('package_trip_detail_four') || request()->is('crew_members_four') || request()->is('trips_specifications_four') || request()->is('menu_plan_four') || request()->is('spa_menu_four') || request()->is('packages_activities_four') || request()->is('safety_onboard_four')): ?>
        <link href="<?php echo e(asset('website')); ?>/assets/css/pack_variation_four.css" rel="stylesheet" type="text/css" />
        <?php elseif(request()->is('itinerary_overview_five') || request()->is('package_trip_detail_five') || request()->is('crew_members_five') || request()->is('trips_specifications_five') || request()->is('menu_plan_five') || request()->is('spa_menu_five') || request()->is('packages_activities_five') || request()->is('safety_onboard_five')): ?>
        <link href="<?php echo e(asset('website')); ?>/assets/css/pack_variation_five.css" rel="stylesheet" type="text/css" />
    <?php else: ?>
        <link href="<?php echo e(asset('website')); ?>/assets/css/style.css" rel="stylesheet" type="text/css" />
        <link href="<?php echo e(asset('website')); ?>/assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <?php endif; ?>

    <?php echo $__env->yieldPushContent('css'); ?>
    <!--end::Global Stylesheets Bundle-->
    <script>
        // Frame-busting to prevent site from being loaded within a frame without permission (click-jacking) if (window.top != window.self) { window.top.location.replace(window.self.location.href); }
    </script>

</head>
<!--end::Head-->
<!--begin::Body-->

<body id="kt_body" data-bs-spy="scroll" data-bs-target="#kt_landing_menu" class="bg-body position-relative app-blank">
    <!--begin::Theme mode setup on page load-->
    <script>
        var defaultThemeMode = "light";
        var themeMode;
        if (document.documentElement) {
            if (document.documentElement.hasAttribute("data-bs-theme-mode")) {
                themeMode = document.documentElement.getAttribute("data-bs-theme-mode");
            } else {
                if (localStorage.getItem("data-bs-theme") !== null) {
                    themeMode = localStorage.getItem("data-bs-theme");
                } else {
                    themeMode = defaultThemeMode;
                }
            }
            if (themeMode === "system") {
                themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
            }
            document.documentElement.setAttribute("data-bs-theme", themeMode);
        }
    </script>
    <!--end::Theme mode setup on page load-->
    <!--begin::Root-->
    <div class="" id="kt_app_root">
        <!--begin::Header Section-->
        
        <!--begin::Wrapper-->
        




        <!--begin::Header-->
        
        <?php if(request()->is('/') ||
                request()->is('about') ||
                request()->is('services') ||
                request()->is('contact_us') ||
                request()->is('pricing')): ?>
            <nav class="navbar navbar-expand-lg navbar_header_sec ">
                <div class="container custom_container_fluid_service_ph">
                    <a class="navbar-brand" href="<?php echo e(url('/')); ?>">
                        <img alt="Logo" src="<?php echo e(asset('website')); ?>/assets/images/header_logo.svg"
                            class="logo-default" />
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                        data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse header_menus_wrapper" id="navbarSupportedContent">
                        <ul class="navbar-nav ">
                            <li class="nav-item <?php if(request()->is('/')): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('/')); ?>">HOME</a>
                            </li>
							<li class="nav-item <?php if(request()->route()->getName() == "about"): ?> active <?php endif; ?>">
                                <a class="nav-link " href="about">ABOUT</a>
                            </li>
							<li class="nav-item <?php if(request()->route()->getName() == "services"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('services')); ?>">SERVICES</a>
                            </li>
							<li class="nav-item <?php if(request()->route()->getName() == "contact_us"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('contact_us')); ?>">CONTACT US</a>
                            </li>
							<li class="nav-item <?php if(request()->route()->getName() == "pricing"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('pricing')); ?>">PRICING</a>
                            </li>

                        </ul>
                        <?php if(Auth::user()): ?>
                            <div class="login_logout_header login_logout_header_hamburger ">
                                
                                <a href="<?php echo e(url('logout')); ?>"
                                    class="btn_global yellow_btn arrow_up_right_btn_img">Logout <img
                                        src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></a>
                            </div>
                        <?php else: ?>
                            <div class="login_logout_header login_logout_header_hamburger">
                                
                                <a href="<?php echo e(url('login')); ?>"
                                    class="btn_global yellow_btn arrow_up_right_btn_img">Login <img
                                        src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></a>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    

                </div>
            </nav>
            
            <?php elseif(request()->is('itinerary_overview_five') || request()->is('package_trip_detail_five') || request()->is('crew_members_five') || request()->is('trips_specifications_five') || request()->is('menu_plan_five') || request()->is('spa_menu_five') || request()->is('packages_activities_five') || request()->is('safety_onboard_five')): ?>
            <nav class="navbar navbar-expand-lg packages_navbar">
                <div class="container custom_container">
                    <button class="navbar-toggler" type="button" data-toggle="collapse"
                        data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse custom_navbar_list" id="navbarSupportedContent">
                        <ul class="navbar-nav ">
                            <li class="nav-item <?php if(request()->is('itinerary_overview_five')): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('itinerary_overview_five')); ?>">Itinerary
                                    Overview</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "package_trip_detail_five"): ?> active <?php endif; ?>">
                                <a class="nav-link " href="<?php echo e(url('package_trip_detail_five')); ?>">Trip Details</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "crew_members_five"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('crew_members_five')); ?>">Crew</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "menu_plan_five"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('menu_plan_five')); ?>">Menu Plan</a>
                            </li>
								<li class="nav-item <?php if(request()->route()->getName() == "spa_menu_five"): ?> active <?php endif; ?>">
									<a class="nav-link" href="<?php echo e(url('spa_menu_five')); ?>">SPA Menu</a>
								</li>
                                <li class="nav-item <?php if(request()->route()->getName() == "packages_activities_five"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('packages_activities_five')); ?>">Activities</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "trips_specifications_five"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('trips_specifications_five')); ?>">Specifications</a>
                                </li>
								<li class="nav-item <?php if(request()->route()->getName() == "safety_onboard_five"): ?> active <?php endif; ?>">
									<a class="nav-link" href="<?php echo e(url('safety_onboard_five')); ?>">Safety OnBoard</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
            <?php elseif(request()->is('itinerary_overview_two') || request()->is('package_trip_detail_two') || request()->is('crew_members_two') || request()->is('trips_specifications_two') || request()->is('menu_plan_two') || request()->is('spa_menu_two') || request()->is('packages_activities_two') || request()->is('safety_onboard_two')): ?>
            <nav class="navbar navbar-expand-lg packages_navbar">
                <div class="container custom_container">
                    <button class="navbar-toggler" type="button" data-toggle="collapse"
                        data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse custom_navbar_list" id="navbarSupportedContent">
                        <ul class="navbar-nav ">
                            <li class="nav-item <?php if(request()->is('itinerary_overview_two')): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('itinerary_overview_two')); ?>">Itinerary Overview</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "package_trip_detail_two"): ?> active <?php endif; ?>">
                                <a class="nav-link " href="<?php echo e(url('package_trip_detail_two')); ?>">Trip Details</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "crew_members_two"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('crew_members_two')); ?>">Crew</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "menu_plan_two"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('menu_plan_two')); ?>">Menu Plan</a>
                            </li>
								<li class="nav-item <?php if(request()->route()->getName() == "spa_menu_two"): ?> active <?php endif; ?>">
									<a class="nav-link" href="<?php echo e(url('spa_menu_two')); ?>">SPA Menu</a>
								</li>
                                <li class="nav-item <?php if(request()->route()->getName() == "packages_activities_two"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('packages_activities_two')); ?>">Activities</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "trips_specifications_two"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('trips_specifications_two')); ?>">Specifications</a>
                            </li>
								<li class="nav-item <?php if(request()->route()->getName() == "safety_onboard_two"): ?> active <?php endif; ?>">
									<a class="nav-link" href="<?php echo e(url('safety_onboard_two')); ?>">Safety OnBoard</a>
								</li>
                        </ul>
                    </div>
                </div>
            </nav>
            <?php elseif(request()->is('itinerary_overview_three') || request()->is('package_trip_detail_three') || request()->is('crew_members_three') || request()->is('trips_specifications_three') || request()->is('menu_plan_three') || request()->is('spa_menu_three') || request()->is('packages_activities_three') || request()->is('safety_onboard_three')): ?>
            <nav class="navbar navbar-expand-lg packages_navbar">
                <div class="container custom_container">
                    <button class="navbar-toggler" type="button" data-toggle="collapse"
                        data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse custom_navbar_list" id="navbarSupportedContent">
                        <ul class="navbar-nav ">
                            <li class="nav-item <?php if(request()->is('itinerary_overview_three')): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('itinerary_overview_three')); ?>">Itinerary
                                    Overview</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "package_trip_detail_three"): ?> active <?php endif; ?>">
                                <a class="nav-link " href="<?php echo e(url('package_trip_detail_three')); ?>">Trip Details</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "crew_members_three"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('crew_members_three')); ?>">Crew</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "menu_plan_three"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('menu_plan_three')); ?>">Menu Plan</a>
                            </li>
								<li class="nav-item <?php if(request()->route()->getName() == "spa_menu_three"): ?> active <?php endif; ?>">
									<a class="nav-link" href="<?php echo e(url('spa_menu_three')); ?>">SPA Menu</a>
								</li>
                                <li class="nav-item <?php if(request()->route()->getName() == "packages_activities_three"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('packages_activities_three')); ?>">Activities</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "trips_specifications_three"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('trips_specifications_three')); ?>">Specifications</a>
                                </li>
								<li class="nav-item <?php if(request()->route()->getName() == "safety_onboard_three"): ?> active <?php endif; ?>">
									<a class="nav-link" href="<?php echo e(url('safety_onboard_three')); ?>">Safety OnBoard</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
            <?php elseif(request()->is('itinerary_overview_four') || request()->is('package_trip_detail_four') || request()->is('crew_members_four') || request()->is('trips_specifications_four') || request()->is('menu_plan_four') || request()->is('spa_menu_four') || request()->is('packages_activities_four') || request()->is('safety_onboard_four')): ?>
            <nav class="navbar navbar-expand-lg packages_navbar">
                <div class="container custom_container">
                    <button class="navbar-toggler" type="button" data-toggle="collapse"
                        data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse custom_navbar_list" id="navbarSupportedContent">
                        <ul class="navbar-nav ">
                            <li class="nav-item <?php if(request()->is('itinerary_overview_four')): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('itinerary_overview_four')); ?>">Itinerary
                                    Overview</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "package_trip_detail_four"): ?> active <?php endif; ?>">
                                <a class="nav-link " href="<?php echo e(url('package_trip_detail_four')); ?>">Trip Details</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "crew_members_four"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('crew_members_four')); ?>">Crew</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "menu_plan_four"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('menu_plan_four')); ?>">Menu Plan</a>
                            </li>
								<li class="nav-item <?php if(request()->route()->getName() == "spa_menu_four"): ?> active <?php endif; ?>">
									<a class="nav-link" href="<?php echo e(url('spa_menu_four')); ?>">SPA Menu</a>
								</li>
                                <li class="nav-item <?php if(request()->route()->getName() == "packages_activities_four"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('packages_activities_four')); ?>">Activities</a>
                            </li>
                                <li class="nav-item <?php if(request()->route()->getName() == "trips_specifications_four"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('trips_specifications_four')); ?>">Specifications</a>
                                </li>
								<li class="nav-item <?php if(request()->route()->getName() == "safety_onboard_four"): ?> active <?php endif; ?>">
									<a class="nav-link" href="<?php echo e(url('safety_onboard_four')); ?>">Safety OnBoard</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        <?php else: ?>
            <nav class="navbar navbar-expand-lg packages_navbar">
                <div class="container custom_container">
                    <button class="navbar-toggler" type="button" data-toggle="collapse"
                        data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse custom_navbar_list" id="navbarSupportedContent">
                        <ul class="navbar-nav ">
                            <li class="nav-item <?php if(request()->is('/itinerary_overview')): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('/itinerary_overview')); ?>">Itinerary Overview</a>
                            </li>
							<li class="nav-item <?php if(request()->route()->getName() == "package_trip_detail"): ?> active <?php endif; ?>">
                                <a class="nav-link " href="<?php echo e(url('package_trip_detail')); ?>">Trip Details</a>
                            </li>
							<li class="nav-item <?php if(request()->route()->getName() == "crew_members"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('crew_members')); ?>">Crew</a>
                            </li>
							<li class="nav-item <?php if(request()->route()->getName() == "menu_plan"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('menu_plan')); ?>">Menu Plan</a>
                            </li>
							<li class="nav-item <?php if(request()->route()->getName() == "spa_menu"): ?> active <?php endif; ?>">
								<a class="nav-link" href="<?php echo e(url('spa_menu')); ?>">SPA Menu</a>
							</li>
							<li class="nav-item <?php if(request()->route()->getName() == "packages_activities"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('packages_activities')); ?>">Activities</a>
                            </li>
							<li class="nav-item <?php if(request()->route()->getName() == "trips_specifications"): ?> active <?php endif; ?>">
                                <a class="nav-link" href="<?php echo e(url('trips_specifications')); ?>">Specifications</a>
							</li>
							<li class="nav-item <?php if(request()->route()->getName() == "safety_onboard"): ?> active <?php endif; ?>">
								<a class="nav-link" href="<?php echo e(url('safety_onboard')); ?>">Safety OnBoard</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        <?php endif; ?>
        

        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        <!--end::Header-->
        <!--begin::Landing hero-->
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        <!--end::Landing hero-->
        
        <!--end::Wrapper-->
        <!--begin::Curve bottom-->
        
        
        
        
        
        <!--end::Curve bottom-->
        
        <?php echo $__env->yieldContent('content'); ?>
        
        <!--begin::Curve top-->
        
        
        
        
        
        <!--end::Curve top-->
        <!--begin::Wrapper-->
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        
        <!--end::Wrapper-->
        
        <!--end::Footer Section-->
        <?php if(request()->is('/') ||
                request()->is('about') ||
                request()->is('services') ||
                request()->is('contact_us') ||
                request()->is('pricing')): ?>
            <footer>
                <section class="footer_sec">
                    <div class="container-fluid custom_container_footer">
                        <div class="row custom_footer_first_row">
                            <div class="col-md-12 custom_footer_parent_col">
                                <div class="footer_video">
                                    <video autoplay muted loop>
                                        <source src="<?php echo e(asset('website')); ?>/assets/images/footer_video.mp4"
                                            type="video/mp4">
                                    </video>
                                    <div class="footer_wrapper">
                                        <div class="background_content">
                                            <div class="row custom_row_footer">
                                                <div class="col-md-3 col-sm-12">
                                                    <div class="logo_details_sec">
                                                        <a href="<?php echo e(url('/')); ?>">
                                                            <div class="footer_logo">
                                                                <img alt="Logo"
                                                                    src="<?php echo e(asset('website')); ?>/assets/images/header_logo.svg"
                                                                    class="logo-default" />
                                                            </div>
                                                        </a>

                                                        <h6>Experience personalized yacht services designed to meet your
                                                            unique needs. From planning to maintenance.</h6>
                                                    </div>
                                                </div>
                                                <div class="col-md-7 col-sm-12">
                                                    <div class="footer_menus_wrapper" id="">
                                                        <ul class="navbar-nav">
                                                            <li
                                                                class="nav-item <?php if(request()->is('/')): ?> active <?php endif; ?>">
                                                                <a class="nav-link"
                                                                    href="<?php echo e(url('/')); ?>">Home</a>
                                                            </li>
															<li class="nav-item <?php if(request()->route()->getName() == "about"): ?> active <?php endif; ?>">
                                                                <a class="nav-link"
                                                                    href="<?php echo e(url('about')); ?>">ABOUT</a>
                                                            </li>
															<li class="nav-item <?php if(request()->route()->getName() == "services"): ?> active <?php endif; ?>">
                                                                <a class="nav-link"
                                                                    href="<?php echo e(url('services')); ?>">SERVICES</a>
                                                            </li>
															<li class="nav-item <?php if(request()->route()->getName() == "contact_us"): ?> active <?php endif; ?>">
                                                                <a class="nav-link"
                                                                    href="<?php echo e(url('contact_us')); ?>">CONTACT US</a>
                                                            </li>
															<li class="nav-item <?php if(request()->route()->getName() == "pricing"): ?> active <?php endif; ?>">
                                                                <a class="nav-link"
                                                                    href="<?php echo e(url('pricing')); ?>">PRICING</a>
                                                            </li>

                                                        </ul>
                                                    </div>
                                                </div>
                                                <div class="col-md-2 col-sm-12">
                                                    <div class="social_icons_footer">
                                                        <a href="javascript:void(0)"><i
                                                                class="fa-brands fa-linkedin"></i></a>
                                                        <a href="mailto:<EMAIL>"><i
                                                                class="fa-regular fa-envelope"></i></a>
                                                        <a href="tel:93992092-211"><i
                                                                class="fa-solid fa-phone"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="">
                                            <div class="copy_rights">
                                                <h6>©2024 GuestTrip. All Rights Reserved.</h6>
                                                <div class="privacy_terms_sec">
                                                    <a href="javascript:void(0)">
                                                        <h6>Privacy Policy</h6>
                                                    </a>
                                                    <a href="javascript:void(0)">
                                                        <h6>Terms & Condition</h6>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </footer>

            
        <?php else: ?>
            <footer class="footer_sec">
                <div class="container custom_container">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="custom_space_between copy_rights">
                                <h6>©2024 GuestTrip. All Rights Reserved.</h6>
                                <div class="custom_flex terms_privacy">
                                    <a href="javascript:void(0)">
                                        <h6>Privacy Policy</h6>
                                    </a>
                                    <a href="javascript:void(0)">
                                        <h6>Terms & Condition</h6>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        <?php endif; ?>
        


        <!--begin::Scrolltop-->
        
        
        
        
        
        
        <!--end::Scrolltop-->
    </div>
    <!--end::Root-->
    <!--begin::Scrolltop-->
    
    
    
    
    
    
    <!--end::Scrolltop-->
    <!--begin::Javascript-->
    <script>
        var hostUrl = "<?php echo e(asset('website')); ?>/assets/";
    </script>
    <!--begin::Global Javascript Bundle(mandatory for all pages)-->
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/js/scripts.bundle.js"></script>
    <!--end::Global Javascript Bundle-->
    <!--begin::Vendors Javascript(used for this page only)-->
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/custom/fslightbox/fslightbox.bundle.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/custom/typedjs/typedjs.bundle.js"></script>
    <!--end::Vendors Javascript-->
    <!--begin::Custom Javascript(used for this page only)-->
    <script src="<?php echo e(asset('website')); ?>/assets/js/custom/landing.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/js/custom/pages/pricing/general.js"></script>
    

    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <!-- Initialize Swiper -->
    <script>
        var swiper = new Swiper(".crewCardSwiper", {
            slidesPerView: 4,
            spaceBetween: 30,
            loop: true,
				autoplay: {
             delay: 3000,
             disableOnInteraction: false,
             pauseOnMouseEnter: true,
         },
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
        });

        var itenarySwiper = new Swiper(".itenarySwiper", {
            slidesPerView: 1,
            spaceBetween: 10,
            loop: true,
				autoplay: {
             delay: 3000,
             disableOnInteraction: false,
             pauseOnMouseEnter: true,
         },
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
            },
        });
    </script>
    <!--end::Custom Javascript-->
    <!--end::Javascript-->

    <?php echo $__env->yieldPushContent('js'); ?>

    

    
    
    
    
    

</body>
<!--end::Body-->

</html>
<?php /**PATH D:\guesttrip\resources\views\website\layout\masterOLD.blade.php ENDPATH**/ ?>