<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    
    <?php if(isset($trip->itineraries)): ?>
        <?php $__currentLoopData = $trip->itineraries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $itinerary): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <section
                class="hero_section trip_details_section trip_details_section_end specification_three <?php echo e($index == 0 ? 'margin_bottom_image' : 'margin_top_bottom_image'); ?>">
                <div class="section_inner_wrapper">
                    <div class="container custom_container">
                        <div class="row custom_row_align">
                            <?php if($index % 2 == 0): ?>
                                <div class="col-md-6">
                                    <div class="hero_content">
                                        <h1><?php echo e(str_pad($itinerary->duration, 2, '0', STR_PAD_LEFT)); ?><sub>Day</sub></h1>
                                        <h5><?php echo e($itinerary->date ?? ''); ?> <?php echo e($itinerary->time ?? ''); ?></h5>
                                        <h2><?php echo e($itinerary->start_point); ?> - <?php echo e($itinerary->end_point); ?></h2>
                                        <h5><?php echo e($itinerary->description); ?></h5>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="multi_images_section">
                                        <div class="row custom_row">
                                            <div class="col-md-7">
                                                <div class="custom_images">
                                                    <?php if(isset($itinerary->images) && isset($itinerary->images[0])): ?>
                                                        <img src="<?php echo e(asset('website/' . $itinerary->images[0]->image)); ?>"
                                                            alt="Image 1">
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="col-md-5">
                                                <div class="custom_images">
                                                    <?php if(isset($itinerary->images) && isset($itinerary->images[1])): ?>
                                                        <img src="<?php echo e(asset('website/' . $itinerary->images[1]->image)); ?>"
                                                            alt="Image 1">
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="swiper itenarySwiper">
                                                    <div class="swiper-wrapper">
                                                        <?php $__currentLoopData = $itinerary->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php if($key == 0 || $key == 1): ?>
                                                                <?php continue; ?>
                                                            <?php endif; ?>
                                                            <div class="swiper-slide">
                                                                <div class="slider_images">
                                                                    <img src="<?php echo e(asset('website/' . $image->image)); ?>">
                                                                </div>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                    <div class="swiper-pagination"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="row custom_row_align">
                                    <div class="col-md-6">
                                        <div class="multi_images_section">
                                            <div class="row custom_row">
                                                <div class="col-md-12">
                                                    <div class="swiper itenarySwiper">
                                                        <div class="swiper-wrapper">
                                                            <?php $__currentLoopData = $itinerary->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <?php if($key == 0 || $key == 1): ?>
                                                                    <?php continue; ?>
                                                                <?php endif; ?>
                                                                <div class="swiper-slide">
                                                                    <div class="slider_images">
                                                                        <img src="<?php echo e(asset('website/' . $image->image)); ?>">
                                                                    </div>
                                                                </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                        <div class="swiper-pagination"></div>
                                                    </div>
                                                </div>
                                                <div class="col-md-7">
                                                    <div class="custom_images">
                                                        <?php if(isset($itinerary->images) && isset($itinerary->images[0])): ?>
                                                            <img src="<?php echo e(asset('website/' . $itinerary->images[0]->image)); ?>" alt="Image 1">
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-5">
                                                    <div class="custom_images">
                                                        <?php if(isset($itinerary->images) && isset($itinerary->images[1])): ?>
                                                            <img src="<?php echo e(asset('website/' . $itinerary->images[1]->image)); ?>" alt="Image 1">
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-5">
                                        <div class="trip_contents">
                                            <h1><?php echo e(str_pad($itinerary->duration, 2, '0', STR_PAD_LEFT)); ?><sub>Day</sub></h1>
                                            <h5><?php echo e($itinerary->date ?? ''); ?> <?php echo e($itinerary->time ?? ''); ?></h5>
                                            <h2><?php echo e($itinerary->start_point); ?> - <?php echo e($itinerary->end_point); ?></h2>
                                            <h5><?php echo e($itinerary->description); ?></h5>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </section>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php endif; ?>

    
    
    
    
    <section class="image_fixed_sec" style="">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="image_fixed_wrapper" style="">
                        <img src="<?php echo e(asset('website')); ?>/assets/images/section_img.png"
                            style="">
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master_trip', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\website\packages_templates\VariationThree\package_trip_detail.blade.php ENDPATH**/ ?>