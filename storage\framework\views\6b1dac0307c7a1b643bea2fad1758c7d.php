
<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="trip_details_section">
        <div class="container custom_container">
            <div class="row custom_align_center">
                <div class="col-md-6">
                    <div class="multi_images_section">
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <div class="swiper itenarySwiper">
                                    <div class="swiper-wrapper">
                                        <?php if(isset($trip->boatSpecImages)): ?>
                                            <?php $__currentLoopData = $trip->boatSpecImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if($index >= 2): ?>
                                                    <div class="swiper-slide">
                                                        <div class="slider_images">
                                                            <img src="<?php echo e(asset('website/' . $image->image)); ?>"
                                                                alt="Image <?php echo e($index + 1); ?>">
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </div>
                                    <div class="swiper-pagination"></div>
                                </div>
                            </div>
                            <div class="col-md-7">
                                <div class="custom_images">
                                    <?php if(isset($trip->boatSpecImages) && isset($trip->boatSpecImages[0])): ?>
                                        <img src="<?php echo e(asset('website/' . $trip->boatSpecImages[0]->image)); ?>" alt="Image 1">
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div class="custom_images">
                                    <?php if(isset($trip->boatSpecImages) && isset($trip->boatSpecImages[1])): ?>
                                        <img src="<?php echo e(asset('website/' . $trip->boatSpecImages[1]->image)); ?>" alt="Image 2">
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="trip_contents">
                        <?php if(isset($trip->boatSpec)): ?>
                            <h6><?php echo e($trip->boatSpec->kicker ?? ''); ?></h6>
                            <h2><?php echo e($trip->boatSpec->heading ?? ''); ?></h2>
                            <h6><?php echo e($trip->boatSpec->sub_heading ?? ''); ?></h6>
                            <h5><?php echo e($trip->boatSpec->description ?? ''); ?></h5>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master_trip', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\website\packages_templates\VariationOne\trips_specifications.blade.php ENDPATH**/ ?>