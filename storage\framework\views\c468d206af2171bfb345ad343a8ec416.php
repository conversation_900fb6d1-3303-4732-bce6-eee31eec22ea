

<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <?php if(auth()->user()->hasRole('admin')): ?>
        <section class="payment_management">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="custom_table custom_cards_design">
                            <h1>Payment Management</h1>
                            <table class="table myTable datatable">
                                <thead>
                                    <tr>
                                        <th>Sr#</th>
                                        <th>Company Name</th>
                                        <th>Email</th>
                                        <th>Trip Name</th>
                                        <th>Subscription Type</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php for($i = 0; $i < 5; $i++): ?>
                                        <tr>
                                            <td>01</td>
                                            <td>Yacht IQ</td>
                                            <td><EMAIL></td>
                                            <td>Vacations</td>
                                            <td>Gold</td>
                                            <td>$ 200</td>
                                            <td>19/09/2024 </td>
                                            <td> <span class="success">Paid</span></td>
                                        </tr>
                                        <tr>
                                            <td>01</td>
                                            <td>Yacht IQ</td>
                                            <td><EMAIL></td>
                                            <td>Vacations</td>
                                            <td>Gold</td>
                                            <td>$ 200</td>
                                            <td>19/09/2024 </td>
                                            <td> <span class="danger">Rejected</span></td>
                                        </tr>
                                        <tr>
                                            <td>01</td>
                                            <td>Yacht IQ</td>
                                            <td><EMAIL></td>
                                            <td>Vacations</td>
                                            <td>Gold</td>
                                            <td>$ 200</td>
                                            <td>19/09/2024 </td>
                                            <td> <span class="warning">Pending</span></td>
                                        </tr>
                                    <?php endfor; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php elseif(auth()->user()->hasRole('owner')): ?>
        <section class="payment_management">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="custom_table custom_cards_design">
                            <div class="custom_justify_between">
                                <h1>Invoices Management</h1>
                                <button type="button" data-bs-toggle="modal" data-bs-target="#make_payment"
                                    class="btn btn_dark_green">Make Payment</button>
                            </div>
                            <table class="table myTable datatable">
                                <thead>
                                    <tr>
                                        <th>Sr#</th>
                                        <th>Trip Name</th>
                                        <th>Subscription Type</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php for($i = 0; $i < 10; $i++): ?>
                                        <tr>
                                            <td>01</td>
                                            <td>Vacations</td>
                                            <td>Gold</td>
                                            <td>$ 200</td>
                                            <td>19/09/2024 </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="dropdown-toggle" type="button" id="dropdownMenuButton11"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                        <li><a href="#!" class="dropdown-item"><i
                                                                    class="fa-solid fa-eye"></i>View</a></li>
                                                        <li><a href="#!" class="dropdown-item"><i
                                                                    class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                        <li><a href="#!" class="dropdown-item"><i
                                                                    class="fa-solid fa-trash"></i>Deactivate</a></li>
                                                        <li><a href="#!" class="dropdown-item download_invoice"><i
                                                                    class="fa-solid fa-download"></i>Download Invoice</a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endfor; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    

    <div class="modal fade" id="make_payment" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title" id="createModalLabel">Make Payment</h1>
                    
                </div>
                <div class="modal-body ">
                    <form>
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Subscription name:</label>
                                    <input type="text" class="form-control" value="Gold" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="txt_field">
                                    <label>Payment Type:</label>
                                    <input type="text" class="form-control" value="Monthly" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="txt_field">
                                    <label>Amount:</label>
                                    <input type="number" class="form-control" value="500.00" required>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="custom_check_box">
                                    <input class="form-check-input" type="checkbox" value="" id="Check">
                                    <label for="Check">Use Saved Card</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <button type="submit" class="btn btn_dark_green">Pay</button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn_transparent" data-bs-dismiss="modal">Cancel</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\PaymentManagement\payment_index.blade.php ENDPATH**/ ?>