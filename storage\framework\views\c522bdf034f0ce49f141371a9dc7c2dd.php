<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="payment_management">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_table custom_cards_design">
                        <h1>Payment Management</h1>
                        <table class="table without_pagination_tbl datatable">
                            <thead>
                            <tr>
                                <th>Sr#</th>
                                <th>Company Name</th>
                                <th>Email</th>
                                <th>Trip Name</th>
                                <th>Subscription Type</th>
                                <th>Amount</th>
                                <th>Date</th>
                                <th>Status</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php for($i=0;$i<5 ;$i++): ?>
                                <tr>
                                    <td>01</td>
                                    <td>Yacht IQ</td>
                                    <td><EMAIL></td>
                                    <td>Vacations</td>
                                    <td>Gold</td>
                                   <td>$ 200</td>
                                    <td>19/09/2024 </td>
                                    <td> <span class="success">Paid</span></td>
                                </tr>
                                <tr>
                                    <td>01</td>
                                    <td>Yacht IQ</td>
                                    <td><EMAIL></td>
                                    <td>Vacations</td>
                                    <td>Gold</td>
                                    <td>$ 200</td>
                                    <td>19/09/2024 </td>
                                    <td> <span class="danger">Rejected</span></td>
                                </tr>
                                <tr>
                                    <td>01</td>
                                    <td>Yacht IQ</td>
                                    <td><EMAIL></td>
                                    <td>Vacations</td>
                                    <td>Gold</td>
                                    <td>$ 200</td>
                                    <td>19/09/2024 </td>
                                    <td> <span class="warning">Pending</span></td>
                                </tr>
                            <?php endfor; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>


<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\Admin\payment_management.blade.php ENDPATH**/ ?>