<!DOCTYPE html>
<html lang="en">
<!--begin::Head-->

<head>
    <base href="" />
    <title>{{ App\Models\Setting::first()->title ?? '' }}</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="" />
    <meta property="og:url" content="https://keenthemes.com/metronic" />
    <meta property="og:site_name" content="Keenthemes | Metronic" />
    <link rel="canonical" href="https://preview.keenthemes.com/metronic8" />
    <link rel="shortcut icon" href="{{ asset('') }}{{ App\Models\Setting::first()->favicon ?? '' }}" />
    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <!--end::Fonts-->
    <link href="assets/plugins/custom/datatables/datatables.bundle.css" rel="stylesheet" type="text/css" />
    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="{{ asset('website') }}/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <!--end::Global Stylesheets Bundle-->
    <link href="{{ asset('Dashboard') }}/Css/dashboard.css" rel="stylesheet" />
    <link href="{{asset('Dashboard')}}/Css/dashboard-responsive.css" rel="stylesheet" />
    <style type="text/css">
        #loading {
            width: 100%;
            height: 100%;
            position: fixed;
            z-index: 9999;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
        }
    </style>
    @stack('css')
</head>
<!--end::Head-->
<!--begin::Body-->

<body id="kt_app_body" data-kt-app-layout="dark-sidebar" data-kt-app-header-fixed="true"
    data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-hoverable="true"
    data-kt-app-sidebar-push-header="true" data-kt-app-sidebar-push-toolbar="true"
    data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true" class="app-default">
    <!--begin::Theme mode setup on page load-->
    <script>
        var defaultThemeMode = "light";
        var themeMode;
        if (document.documentElement) {
            if (document.documentElement.hasAttribute("data-bs-theme-mode")) {
                themeMode = document.documentElement.getAttribute("data-bs-theme-mode");
            } else {
                if (localStorage.getItem("data-bs-theme") !== null) {
                    themeMode = localStorage.getItem("data-bs-theme");
                } else {
                    themeMode = defaultThemeMode;
                }
            }
            if (themeMode === "system") {
                themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
            }
            document.documentElement.setAttribute("data-bs-theme", themeMode);
        }
    </script>
    <!--end::Theme mode setup on page load-->
    <!--begin::App-->
    <div class="d-flex flex-column flex-root app-root" id="kt_app_root">
        <!--begin::Page-->
        <div class="app-page flex-column flex-column-fluid" id="kt_app_page">
            <!--begin::Header-->
            <div id="kt_app_header" class="app-header custom_header" data-kt-sticky="true"
                data-kt-sticky-activate="{default: true, lg: true}" data-kt-sticky-name="app-header-minimize"
                data-kt-sticky-offset="{default: '200px', lg: '0'}" data-kt-sticky-animation="false">
                <!--begin::Header container-->
                <div class="app-container container-fluid d-flex align-items-stretch justify-content-between"
                    id="kt_app_header_container">
                    <!--begin::Sidebar mobile toggle-->
                    <div class="d-flex align-items-center d-lg-none ms-n3 me-1 me-md-2" title="Show sidebar menu">
                        <div class="btn btn-icon btn-active-color-primary w-35px h-35px"
                            id="kt_app_sidebar_mobile_toggle">
                            <i class="ki-duotone ki-abstract-14 fs-2 fs-md-1">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </div>
                    </div>
                    <!--end::Sidebar mobile toggle-->
                    <!--begin::Mobile logo-->
{{--                    <div class="d-flex align-items-center flex-grow-1 flex-lg-grow-0">--}}
{{--                        <a href="../../demo1/dist/index.html" class="d-lg-none">--}}
{{--                            <img alt="Logo" src="{{ asset('website') }}/assets/media/logos/default-small.svg"--}}
{{--                                class="h-30px" />--}}
{{--                        </a>--}}
{{--                    </div>--}}
                    <!--end::Mobile logo-->
                    <!--begin::Header wrapper-->
                    <div class="d-flex align-items-stretch justify-content-between flex-lg-grow-1 main_header"
                        id="kt_app_header_wrapper">
                        @yield('navbar-title')
                        <!--begin::Menu wrapper-->
                        @include('theme.layout.navbar')
                        <!--end::Menu wrapper-->
                        <!--begin::Navbar-->
                        @include('theme.layout.right_sidebar')
                        <!--end::Navbar-->
                        @include('trips.trip_modals')
                    </div>
                    <!--end::Header wrapper-->
                </div>
                <!--end::Header container-->
            </div>
            <!--end::Header-->
            <!--begin::Wrapper-->

            <div id="loading">
                <div class="loader"></div>
            </div>
            <div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
                @include('theme.layout.sidebar')
                <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
                    <div class="d-flex flex-column flex-column-fluid">
                        @yield('breadcrumb')

                        @yield('content')
                    </div>
                    @if (auth()->user()->hasRole('developer'))
                        <div id="kt_app_footer" class="app-footer">
                            <div
                                class="app-container container-fluid d-flex flex-column flex-md-row flex-center flex-md-stack py-3">
                                <div class="text-dark order-2 order-md-1">
                                    <span
                                        class="text-muted fw-semibold me-1">{{ App\Models\Setting::first()->footer_text ?? '' }}&copy;</span>
                                    <a href="https://keenthemes.com" target="_blank"
                                        class="text-gray-800 text-hover-primary">Admin</a>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!--end::Wrapper-->
        </div>
        <!--end::Page-->
    </div>
    <div id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true">
        <i class="ki-duotone ki-arrow-up">
            <span class="path1"></span>
            <span class="path2"></span>
        </i>
    </div>
    <!--end::Scrolltop-->
    @include('theme.layout.modal')
    <!--begin::Javascript-->
    
    <script>
        var hostUrl = "{{ asset('website') }}/assets/";
    </script>
    <!--begin::Global Javascript Bundle(mandatory for all pages)-->
    <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script>
    <script src="{{ asset('website') }}/assets/js/scripts.bundle.js"></script>
    <script src="{{ asset('website') }}/assets/plugins/custom/datatables/datatables.bundle.js"></script>
    <!--end::Global Javascript Bundle-->

    <!--end::Javascript-->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script type="text/javascript">
        @if (session()->has('message'))
            Swal.fire({
                title: "{{ session()->get('title') ?? 'success!' }}",
                html: "{{ @ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('message'))) }}",
                icon: "{{ session()->get('type') ?? 'success' }}",
                timer: 5000,
                buttons: false,
            });
        @endif
        @if (session()->has('flash_message'))
            Swal.fire({
                title: "{{ @ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('flash_message'))) }}",
                icon: "{{ session()->get('type') ?? 'success' }}",
                timer: 5000,
                buttons: false,
            });
        @endif
        //delete button confirm swal dynamic.

        function showDeleteConfirmation(url, subSection, successMessage = "Deleted Successfully!!") {
            Swal.fire({
                title: 'Are you Sure?',
                text: 'You will not be Able to Recover this Sub Section.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, Delete It',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: url,
                        type: 'DELETE',
                        data: {
                            _token: '{{ csrf_token() }}',
                        },
                        success: function(response) {
                            if (response.success) {
                                subSection.remove();

                                Swal.fire({
                                    title: "Deleted!",
                                    text: successMessage,
                                    icon: "success",
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                            } else {
                                Swal.fire({
                                    title: "Error!",
                                    text: "Failed to delete subsection.",
                                    icon: "error",
                                });
                            }
                        },
                        error: function(xhr) {
                            console.log(xhr.responseText);

                            Swal.fire({
                                title: "Error!",
                                text: "An error occurred while deleting the subsection.",
                                icon: "error"
                            });
                        }
                    });
                }
            });
        }

        $(function() {
            $('[data-toggle="tooltip"]').tooltip()
        })
    </script>

    <script>
        $(window).on('load', function() {
            const $sidebar = $('#kt_app_sidebar');
            const screenWidth = $(window).width();

            if (screenWidth <= 991) {
                $sidebar.addClass('drawer drawer-start');
            } else {
                $sidebar.removeClass('drawer drawer-start');
            }
        });
        $(document).ready(function() {
            var dataTable = $('.myTable').DataTable({
                "searching": true,
                "bLengthChange": false,
                "paging": true,
                "info": true,
            });
            $(document).on("input", '.custom_search_box', function() {
                var searchValue = $(this).val();
                dataTable.search(searchValue).draw();
            });
            $(".myTable").DataTable();
        })
    </script>

    {{-- Pages Script --}}
    <script>
        $(document).ready(function() {

            //             Profile Editable Button Hide & show Jquery
            $(".profile_changes .edit_profile").click(function() {
                var allInputField = $(this).closest("form").find(".myinput");
                $(allInputField).each(function() {
                    var input = $(this);
                    input.removeAttr("readonly", !input.attr("readonly"));
                    if (input.attr("readonly")) {}
                });
            });

            $(".profile_changes .save_cancel_btn").hide();
            $(".profile_changes .edit_profile").on("click", function() {
                $(".profile_changes .edit_change").hide();
                $(".profile_changes .save_cancel_btn").show();
            });

            $(".save_cancel_btn .btn_transparent").on("click", function() {
                $(".profile_changes .save_cancel_btn").hide();
                $(".profile_changes .edit_change").show();
            });

            //            Click Cancel to field Readonly

            $(".profile_changes .cancel_edit").click(function() {
                var allInputField = $(this).closest("form").find(".myinput");
                $(allInputField).each(function() {
                    var input = $(this);
                    input.attr("readonly", !input.attr("readonly"));
                    if (input.attr("readonly")) {}
                });
            });

            //           File Uploading Jquery
            // When the file input changes, update the corresponding image preview
            $('.custom_file_input').on('change', function() {
                // Get the file input and its corresponding image
                var input = $(this);
                var img = input.closest('.image-input').find('.input_image_field');

                // Update the image source
                var file = this.files[0];
                if (file) {
                    img.attr('src', URL.createObjectURL(file));
                }
            });

            // When the cancel button is clicked, reset the image to its original source
            $('[data-kt-image-input-action="cancel"]').on('click', function() {
                // Get the corresponding image and reset the source
                var img = $(this).closest('.image-input').find('.input_image_field');
                var originalSrc = img.attr('data-original-src');
                img.attr('src', originalSrc);
            });
        });
    </script>
    <script type="text/javascript">
        document.onreadystatechange = function() {
            var state = document.readyState
            if (state == 'interactive') {

                document.getElementById('loading').style.visibility = "visible";
            } else if (state == 'complete') {

                setTimeout(function() {
                    document.getElementById('interactive');
                    document.getElementById('loading').style.visibility = "hidden";

                }, 1000);

            }

        }
    </script>
    @include('trips.trip_scripts')
    
    
    @stack('js')


</body>
<!--end::Body-->

</html>
