<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Itinerary extends Model
{
    use HasFactory;
    protected $fillable = [
        'trip_id',
        'number',
        'duration',
        'date',
        'start_point',
        'end_point',
        'start_lat',
        'start_lng',
        'end_lat',
        'end_lng',
        'time',
        'description',
        'night_stay'
    ];

    protected $casts = [
        'night_stay' => 'boolean',
    ];
    // Relationship with Trip (inverse)
    public function trip()
    {
        return $this->belongsTo(Trip::class);
    }
    public function images()
    {
        return $this->hasMany(ItineraryImage::class);
    }
}
