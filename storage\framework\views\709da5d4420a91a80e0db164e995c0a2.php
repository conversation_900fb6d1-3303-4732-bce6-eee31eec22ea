
<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>

    <section class="packages_activities custom_padding">
        <div class="container custom_container">
            <div class="row custom_align_center">
                <div class="col-md-6">
                    <div class="activities_content">
                        <?php if(isset($trip->activity)): ?>
                            <h6><?php echo e($trip->activity->kicker ?? ''); ?></h6>
                            <h2><?php echo e($trip->activity->heading ?? ''); ?></h2>
                            <h6><?php echo e($trip->activity->sub_heading ?? ''); ?></h6>
                            <?php if($trip->activity->texts != null): ?>
                                <div class="activity_listing">
                                    <ul>
                                        <?php $__currentLoopData = $trip->activity->texts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $text): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($text != null): ?>
                                                <li><i class="fa-solid fa-arrow-circle-right"></i><?php echo e($text); ?></li>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="multi_images_section">
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <div class="swiper itenarySwiper">
                                    <div class="swiper-wrapper">
                                        <?php if(isset($trip->activityImages)): ?>
                                            <?php $__currentLoopData = $trip->activityImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if($index >= 2): ?>
                                                    <div class="swiper-slide">
                                                        <div class="slider_images">
                                                            <img src="<?php echo e(asset('website/' . $image->image)); ?>"
                                                                alt="Image <?php echo e($index + 1); ?>">
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </div>
                                    <div class="swiper-pagination"></div>
                                </div>
                            </div>
                            <div class="col-md-7">
                                <div class="custom_images">
                                    <?php if(isset($trip->activityImages) && isset($trip->activityImages[0])): ?>
                                        <img src="<?php echo e(asset('website/' . $trip->activityImages[0]->image)); ?>" alt="Image 1">
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div class="custom_images">
                                    <?php if(isset($trip->activityImages) && isset($trip->activityImages[1])): ?>
                                        <img src="<?php echo e(asset('website/' . $trip->activityImages[1]->image)); ?>" alt="Image 2">
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>


<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master_trip', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\website\packages_templates\VariationFour\packages_activities.blade.php ENDPATH**/ ?>