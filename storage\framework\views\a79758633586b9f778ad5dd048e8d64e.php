
<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    
    <section class="hero_sec services_hero_sec">
        <div class="container custom_container">
            <div class="row">
                <div class="col-md-12">
                    <div class="hero_sec_wrapper">
                        <h1>Subscription</h1>
                        <h6>Enjoy the open waters without worrying about the details. From trip planning to logistics, our dedicated team takes care of everything so you can focus on the journey ahead.</h6>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="contact_us_sec">
        <div class="container-fluid custom_container_fluid_service_ph">
            <div class="row column_reverse">
                <div class="col-md-6 custom_col_contact_us">
                    <div class="get_in_touch_heading">
                        
                        <h2>Payment</h2>
                        
                    </div>
                    <form id="order_form" action="<?php echo e(route('processStripePayment')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="form_contact_us">
                            <div class="row form_contact_us_row">
                                <div class="col-md-12">
                                    <div class="field_wrapper">
                                        <label for="owner">Card Holder Name*</label>
                                        <input type="text" id="owner" class="form-control" name="owner"
                                            placeholder="Card Holder Name">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="field_wrapper">
                                        <label for="card-number-element">Card Number*</label>
                                        <div id="card-number-element"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="field_wrapper">
                                        <label for="card-expiry-element">Expiration Date*</label>
                                        <div id="card-expiry-element"></div>
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="field_wrapper">
                                        <label for="card-cvc-element">CVC*</label>
                                        <div id="card-cvc-element"></div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="">
                                        <button type="submit" id="stripe-submit"
                                            class="btn_global yellow_btn arrow_up_right_btn_img">Subscibe</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </form>
                </div>
                <div class="col-md-6 custom_col_contact_us">
                    <div class="image_contact_wrapper">
                        <img src="<?php echo e(asset('website')); ?>/assets/images/contact_us_img.png">
                    </div>
                    <div class="social_wrapper_contact_us">
                        <div class="phone_number">
                            <i class="fa-solid fa-phone"></i>
                            <div>
                                <h6>Phone Number</h6>
                                <a href="tel:+442086386370"><h2>+44 2086 386 370</h2></a>
                            </div>
                        </div>
                        <div class="phone_number">
                            <i class="fa-regular fa-envelope"></i>
                            <div>
                                <h6>Email Address</h6>
                                <a href="mailto:<EMAIL>"><h2><EMAIL></h2></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://js.stripe.com/v3/"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Stripe Initialization
            const stripe = Stripe("<?php echo e(env('STRIPE_KEY')); ?>"); // Replace with your Stripe publishable key
            const elements = stripe.elements();

            // Create card elements
            const cardNumberElement = elements.create('cardNumber', {});
            const cardExpiryElement = elements.create('cardExpiry', {});
            const cardCvcElement = elements.create('cardCvc', {});

            // Mount card elements
            cardNumberElement.mount('#card-number-element');
            cardExpiryElement.mount('#card-expiry-element');
            cardCvcElement.mount('#card-cvc-element');

            // Toggle between Stripe and PayPal
            document.getElementById('payment-method').addEventListener('change', function() {
                const selectedMethod = this.value;
                const stripeFields = document.getElementById('stripe-fields');
                const paypalFields = document.getElementById('paypal-fields');

                if (selectedMethod === 'stripe') {
                    stripeFields.style.display = 'block';
                    paypalFields.style.display = 'none';
                } else {
                    stripeFields.style.display = 'none';
                    paypalFields.style.display = 'block';
                }
            });

            // Stripe Form Submission
            const form = document.getElementById('order_form');
            let isSubmitting = false; // Flag to prevent duplicate submissions
            form.addEventListener('submit', async function(e) {
                if (isSubmitting) return; // If form is already submitting, prevent further actions

                isSubmitting = true; // Set flag to true to indicate form is in submission process
                let $error = true;
                e.preventDefault();

                const cardHolderName = document.getElementById('owner').value.trim();
                if (!cardHolderName) {
                    document.getElementById('card-errors').textContent =
                        'Card Holder Name is required.';
                    resetSubmission(); // Reset the flag
                    return;
                }

                // Generate Stripe token
                const {
                    error,
                    token
                } = await stripe.createToken(cardNumberElement, {
                    name: cardHolderName
                });
                if (error) {
                    document.getElementById('card-errors').textContent = error.message;
                    resetSubmission(); // Reset if there's an error
                    return;
                } else {
                    document.getElementById('card-errors').textContent = '';

                    // Attach token to form and submit
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'stripeToken';
                    hiddenInput.value = token.id;
                    form.appendChild(hiddenInput);
                    document.getElementById('card-errors').textContent = '';
                    // this.submit();
                }

                // Final form submission if no errors
                if ($error) {
                    document.getElementById('stripe-submit').disabled =
                        true; // Disable the submit button
                    form.submit();
                } else {
                    resetSubmission(); // Reset if there are validation errors
                }
            });

            function resetSubmission() {
                isSubmitting = false; // Reset the flag for re-attempts
                document.getElementById('stripe-submit').disabled = false; // Re-enable the button if needed
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\website\stripe_payment.blade.php ENDPATH**/ ?>