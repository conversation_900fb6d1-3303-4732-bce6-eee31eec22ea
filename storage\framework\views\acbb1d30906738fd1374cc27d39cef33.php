<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>


<?php $__env->startSection('content'); ?>
    
    <section class="profile_page">
        <div class="container-fluid custom_container">
            <div class="row custom_row">
                <div class="col-md-12">
                    <form method="POST" action="<?php echo e(route('update.profile')); ?>" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="custom_cards_design icon_image">
                            <div class="row custom_row">
                                <div class="col-md-12">
                                    <h2>Profile Settings</h2>
                                </div>
                                <div class="col-md-12">
                                    <div class="custom_justify_between">
                                        <div class="profile_image">
                                            <!--begin::Image input-->
                                            <div class="image-input image-input-circle" data-kt-image-input="true">
                                                <!--begin::Image preview wrapper-->
                                                <div class="image-input-wrapper">
                                                    <?php if($user->profile->pic != null): ?>
                                                        <img class="input_image_field"
                                                            src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($user->profile->pic); ?>"
                                                            data-original-src="<?php echo e(asset('storage/uploads/users')); ?>/<?php echo e($user->profile->pic); ?>">
                                                    <?php else: ?>
                                                        <img class="input_image_field"
                                                            src="<?php echo e(asset('website')); ?>/assets/images/buildings.png"
                                                            data-original-src="<?php echo e(asset('website')); ?>/assets/images/buildings.png">
                                                    <?php endif; ?>

                                                </div>
                                                <!--end::Image preview wrapper-->

                                                <!--begin::Edit button-->
                                                <label
                                                    class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                    data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                    data-bs-dismiss="click" title="Change avatar">
                                                    <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                            class="path2"></span></i>

                                                    <!--begin::Inputs-->
                                                    <input type="file" name="pic" accept=".png, .jpg, .jpeg"
                                                        class="myinput custom_file_input" readonly />
                                                    <input type="hidden" name="avatar_remove" />
                                                    <!--end::Inputs-->
                                                </label>
                                                <!--end::Edit button-->

                                                <!--begin::Cancel button-->
                                                <span
                                                    class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                    data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                    data-bs-dismiss="click" title="Cancel avatar">
                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                </span>
                                                <!--end::Cancel button-->
                                            </div>
                                        </div>
                                        <div class="profile_changes">
                                            <div class="edit_change custom_flex">
                                                <a class="btn btn_dark_green edit_profile">Edit</a>
                                                <a class="btn btn_dark_green" data-bs-toggle="modal"
                                                    data-bs-target="#changePassword">Change Password</a>
                                            </div>
                                            <div class="save_cancel_btn custom_flex">
                                                <button type="submit" class="btn btn_dark_green">Save changes</button>
                                                <button type="button"
                                                    class="btn btn_transparent cancel_edit">Cancel</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <h2>Personal Information</h2>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Name:</label>
                                        <input type="text" class="form-control myinput" name="name"
                                            value="<?php echo e($user->name ?? ''); ?>" placeholder="Name Here" readonly required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Company Name:</label>
                                        <input type="text" class="form-control myinput" name="company_name"
                                            value="<?php echo e($user->company_name ?? ''); ?>" placeholder="Name Here" readonly
                                            required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Email Address:</label>
                                        <input type="email" class="form-control" placeholder="<?php echo e($user->email ?? ''); ?>"
                                            readonly required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Phone Number:</label>
                                        <input type="tel" class="form-control myinput" name="phone"
                                            value="<?php echo e($user->profile->phone ?? ''); ?>" placeholder="Number Here" readonly
                                            required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
    
    <div class="modal fade modal_wrapper" id="changePassword" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title" id="exampleModalLabel1">Password Setting</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"><i
                            class="fa-solid fa-close"></i></button>
                </div>
                <div class="modal-body">
                    <form method="POST" action="<?php echo e(route('change.password')); ?>">
                        <?php echo csrf_field(); ?>
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label for="current_password" class="form-label">Current Password</label>
                                    <input type="password" class="form-control" id="current_password"
                                        name="current_password" required placeholder="Enter Current Password Here">
                                    <i class="fa-solid fa-eye eye_np input-icon custom_icon"></i>
                                    <i class="fa-solid fa-eye-slash eye_slash_np input-icon custom_icon"></i>
                                    <?php $__errorArgs = ['current_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label for="password" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="password" name="password" required
                                        placeholder="Enter New Password Here">
                                    <i class="fa-solid fa-eye eye_newpass input-icon-new-pass custom_icon"></i>
                                    <i class="fa-solid fa-eye-slash eye_slash_newpass input-icon-new-pass custom_icon"></i>
                                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label for="confirm-password" class="form-label">Confirm New Password</label>
                                    <input type="password" class="form-control" id="confirm-password"
                                        name="confirm-password" required placeholder="Enter Confirm Password Here">
                                    <i class="fa-solid fa-eye eye_cp input-icon-conf-pass custom_icon"></i>
                                    <i class="fa-solid fa-eye-slash eye_slash_cp input-icon-conf-pass custom_icon"></i>
                                    <?php $__errorArgs = ['confirm-password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="update_changes custom_flex">
                                    <button type="submit" class="btn btn_dark_green">Save changes</button>
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal"
                                        aria-label="Close">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    
    
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            $(".eye_np").hide();
            $(".input-icon").click(function() {
                var input = $(".pass_log_id");
                if (input.attr("type") === "password") {
                    input.attr("type", "text");
                    $(".eye_slash_np").hide();
                    $(".eye_np").show();
                } else {
                    input.attr("type", "password");
                    $(".eye_np").hide();
                    $(".eye_slash_np").show();
                }
            });
        })
    </script>
    <script>
        $(document).ready(function() {
            $(".eye_cp").hide();
            $(".input-icon-conf-pass").click(function() {
                var input = $(".pass_log_conf_pass");
                if (input.attr("type") === "password") {
                    input.attr("type", "text");
                    $(".eye_slash_cp").hide();
                    $(".eye_cp").show();
                } else {
                    input.attr("type", "password");
                    $(".eye_cp").hide();
                    $(".eye_slash_cp").show();
                }
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            $(".eye_newpass").hide();
            $(".input-icon-new-pass").click(function() {
                var input = $(".pass_log_new_id");
                if (input.attr("type") === "password") {
                    input.attr("type", "text");
                    $(".eye_slash_newpass").hide();
                    $(".eye_newpass").show();
                } else {
                    input.attr("type", "password");
                    $(".eye_newpass").hide();
                    $(".eye_slash_newpass").show();
                }
            });

            <?php if($errors->has('current_password') || $errors->has('password') || $errors->has('confirm-password')): ?>
                $('#changePassword').modal('show');
            <?php else: ?>
                // Clear modal fields only if there are no errors
                $('#changePassword').on('show.bs.modal', function() {
                    $('#current_password').val('');
                    $('#password').val('');
                    $('#confirm-password').val('');
                });
            <?php endif; ?>
        });
    </script>

    
    
    <script>
        // $(document).ready(function() {
        //     var cardNumber = $('.card_number_heading').text();
        //     // Format the card number with non-breaking spaces
        //     var formattedNumber = cardNumber.replace(/(\d{4})(?=\d)/g,
        //         '$1&nbsp;&nbsp;&nbsp;&nbsp;'); // Adds 4 non-breaking spaces after every 4 digits
        //     $('.card_number_heading').html(formattedNumber.trim()); // Update the text with formatted number
        // });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\dashboard_profile.blade.php ENDPATH**/ ?>