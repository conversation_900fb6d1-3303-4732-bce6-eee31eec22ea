<?php $__env->startPush('css'); ?>
<link href="assets/css/style.bundle.css" rel="stylesheet" type="text/css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"  />
<style>
.image-input-placeholder {
background-image: url("<?php echo e(asset('website/assets/media/avatars')); ?>/avatar.svg");
}


</style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
<?php $__env->startSection('breadcrumb'); ?>
<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
<div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
<h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0"><?php echo e(config('app.name')); ?></h1>
<ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
<li class="breadcrumb-item text-muted">
<a href="<?php echo e(url('home')); ?>" class="text-muted text-hover-primary">Home</a>
</li>
<li class="breadcrumb-item">
<span class="bullet bg-gray-400 w-5px h-2px"></span>
</li>
<li class="breadcrumb-item text-muted">subscriptions</li>
</ul>
</div>
</div>
</div>
<?php $__env->stopSection(); ?>
<div id="" class="app-content flex-column-fluid">
<div id="kt_app_content_container" class="app-container container-xxl">
<?php if($errors->any()): ?>
<div class="alert alert-danger">
<?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<?php echo e($error); ?> <br>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>
<?php endif; ?>
<div class="card">
<div class="card-body">
<form method="post" action="<?php echo e(route('subscriptions.update',$subscription->id)); ?>" class="form-horizontal" enctype="multipart/form-data">
<?php echo e(method_field('PATCH')); ?>

<?php echo e(csrf_field()); ?>

<div class="row g-7">




                <div class="col-md-12 fv-row">
                <label class="form-label">
                <span class="">User_id</span>
                </label>
                <input type="number" name="user_id"  class="form-control form-control-solid" placeholder="" value="<?php echo e($subscription->user_id??''); ?>" />
                </div>
                <div class="col-md-12 fv-row">
                <label class="form-label">
                <span class="">Stripe_customer_id</span>
                </label>
                <input type="text" name="stripe_customer_id"  class="form-control form-control-solid" placeholder="" value="<?php echo e($subscription->stripe_customer_id??''); ?>"/>
                </div>
                <div class="col-md-12 fv-row">
                <label class="form-label">
                <span class="">Stripe_subscription_id</span>
                </label>
                <input type="text" name="stripe_subscription_id"  class="form-control form-control-solid" placeholder="" value="<?php echo e($subscription->stripe_subscription_id??''); ?>"/>
                </div>
                <div class="col-md-12 fv-row">
                <label class="form-label">
                <span class="">Package_id</span>
                </label>
                <input type="number" name="package_id"  class="form-control form-control-solid" placeholder="" value="<?php echo e($subscription->package_id??''); ?>" />
                </div>
                <div class="col-md-12 fv-row">
                <label class="form-label">
                <span class="">Status</span>
                </label>
                <input type="text" name="status"  class="form-control form-control-solid" placeholder="" value="<?php echo e($subscription->status??''); ?>"/>
                </div>
                <div class="col-md-12 fv-row">
                <label class="form-label">
                <span class="">Started_at</span>
                </label>
                <input type="text" name="started_at"  class="form-control form-control-solid" placeholder="" value="<?php echo e($subscription->started_at??''); ?>"/>
                </div>
                <div class="col-md-12 fv-row">
                <label class="form-label">
                <span class="">Ended_at</span>
                </label>
                <input type="text" name="ended_at"  class="form-control form-control-solid" placeholder="" value="<?php echo e($subscription->ended_at??''); ?>"/>
                </div>
                <div class="col-md-12 fv-row">
                <label class="form-label">
                <span class="">No_of_trips</span>
                </label>
                <input type="number" name="no_of_trips"  class="form-control form-control-solid" placeholder="" value="<?php echo e($subscription->no_of_trips??''); ?>" />
                </div>

<div class="text-center">
<a href="<?php echo e(route('subscriptions.index')); ?>" id="kt_modal_new_target_cancel" class="btn btn-light me-3">Cancel</a>
<button type="submit" id="kt_modal_new_target_submit" class="btn btn-primary">
<span class="indicator-label">Update</span>
<span class="indicator-progress">Please wait...
<span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
</button>
</div>

</div>
</div>
</form>
</div>
</div>
</div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script src="<?php echo e(asset('website')); ?>/assets/js/scripts.bundle.js"></script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\subscriptions\edit.blade.php ENDPATH**/ ?>