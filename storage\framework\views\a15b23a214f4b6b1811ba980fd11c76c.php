<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <section class="card-name-main p-20">
        <div class="container-fluid">
            <div class="row">
                <div class="col-lg-12">
                    <div class="pb-10">
                        <img alt="Logo" src="<?php echo e(asset('website')); ?>/assets/images/MAHIYA Living (1).png">
                    </div>
                </div>
                <div class="col-lg-8">

                    
                    
                    <div class="card-number-main pt-20">

                        <!-- Payment Method Selection -->
                        <div class="form-group" style="display: none">
                            <label for="payment-method">Payment Method:</label>
                            <select id="payment-method" class="form-control">
                                <option value="stripe" selected>Stripe</option>
                                <option value="paypal">PayPal</option>
                            </select>
                        </div>

                        <!-- Stripe Payment Form -->
                        <div id="stripe-fields">
                            <form id="order_form" action="<?php echo e(route('processStripePayment')); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" id="amount" name="amount" value="1000">
                                <div class="form-group">
                                    <label class="fs-16 w-400 quarion-font black-class" for="owner">Card Holder Name*</label>
                                    <input type="text" id="owner" class="form-control" name="owner"
                                        placeholder="Card Holder Name">
                                    
                                </div>
                                <div class="form-group">
                                    <label class="fs-16 w-400 quarion-font black-class" for="card-number-element">Card Number*</label>
                                    <div id="card-number-element"></div>
                                    <hr style="border: 1px solid #000000; margin-top: 10px;">
                                </div>
                                <div class="form-row">
                                    <div class="col">
                                        <label class="fs-16 w-400 quarion-font black-class" for="card-expiry-element">Expiration Date*</label>
                                        <div id="card-expiry-element"></div>
                                        <hr style="border: 1px solid #000000; margin-top: 10px;">
                                    </div>
                                    <div class="col">
                                        <label class="fs-16 w-400 quarion-font black-class" for="card-cvc-element">CVC*</label>
                                        <div id="card-cvc-element"></div>
                                        <hr style="border: 1px solid #000000; margin-top: 10px;">
                                    </div>
                                </div>
                                <small id="card-errors" class="text-danger"></small>
                                <div class="pt-3">
                                    <button type="submit" id="stripe-submit"
                                        class="fs-16 w-400 quarion-font btn-search">Pay Now</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://js.stripe.com/v3/"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Stripe Initialization
            const stripe = Stripe("<?php echo e(env('STRIPE_KEY')); ?>"); // Replace with your Stripe publishable key
            const elements = stripe.elements();

            // Create card elements
            const cardNumberElement = elements.create('cardNumber', {});
            const cardExpiryElement = elements.create('cardExpiry', {});
            const cardCvcElement = elements.create('cardCvc', {});

            // Mount card elements
            cardNumberElement.mount('#card-number-element');
            cardExpiryElement.mount('#card-expiry-element');
            cardCvcElement.mount('#card-cvc-element');

            // Toggle between Stripe and PayPal
            document.getElementById('payment-method').addEventListener('change', function() {
                const selectedMethod = this.value;
                const stripeFields = document.getElementById('stripe-fields');
                const paypalFields = document.getElementById('paypal-fields');

                if (selectedMethod === 'stripe') {
                    stripeFields.style.display = 'block';
                    paypalFields.style.display = 'none';
                } else {
                    stripeFields.style.display = 'none';
                    paypalFields.style.display = 'block';
                }
            });

            // Stripe Form Submission
            const form = document.getElementById('order_form');
            let isSubmitting = false; // Flag to prevent duplicate submissions
            form.addEventListener('submit', async function(e) {
                if (isSubmitting) return; // If form is already submitting, prevent further actions

                isSubmitting = true; // Set flag to true to indicate form is in submission process
                let $error = true;
                e.preventDefault();

                const cardHolderName = document.getElementById('owner').value.trim();
                if (!cardHolderName) {
                    document.getElementById('card-errors').textContent =
                        'Card Holder Name is required.';
                    resetSubmission(); // Reset the flag
                    return;
                }

                // Generate Stripe token
                const {
                    error,
                    token
                } = await stripe.createToken(cardNumberElement, {
                    name: cardHolderName
                });
                if (error) {
                    document.getElementById('card-errors').textContent = error.message;
                    resetSubmission(); // Reset if there's an error
                    return;
                } else {
                    document.getElementById('card-errors').textContent = '';

                    // Attach token to form and submit
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'stripeToken';
                    hiddenInput.value = token.id;
                    form.appendChild(hiddenInput);
                    document.getElementById('card-errors').textContent = '';
                    // this.submit();
                }

                // Final form submission if no errors
                if ($error) {
                    document.getElementById('stripe-submit').disabled =
                        true; // Disable the submit button
                    form.submit();
                } else {
                    resetSubmission(); // Reset if there are validation errors
                }
            });

            function resetSubmission() {
                isSubmitting = false; // Reset the flag for re-attempts
                document.getElementById('stripe-submit').disabled = false; // Re-enable the button if needed
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\payment.blade.php ENDPATH**/ ?>