<!DOCTYPE html>

<html lang="en">
<!--begin::Head-->

<head>
    <base href="" />
    <title><PERSON> (Guest Trip) </title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="" />
    <meta property="og:url" content="" />
    <meta property="og:site_name" content="" />
    <link rel="canonical" href="" />
    <link rel="shortcut icon" href="<?php echo e(asset('website')); ?>/assets/images/header_logo.svg" />
    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />

    <!--end::Fonts-->
    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <link href="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" />


    <link href="<?php echo e(asset('website')); ?>/assets/css/style.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/responsive.css" rel="stylesheet" type="text/css" />

    <?php echo $__env->yieldPushContent('css'); ?>
    <!--end::Global Stylesheets Bundle-->
    <script>
        // Frame-busting to prevent site from being loaded within a frame without permission (click-jacking) if (window.top != window.self) { window.top.location.replace(window.self.location.href); }
    </script>

</head>
<!--end::Head-->
<!--begin::Body-->

<body id="kt_body" data-bs-spy="scroll" data-bs-target="#kt_landing_menu" class="bg-body position-relative app-blank">
    <!--begin::Theme mode setup on page load-->
    <script>
        var defaultThemeMode = "light";
        var themeMode;
        if (document.documentElement) {
            if (document.documentElement.hasAttribute("data-bs-theme-mode")) {
                themeMode = document.documentElement.getAttribute("data-bs-theme-mode");
            } else {
                if (localStorage.getItem("data-bs-theme") !== null) {
                    themeMode = localStorage.getItem("data-bs-theme");
                } else {
                    themeMode = defaultThemeMode;
                }
            }
            if (themeMode === "system") {
                themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
            }
            document.documentElement.setAttribute("data-bs-theme", themeMode);
        }
    </script>
    <!--end::Theme mode setup on page load-->
    <!--begin::Root-->
    <div class="" id="kt_app_root">
        <!--begin::Header Section-->
        <nav class="navbar navbar-expand-lg navbar_header_sec ">
            <div class="container custom_container_fluid_service_ph">
                <a class="navbar-brand" href="<?php echo e(url('/')); ?>">
                    <img alt="Logo" src="<?php echo e(asset('website')); ?>/assets/images/header_logo.svg"
                        class="logo-default" />
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                    data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                    aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse header_menus_wrapper" id="navbarSupportedContent">
                    <ul class="navbar-nav ">
                        <li class="nav-item <?php if(request()->is('/')): ?> active <?php endif; ?>">
                            <a class="nav-link" href="<?php echo e(url('/')); ?>">HOME</a>
                        </li>
                        <li class="nav-item <?php if(request()->route()->getName() == 'about'): ?> active <?php endif; ?>">
                            <a class="nav-link " href="about">ABOUT</a>
                        </li>
                        <li class="nav-item <?php if(request()->route()->getName() == 'services'): ?> active <?php endif; ?>">
                            <a class="nav-link" href="<?php echo e(url('services')); ?>">SERVICES</a>
                        </li>
                        <li class="nav-item <?php if(request()->route()->getName() == 'contact_us'): ?> active <?php endif; ?>">
                            <a class="nav-link" href="<?php echo e(url('contact_us')); ?>">CONTACT US</a>
                        </li>
                        <li class="nav-item <?php if(request()->route()->getName() == 'pricing'): ?> active <?php endif; ?>">
                            <a class="nav-link" href="<?php echo e(url('pricing')); ?>">PRICING</a>
                        </li>

                    </ul>
                    <?php if(Auth::user()): ?>
                        <div class="login_logout_header login_logout_header_hamburger ">
                            <a href="<?php echo e(url('home')); ?>"
                                class="btn_global yellow_btn arrow_up_right_btn_img">Dashboard
                                <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></a>
                        </div>
                        <div class="login_logout_header login_logout_header_hamburger ">
                            
                            <a href="<?php echo e(url('logout')); ?>" class="btn_global yellow_btn arrow_up_right_btn_img">Logout
                                <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></a>
                        </div>
                    <?php else: ?>
                        <div class="login_logout_header login_logout_header_hamburger">
                            
                            <a href="<?php echo e(url('login')); ?>" class="btn_global yellow_btn arrow_up_right_btn_img">Login
                                <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </nav>
        <?php echo $__env->yieldContent('content'); ?>
        <footer>
            <section class="footer_sec">
                <div class="container-fluid custom_container_footer">
                    <div class="row custom_footer_first_row">
                        <div class="col-md-12 custom_footer_parent_col">
                            <div class="footer_video">
                                <video autoplay muted loop>
                                    <source src="<?php echo e(asset('website')); ?>/assets/images/footer_video.mp4"
                                        type="video/mp4">
                                </video>
                                <div class="footer_wrapper">
                                    <div class="background_content">
                                        <div class="row custom_row_footer">
                                            <div class="col-md-3 col-sm-12">
                                                <div class="logo_details_sec">
                                                    <a href="<?php echo e(url('/')); ?>">
                                                        <div class="footer_logo">
                                                            <img alt="Logo"
                                                                src="<?php echo e(asset('website')); ?>/assets/images/header_logo.svg"
                                                                class="logo-default" />
                                                        </div>
                                                    </a>

                                                    <h6>Experience personalized yacht services designed to meet your
                                                        unique needs. From planning to maintenance.</h6>
                                                </div>
                                            </div>
                                            <div class="col-md-7 col-sm-12">
                                                <div class="footer_menus_wrapper" id="">
                                                    <ul class="navbar-nav">
                                                        <li
                                                            class="nav-item <?php if(request()->is('/')): ?> active <?php endif; ?>">
                                                            <a class="nav-link" href="<?php echo e(url('/')); ?>">Home</a>
                                                        </li>
                                                        <li
                                                            class="nav-item <?php if(request()->route()->getName() == 'about'): ?> active <?php endif; ?>">
                                                            <a class="nav-link" href="<?php echo e(url('about')); ?>">ABOUT</a>
                                                        </li>
                                                        <li
                                                            class="nav-item <?php if(request()->route()->getName() == 'services'): ?> active <?php endif; ?>">
                                                            <a class="nav-link"
                                                                href="<?php echo e(url('services')); ?>">SERVICES</a>
                                                        </li>
                                                        <li
                                                            class="nav-item <?php if(request()->route()->getName() == 'contact_us'): ?> active <?php endif; ?>">
                                                            <a class="nav-link"
                                                                href="<?php echo e(url('contact_us')); ?>">CONTACT US</a>
                                                        </li>
                                                        <li
                                                            class="nav-item <?php if(request()->route()->getName() == 'pricing'): ?> active <?php endif; ?>">
                                                            <a class="nav-link"
                                                                href="<?php echo e(url('pricing')); ?>">PRICING</a>
                                                        </li>

                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="col-md-2 col-sm-12">
                                                <div class="social_icons_footer">
                                                    <a href="javascript:void(0)"><i
                                                            class="fa-brands fa-linkedin"></i></a>
                                                    <a href="mailto:<EMAIL>"><i
                                                            class="fa-regular fa-envelope"></i></a>
                                                    <a href="tel:93992092-211"><i class="fa-solid fa-phone"></i></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="">
                                        <div class="copy_rights">
                                            <h6>©2024 GuestTrip. All Rights Reserved.</h6>
                                            <div class="privacy_terms_sec">
                                                <a href="javascript:void(0)">
                                                    <h6>Privacy Policy</h6>
                                                </a>
                                                <a href="javascript:void(0)">
                                                    <h6>Terms & Condition</h6>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </footer>
    </div>
    <script>
        var hostUrl = "<?php echo e(asset('website')); ?>/assets/";
    </script>
    <!--begin::Global Javascript Bundle(mandatory for all pages)-->
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/js/scripts.bundle.js"></script>
    <!--end::Global Javascript Bundle-->
    <!--begin::Vendors Javascript(used for this page only)-->
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/custom/fslightbox/fslightbox.bundle.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/custom/typedjs/typedjs.bundle.js"></script>
    <!--end::Vendors Javascript-->
    <!--begin::Custom Javascript(used for this page only)-->
    <script src="<?php echo e(asset('website')); ?>/assets/js/custom/landing.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/js/custom/pages/pricing/general.js"></script>
    

    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/jquery.validation/1.16.0/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>



    <!-- Initialize Swiper -->
    <script>
        var swiper = new Swiper(".crewCardSwiper", {
            slidesPerView: 4,
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
            },
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
        });

        var itenarySwiper = new Swiper(".itenarySwiper", {
            slidesPerView: 1,
            spaceBetween: 10,
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
            },
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
            },
        });

        <?php if(session()->has('message')): ?>
            Swal.fire({
                title: "<?php echo e(session()->get('title') ?? 'success!'); ?>",
                html: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('message')))); ?>",
                icon: "<?php echo e(session()->get('type') ?? 'success'); ?>",
                timer: 5000,
                buttons: false,
            });
        <?php endif; ?>

        <?php if(session()->has('flash_message')): ?>
            Swal.fire({
                title: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('flash_message')))); ?>",
                icon: "<?php echo e(session()->get('type') ?? 'success'); ?>",
                timer: 5000,
                buttons: false,
            });
        <?php endif; ?>
    </script>
    <!--end::Javascript-->
    <?php echo $__env->yieldPushContent('js'); ?>
</body>

</html>
<?php /**PATH D:\guesttrip\resources\views\website\layout\master.blade.php ENDPATH**/ ?>