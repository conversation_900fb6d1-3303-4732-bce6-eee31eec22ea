

<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <?php if(auth()->user()->hasRole('admin')): ?>
        <section class="homepage_section">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="homepage_cards">
                            <div class="row">
                                <div class="col-md-12">
                                    <h2>Hey Mariana -<span>here’s what’s happening with your store today</span></h2>
                                </div>
                                <div class="cards_wrapper">
                                    <div class="custom_card">
                                        <h2>Total Revenue</h2>
                                        <h1>$12,426</h1>
                                    </div>
                                    <div class="custom_card">
                                        <h2>Total Boat Managers</h2>
                                        <h1>500+</h1>
                                    </div>
                                    <div class="custom_card">
                                        <h2>Active Boat Managers</h2>
                                        <h1>84,382</h1>
                                    </div>
                                    <div class="custom_card">
                                        <h2>Total Boat Managers</h2>
                                        <h1>2,426</h1>
                                    </div>
                                    <div class="custom_card">
                                        <h2>Total Subscriptions Purchase</h2>
                                        <h1>$12,426</h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8"></div>
                    <div class="col-md-4">

                    </div>
                    <div class="col-md-8">
                        <div class="custom_table">
                            <table  class="table without_pagination_tbl datatable">
                                <thead>
                                <tr>
                                    <th>Sr#</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Address</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php for($i=0;$i<10;$i++): ?>
                                    <tr>
                                        <td>01</td>
                                        <td>John Doe</td>
                                        <td><EMAIL></td>
                                        <td>123456789</td>
                                        <td>Address Here</td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                    <li><a href="#!" class="dropdown-item">View</a></li>
                                                    <li><a href="#!" class="dropdown-item">Edit</a></li>
                                                    <li><a href="#!" class="dropdown-item">Delete</a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endfor; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4"></div>
                </div>
            </div>
        </section>

    <?php endif; ?>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\index.blade.php ENDPATH**/ ?>