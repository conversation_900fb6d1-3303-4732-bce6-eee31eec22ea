
<!-- Modal -->
<div class="modal fade create_trip_modal custom_trip_modal" id="create_trip_modal" tabindex="-1"
    aria-labelledby="createModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title" id="createModalLabel">Create Trip</h1>
                <button type="button" class="close btn-close"></button>
            </div>
            <div class="modal-body ">
                <form id="trip-create-form">
                    <?php echo csrf_field(); ?>
                    <div class="row custom_row_modal">
                        <div class="col-md-12">
                            <div class="txt_field">
                                <label>Trip Name:</label>
                                <input type="text" name="name" value="<?php echo e(old('name')); ?>"
                                    class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Vacation"
                                    required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="alert alert-danger mt-2"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="txt_field">
                                <label>Start Date:</label>
                                <input type="date" name="start_date" value="<?php echo e(old('start_date')); ?>"
                                    class="form-control <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    placeholder="13/5/2024" required id="start_date">
                                <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="alert alert-danger mt-2"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="txt_field">
                                <label>End Date:</label>
                                <input type="date" name="end_date" value="<?php echo e(old('end_date')); ?>"
                                    class="form-control <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="20/5/2024"
                                    required id="end_date">
                                <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="alert alert-danger mt-2"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="txt_field">
                                <label>Duration:</label>
                                <input type="text" name="duration" value="<?php echo e(old('duration')); ?>"
                                    class=" form-control <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="20 Days"
                                    required id="duration" readonly>
                                <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="alert alert-danger mt-2"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="txt_field">
                                <label>URL: <?php echo e(url('/')); ?>/<?php echo e(auth()->user()->company_name ?? ''); ?>/</label>
                                <input type="text" name="url_slug" value="<?php echo e(old('url_slug')); ?>"
                                    class="form-control <?php $__errorArgs = ['url_slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    placeholder="Type your URL">
                            </div>
                            <?php $__errorArgs = ['url_slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="alert alert-danger mt-2"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="col-md-4">
                            <button type="button" id="submitButton1" class="btn btn_dark_green">Add
                                Trip</button>
                        </div>
                        <div class="col-md-4">
                            <button type="button" id="submitButton2" class="btn btn_green generate_ai_btn">Generate
                                with AI <img src="<?php echo e(asset('website')); ?>/assets/images/ai_icon.png"></button>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="close btn btn_transparent"
                                data-bs-dismiss="modal">Cancel</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="modal fade create_trip_ai" id="create_trip_ai" tabindex="-1" aria-labelledby="createModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title" id="createModalLabel">Generate With AI</h1>
                <button type="button" class="close btn-close"></button>
            </div>
            <div class="modal-body ">
                <form id="trip-itinerarie-generate">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="slug" id="trip_slug" value="">
                    <div class="row custom_row_modal">
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Trip Area:</label>
                                <input type="text" name="trip_area" class="form-control"
                                    placeholder="Describe the sailing area of your itinerary">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Duration:</label>
                                <input type="text" value="" id="trip_duration"
                                    class="form-control duration_days_input" readonly placeholder=""><span>Days</span>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="txt_field">
                                <label>Trip Start Location:</label>
                                <input type="text" name="locations[]" value=""
                                    class="form-control input_start_location" placeholder="">
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="stop_whole_wrap">
                                <div class="ai_trip_stop_append_wrapper">
                                    <div class="txt_field">
                                        <label>Trip Stop 1:</label>
                                        <input type="text" name="locations[]" value=""
                                            class="form-control trip_stop_input" placeholder="">
                                    </div>
                                    <button type="button" class="ai_trip_stop_append_btn delete_stop_all_styling"><i
                                            class="fa-solid fa-plus"></i></button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="txt_field">
                                <label>Trip End Location:</label>
                                <input type="text" name="locations[]" value=""
                                    class="form-control input_end_location" placeholder="">
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div>
                                <btn class="btn btn_dark_green finish_trip" type="button">Build Trip</btn>
                            </div>
                        </div>
                        <div class="col-md-12 my_trip_custom_column">
                            <h1 class="my_trip_heading">My Trip</h1>
                            <div class="my_trip_details_stop">
                                <div class="my_trip_box start">
                                    <h3>Start Trip</h3>
                                    <h2></h2>
                                </div>

                                <div class="my_trip_box end">
                                    <h3>End Trip</h3>
                                    <h2></h2>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="txt_field">
                                <label>Trip preferences:</label>
                                <input type="text" name="preferences" class="form-control"
                                    placeholder="Include any particular points of interest here. E.g high end restaurants, amazing diving spot to visit, picturesque anchor areas etc">
                            </div>
                        </div>

                        <div class="col-md-12">
                            <button type="button" id="generate-itineraries"
                                class="btn btn_transparent">Generate</button>
                        </div>
                        
                        
                        
                        
                        
                        
                        
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Itineraries Modal -->
<div class="modal fade" id="itineraries-modal" tabindex="-1" aria-labelledby="itinerariesModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title" id="itinerariesModalLabel">Generated Itineraries</h1>
                <button type="button" class="close btn-close">
                </button>
            </div>
            <div class="modal-body itineraries_modal_body">
                <div class="itinerary-overview">

                </div>
                <div class="itineraries-description">
                    <!-- Itinerary content will be injected here by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="close btn btn_dark_green" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn_transparent" id="proceed-itineraries">Proceed</button>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\guesttrip\resources\views/trips/trip_modals.blade.php ENDPATH**/ ?>