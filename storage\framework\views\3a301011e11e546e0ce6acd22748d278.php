<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Waypoints in Directions</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA0z3VFl1wO12-FWBFTC1_fdfOMJgaL9R4&libraries=places&callback=initMap"
        defer></script>
    <style>
        #map {
            height: 400px;
            width: 100%;
        }

        #routes {
            margin-top: 20px;
        }

        .route-container {
            margin-bottom: 10px;
            position: relative;
        }

        .remove-btn {
            color: red;
            cursor: pointer;
            font-size: 20px;
            position: absolute;
            top: 5px;
            right: 10px;
        }
    </style>
</head>

<body>

    <div id="container">
        <div id="map"></div>
        <div id="sidebar">
            <div id="routes">
                <div class="route-container" id="route1">
                    <b>Start:</b>
                    <input type="text" class="start-point" id="start1" placeholder="Enter starting location">
                    <br />
                    <b>End:</b>
                    <input type="text" class="end-point" id="end1" placeholder="Enter destination">
                    <span class="remove-btn" onclick="removeRoute('route1')">×</span>
                </div>
            </div>
            <input type="button" value="Add More" id="addMore" />
            <input type="submit" id="submit" />
            <div id="directions-panel"></div>
        </div>
    </div>

    <script>
        let routeCount = 1;

        function initMap() {
            const directionsService = new google.maps.DirectionsService();
            const directionsRenderer = new google.maps.DirectionsRenderer();
            const map = new google.maps.Map($("#map")[0], {
                zoom: 6,
                center: {
                    lat: 41.85,
                    lng: -87.65
                },
            });

            directionsRenderer.setMap(map);

            $("#submit").on("click", () => {
                calculateAndDisplayRoute(directionsService, directionsRenderer);
            });

            $("#addMore").on("click", addRoute);

            // Initialize autocomplete for start and end fields
            initAutocomplete();
        }

        // Initialize Autocomplete for input fields
        function initAutocomplete() {
            $("input[type='text']").each(function() {
                new google.maps.places.Autocomplete(this);
            });
        }

        // Calculate and Display the route based on the user inputs
        function calculateAndDisplayRoute(directionsService, directionsRenderer) {
            const waypts = [];
            const routeContainers = $(".route-container");

            const startPoint = $(".start-point").first().val(); // Start point from the first container
            const endPoint = $(".end-point").last().val(); // End point from the last container

            // If there are more than one route container, we add waypoints
            if (routeContainers.length > 1) {
                // Loop through all route containers and add their end points as waypoints
                routeContainers.each((index, routeContainer) => {
                    const endInput = $(routeContainer).find(".end-point");

                    // Add the first route container's end point as a waypoint
                    if (index !== 0 && index !== routeContainers.length - 1) {
                        waypts.push({
                            location: endInput.val(),
                            stopover: true,
                        });
                    }
                    // Ensure the first route container's end point is also added as a waypoint
                    if (index === 0) {
                        waypts.push({
                            location: endInput.val(),
                            stopover: true,
                        });
                    }
                });
            }

            // Now make the directions request
            directionsService
                .route({
                    origin: startPoint,
                    destination: endPoint,
                    waypoints: waypts,
                    optimizeWaypoints: true,
                    travelMode: google.maps.TravelMode.DRIVING,
                })
                .then((response) => {
                    directionsRenderer.setDirections(response);
                    const route = response.routes[0];
                    const summaryPanel = $("#directions-panel");

                    summaryPanel.empty();
                    route.legs.forEach((leg, i) => {
                        const routeSegment = i + 1;
                        summaryPanel.append(`
                    <b>Route Segment: ${routeSegment}</b><br>
                    ${leg.start_address} to ${leg.end_address}<br>
                    ${leg.distance.text}<br><br>
                `);
                    });
                })
                .catch((e) => window.alert("Directions request failed due to " + e));
        }

        // Add a new route (with dynamic start/end points)
        function addRoute() {
            routeCount++;
            const lastRouteEndPoint = $(`#route${routeCount - 1} .end-point`).val();

            const newRouteDiv = $(`
    <div class="route-container" id="route${routeCount}">
      <b>Start:</b>
      <input type="text" class="start-point" id="start${routeCount}" placeholder="Enter starting location">
      <br />
      <b>End:</b>
      <input type="text" class="end-point" id="end${routeCount}" placeholder="Enter destination">
      <span class="remove-btn" onclick="removeRoute('route${routeCount}')">×</span>
    </div>
  `);

            $("#routes").append(newRouteDiv);

            // Set the start point of this new route as the end point of the previous route
            newRouteDiv.find(".start-point").val(lastRouteEndPoint);

            initAutocomplete(); // Reinitialize autocomplete after adding a new route
        }

        // Remove a specific route
        function removeRoute(routeId) {
            $(`#${routeId}`).remove();
            updateRouteStartPoints();
        }

        // Update the start points of routes after one is removed
        function updateRouteStartPoints() {
            const routeContainers = $(".route-container");
            routeContainers.each((index, routeContainer) => {
                const startInput = $(routeContainer).find(".start-point");
                if (index === 0) {
                    startInput.prop("disabled", false); // First route: allow user to choose start point
                } else {
                    const prevEndPoint = $(routeContainers[index - 1]).find(".end-point");
                    startInput.val(prevEndPoint
                        .val()); // Set start point to the previous route's end point
                    startInput.prop("disabled", true); // Disable to prevent editing
                }
            });
        }

        window.initMap = initMap;
    </script>

</body>

</html>
<?php /**PATH D:\guesttrip\resources\views\website\google_mapOLD.blade.php ENDPATH**/ ?>