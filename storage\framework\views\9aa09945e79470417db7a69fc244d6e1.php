<?php $__env->startPush('css'); ?>
    
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <?php if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('developer')): ?>
        <section class="subscription_management">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="custom_cards_design">
                            <h2>My Subscriptions </h2>
                            <div class="row">
                                <?php $__currentLoopData = $packages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-md-4 mb-2">
                                        <div class="custom_card">
                                            <div class="custom_justify_between subs_card_clr">
                                                <h1><?php echo e($item->name ?? ''); ?></h1>
                                                <div class="dropdown">
                                                    <button class="dropdown-toggle" type="button" id="dropdownMenuButton11"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                        <li><a href="" class="dropdown-item edit-package-btn"
                                                                data-package-id="<?php echo e($item->id); ?>"
                                                                data-bs-target="#edit_subscription"
                                                                data-bs-toggle="modal"><i
                                                                    class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="amount">
                                                <label>Amount:</label>
                                                <span>$<?php echo e($item->amount ?? ''); ?></span>
                                            </div>
                                            <div class="d-flex justify-content-around">
                                                
                                                <div class="custom_select">
                                                    <label>Templates:</label><br>
                                                    <?php $__currentLoopData = $item->templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <span><?php echo e($template->name ?? ''); ?></span><br>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                            <div class="custom_justify_between ">
                                                <h3>Total Trips</h3>
                                                <div class="txt_field">
                                                    <input type="number" class="total_tips_count"
                                                        value="<?php echo e($item->total_trips); ?>" readonly>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <div class="modal fade custom_modal" id="editPackageModal" tabindex="-1" aria-labelledby="createModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title" id="createModalLabel">Upgrade Subscription</h1>
                        
                    </div>
                    <div class="modal-body ">
                        <form id="editPackageForm" method="POST" action="">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <div class="modal-body">
                                <div class="row custom_row">
                                    <div class="col-md-12">
                                        <div class="txt_field">
                                            <label for="name">Package:</label>
                                            <input type="text" class="form-control" id="name" name="name"
                                                required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="txt_field">
                                            <label for="amount">Amount:</label>
                                            <input type="number" class="form-control" id="amount" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="txt_field">
                                            <label for="totalTrips">Total Trips:</label>
                                            <input type="number" class="total_tips_count form-control" id="totalTrips"
                                                name="total_trips" required>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="txt_field">
                                            <label for="description">Description:</label>
                                            <textarea id="description" name="description" class="form-control"></textarea>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-12">
                                        <div class="txt_field custom_select">
                                            <label for="templates">Templates:</label>
                                            <div class="select_modal">
                                                <select multiple
                                                    class="custom_multi_select form-select form-select-transparent"
                                                    data-control="select2" data-placeholder="Templates" name="templates[]"
                                                    id="templates">
                                                    <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($item->id); ?>"><?php echo e($item->name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="submit" class="btn btn_dark_green">Update</button>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn_transparent"
                                            data-bs-dismiss="modal">Cancel</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // $('.show-arrow ul li').prepend('<i class="fa-solid fa-circle-arrow-right"></i>');
            $('.custom_multiselect').select2({
                placeholder: "Select An Option",
                allowClear: true
            });

            function initializeEditors(element, callback) {
                if (element && !element.ckeditorInstance) { // Ensure CKEditor isn't already initialized
                    ClassicEditor.create(element)
                        .then(editor => {
                            element.ckeditorInstance = editor; // Attach instance for future reference
                            if (callback) callback(editor); // Call the callback after initialization
                        })
                        .catch(error => {
                            console.error("CKEditor initialization error:", error);
                        });
                } else if (element.ckeditorInstance && callback) {
                    callback(element.ckeditorInstance); // Editor already initialized, call the callback
                }
            }

            // Trigger AJAX call when a package is selected
            $('.edit-package-btn').click(function() {
                var packageId = $(this).data('package-id'); // Get the selected package ID

                $.ajax({
                    url: "<?php echo e(url('packages')); ?>/" + packageId +
                        '/details', // AJAX endpoint for getting package details
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        // Populate the modal fields with the package data
                        // $('#upgradeTo').val(response.id); // Set the selected package (ID)
                        $('#name').val(response.name); // Set the amount
                        $('#amount').val(response.amount); // Set the amount
                        $('#totalTrips').val(response.total_trips); // Set total trips

                        // Populate the sections
                        // var selectedSections = response.sections.map(function(section) {
                        //     return section.id;
                        // });
                        // Populate the sections
                        var selectedTemplates = response.templates.map(function(template) {
                            return template.id;
                        });

                        // $('#sections').val(selectedSections).trigger(
                        //     'change'); // Set the selected sections using select2
                        $('#templates').val(selectedTemplates).trigger(
                            'change');

                        var descriptionElement = document.getElementById('description');

                        if (descriptionElement) {
                            initializeEditors(descriptionElement, (editor) => {
                                if (response.description !== null) {
                                    try {
                                        editor.setData(response
                                        .description); // Safely set the description data
                                    } catch (error) {
                                        console.error("Error setting CKEditor data:",
                                            error);
                                    }
                                }
                            });
                        } else {
                            console.error("Description element not found.");
                        }

                        // Dynamically set the form action URL with the named route
                        var formAction = "<?php echo e(route('packages.update', ':id')); ?>".replace(':id',
                            packageId);
                        console.log("Form Action URL:", formAction); // Debug

                        $('#editPackageForm').attr('action', formAction);
                    },
                    error: function(error) {
                        console.log('Error:', error);
                    }
                });

                // Show the modal
                $('#editPackageModal').modal('show');
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\packages\index.blade.php ENDPATH**/ ?>