

<?php $__env->startPush('css'); ?>
    <link href="assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <style>
        #map {
            height: 400px;
            width: 100%;
        }

        #routes {
            margin-top: 20px;
        }

        .route-container {
            margin-bottom: 10px;
            position: relative;
        }

        .remove-btn {
            color: red;
            cursor: pointer;
            font-size: 20px;
            position: absolute;
            top: 5px;
            right: 10px;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="content_management">
        <div class="container-fluid">
            <div class="row custom_row">
                <div class="col-md-12">
                    <div class="trip_details">
                        <form method="post" action="<?php echo e(route('trips.update', $trip->id)); ?>" enctype="multipart/form-data">
                            <?php echo e(method_field('PATCH')); ?>

                            <?php echo e(csrf_field()); ?>

                            <div class="row custom_row">
                                <div class="col-md-12">
                                    <div class="cms_edit_btn">
                                        <button type="button" id="edit" class="btn btn_dark_green"><i
                                                class="fa-solid fa-pen-to-square"></i>Edit</button>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section">
                                        <div class="row custom_row">
                                            
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>URL</label>
                                                    <input type="link" class="form-control myinput"
                                                        placeholder="Www.guestrip.com/<?php echo e($trip->name ?? '-'); ?>/<?php echo e($trip->url_slug ?? '-'); ?>"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Duration</label>
                                                    <input type="text" class="form-control myinput"
                                                        placeholder="<?php echo e($trip->duration ?? '-'); ?> Days" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="choose_template">
                                                    <h2>Choose Template</h2>
                                                    <div class="custom_flex custom_template">
                                                        <div class="select_template">
                                                            <div class="custom_radio_wrapper custom_recipient_checked">
                                                                <input class="form-check-input" type="radio"
                                                                    value="" name="choose_template"
                                                                    id="recipientChecked" checked>
                                                                <label for="recipientChecked"></label>
                                                            </div>
                                                            <div class="template_image">
                                                                <img
                                                                    src="<?php echo e(asset('website')); ?>/assets/images/template_one.png">
                                                            </div>
                                                        </div>
                                                        <div class="select_template">
                                                            <div class="custom_radio_wrapper custom_business_checked">
                                                                <input class="form-check-input" type="radio"
                                                                    value="" name="choose_template"
                                                                    id="businessChecked">
                                                                <label for="businessChecked"></label>
                                                            </div>
                                                            <div class="template_image">
                                                                <img
                                                                    src="<?php echo e(asset('website')); ?>/assets/images/template_two.png">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section custom_cards_design main_header_section">
                                        <h1>Section 01: Main Header</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Kicker</label>
                                                    <input type="text" name="main_header_kicker"
                                                        class="form-control myinput" placeholder="kicker"
                                                        value="<?php echo e(@$trip->mainHeader->kicker ?? ''); ?>" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" name="main_header_heading"
                                                        class="form-control myinput" placeholder="Discover Thailand"
                                                        value="<?php echo e(@$trip->mainHeader->heading ?? ''); ?>" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Sub Heading</label>
                                                    <input type="text" name="main_header_sub_heading"
                                                        class="form-control myinput"
                                                        value="<?php echo e(@$trip->mainHeader->sub_heading ?? ''); ?>"
                                                        placeholder="From 23 Jan 2023 to 30 Jan 2023 on board SUPER YACHT"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6"></div>
                                            <div class="col-md-12">
                                                <div class="txt_field txt_description">
                                                    <label>Description</label>
                                                    <textarea class="form-control myinput" placeholder="type here" name="main_header_description" rows="3"
                                                        placeholder="" readonly><?php echo e(@$trip->mainHeader->description ?? ''); ?></textarea>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <h3>Images</h3>
                                                <div class="custom_profile_upload custom_flex append_sub_images">
                                                    <?php $__currentLoopData = $trip->mainHeaderImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="profile_picture">
                                                            <div class="profile_image">
                                                                <!--begin::Image input-->
                                                                <div class="image-input image-input-outline"
                                                                    data-kt-image-input="true">
                                                                    <!--begin::Image preview wrapper-->
                                                                    <div class="image-input-wrapper">
                                                                        <?php if($item->image != null): ?>
                                                                            <img class="input_image_field"
                                                                                src="<?php echo e(asset('website/' . $item->image)); ?>"
                                                                                data-original-src="<?php echo e(asset('website/' . $item->image)); ?>">
                                                                        <?php else: ?>
                                                                            <img class="input_image_field"
                                                                                src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"
                                                                                data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png">
                                                                        <?php endif; ?>
                                                                    </div>
                                                                    <!--end::Image preview wrapper-->

                                                                    <!--begin::Edit button-->
                                                                    <label
                                                                        class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                        data-kt-image-input-action="change"
                                                                        data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                        title="Change avatar">
                                                                        <i class="ki-duotone ki-pencil fs-6"><span
                                                                                class="path1"></span><span
                                                                                class="path2"></span></i>

                                                                        <!--begin::Inputs-->
                                                                        <input type="file"
                                                                            name="main_header_images[<?php echo e($item->id); ?>]"
                                                                            accept=".png, .jpg, .jpeg"
                                                                            class="custom_file_input" />
                                                                        <input type="hidden"
                                                                            name="main_header_image_exist[<?php echo e($item->id); ?>]"
                                                                            value="<?php echo e($item->id); ?>" />
                                                                        
                                                                        <!--end::Inputs-->
                                                                    </label>
                                                                    <!--end::Edit button-->

                                                                    <!--begin::Cancel button-->
                                                                    <span
                                                                        class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                        data-kt-image-input-action="cancel"
                                                                        data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                        title="Cancel avatar">
                                                                        <i class="ki-outline ki-cross fs-3"></i>
                                                                    </span>
                                                                    <!--end::Cancel button-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="add_sub_section">
                                                    <button type="button" class="btn btn_grey append_images"><i
                                                            class="fa-solid fa-plus"></i>Add More Images</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section custom_cards_design">
                                        <h1>Section 02: Itinerary Summary</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Kicker</label>
                                                    <input type="text" class="form-control myinput"
                                                        value="One Week Itinerary for our Our VIP Client" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" class="form-control myinput"
                                                        value="Discover Thailand" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Sub Heading</label>
                                                    <input type="text" class="form-control myinput"
                                                        value="From 23 Jan 2023 to 30 Jan 2023 on board SUPER YACHT"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6"></div>
                                        </div>
                                        <h1 class="custom_margin">Stop Details</h1>

                                        <div id="routes" class="append_sub_section">
                                            <?php $__currentLoopData = $trip->itineraries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $itinerary): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="sub_sections route-container"
                                                    id="route<?php echo e($loop->iteration); ?>">
                                                    <h2>Sub Section 0<?php echo e($loop->iteration); ?></h2>
                                                    <div class="cms_section custom_cards_design location_section"
                                                        data-location="<?php echo e($itinerary->id); ?>">
                                                        <div class="row custom_row">
                                                            <div class="col-md-6">
                                                                <div class="txt_field">
                                                                    <label>Number</label>
                                                                    <input name="itinerary[<?php echo e($itinerary->id); ?>][number]"
                                                                        value="<?php echo e($itinerary->number ?? ''); ?>"
                                                                        class="form-control myinput" type="number"
                                                                        placeholder="0<?php echo e($loop->iteration); ?>">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="txt_field">
                                                                    <label>Duration</label>
                                                                    <input
                                                                        name="itinerary[<?php echo e($itinerary->id); ?>][duration]"
                                                                        value="<?php echo e($itinerary->duration ?? ''); ?>"
                                                                        class="form-control myinput" type="text"
                                                                        placeholder="Day">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="txt_field">
                                                                    <label>Date</label>
                                                                    <input name="itinerary[<?php echo e($itinerary->id); ?>][date]"
                                                                        value="<?php echo e($itinerary->date ?? ''); ?>"
                                                                        class="form-control myinput" type="text"
                                                                        placeholder="Monday, 23 Jan 2023">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6"></div>
                                                            <div class="col-md-6">
                                                                <h3>Location</h3>
                                                                <div class="txt_field custom_time">
                                                                    <label>Start Point:</label>
                                                                    <input
                                                                        name="itinerary[<?php echo e($itinerary->id); ?>][start_point]"
                                                                        value="<?php echo e($itinerary->start_point ?? ''); ?>"
                                                                        class="form-control myinput start-point"
                                                                        id="start<?php echo e($loop->iteration); ?>" type="text"
                                                                        placeholder="Enter starting location">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="txt_field custom_time custom_margin">
                                                                    <label>End Point:</label>
                                                                    <input
                                                                        name="itinerary[<?php echo e($itinerary->id); ?>][end_point]"
                                                                        value="<?php echo e($itinerary->end_point ?? ''); ?>"
                                                                        class="form-control myinput end-point"
                                                                        id="end<?php echo e($loop->iteration); ?>" type="text"
                                                                        placeholder="Enter destination">
                                                                </div>
                                                            </div>
                                                            <input name="itinerary[<?php echo e($itinerary->id); ?>][start_lat]"
                                                                value="<?php echo e($itinerary->start_lat ?? ''); ?>" type="hidden"
                                                                class="start-lat" id="start-lat<?php echo e($loop->iteration); ?>">
                                                            <input name="itinerary[<?php echo e($itinerary->id); ?>][start_lng]"
                                                                value="<?php echo e($itinerary->start_lng ?? ''); ?>" type="hidden"
                                                                class="start-lng" id="start-lng<?php echo e($loop->iteration); ?>">
                                                            <input name="itinerary[<?php echo e($itinerary->id); ?>][end_lat]"
                                                                value="<?php echo e($itinerary->end_lat ?? ''); ?>" type="hidden"
                                                                class="end-lat" id="end-lat<?php echo e($loop->iteration); ?>">
                                                            <input name="itinerary[<?php echo e($itinerary->id); ?>][end_lng]"
                                                                value="<?php echo e($itinerary->end_lng ?? ''); ?>" type="hidden"
                                                                class="end-lng" id="end-lng<?php echo e($loop->iteration); ?>">
                                                            <div class="col-md-6">
                                                                <div class="txt_field">
                                                                    <label>Time</label>
                                                                    <input name="itinerary[<?php echo e($itinerary->id); ?>][time]"
                                                                        value="<?php echo e($itinerary->time ?? ''); ?>"
                                                                        class="form-control myinput" type="text"
                                                                        placeholder="26nm - 2h 15m - Embark at midday.">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6"></div>
                                                            <div class="col-md-12">
                                                                <div class="txt_field txt_description">
                                                                    <label>Description:</label>
                                                                    <textarea name="itinerary[<?php echo e($itinerary->id); ?>][description]" class="form-control myinput" rows="4"
                                                                        placeholder="Type Here"><?php echo e($itinerary->description ?? ''); ?></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <h3>Image</h3>
                                                                <div
                                                                    class="custom_profile_upload custom_flex append_sub_images">
                                                                    <?php $__currentLoopData = $itinerary->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <div class="profile_picture">
                                                                            <div class="profile_image">
                                                                                <!--begin::Image input-->
                                                                                <div class="image-input image-input-outline"
                                                                                    data-kt-image-input="true">
                                                                                    <!--begin::Image preview wrapper-->
                                                                                    <div class="image-input-wrapper">
                                                                                        <?php if($image->image != null): ?>
                                                                                            <img class="input_image_field"
                                                                                                src="<?php echo e(asset('website/' . $image->image)); ?>"
                                                                                                data-original-src="<?php echo e(asset('website/' . $image->image)); ?>">
                                                                                        <?php else: ?>
                                                                                            <img class="input_image_field"
                                                                                                src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"
                                                                                                data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png">
                                                                                        <?php endif; ?>
                                                                                    </div>
                                                                                    <!--end::Image preview wrapper-->

                                                                                    <!--begin::Edit button-->
                                                                                    <label
                                                                                        class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                        data-kt-image-input-action="change"
                                                                                        data-bs-toggle="tooltip"
                                                                                        data-bs-dismiss="click"
                                                                                        title="Change avatar">
                                                                                        <i
                                                                                            class="ki-duotone ki-pencil fs-6"><span
                                                                                                class="path1"></span><span
                                                                                                class="path2"></span></i>

                                                                                        <!--begin::Inputs-->
                                                                                        <input type="file"
                                                                                            name="itinerary[<?php echo e($itinerary->id); ?>][images][<?php echo e($image->id); ?>]"
                                                                                            accept=".png, .jpg, .jpeg"
                                                                                            class="custom_file_input" />
                                                                                        <input type="hidden"
                                                                                            name="itinerary[<?php echo e($itinerary->id); ?>][imagesOLD][<?php echo e($image->id); ?>]"
                                                                                            value="<?php echo e($image->id); ?>" />
                                                                                        <!--end::Inputs-->
                                                                                    </label>
                                                                                    <!--end::Edit button-->

                                                                                    <!--begin::Cancel button-->
                                                                                    <span
                                                                                        class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                        data-kt-image-input-action="cancel"
                                                                                        data-bs-toggle="tooltip"
                                                                                        data-bs-dismiss="click"
                                                                                        title="Cancel avatar">
                                                                                        <i
                                                                                            class="ki-outline ki-cross fs-3"></i>
                                                                                    </span>
                                                                                    <!--end::Cancel button-->
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <div class="add_sub_section">
                                                                    <span class="remove-btn"
                                                                        onclick="removeRoute('route<?php echo e($loop->iteration); ?>')">X</span>
                                                                    <button type="button"
                                                                        class="btn btn_grey append_images"><i
                                                                            class="fa-solid fa-plus"></i>Add More
                                                                        Images</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <div class="add_sub_section">
                                            <button id="showlocationsonmap" type="button" class="btn btn_grey">View
                                                Map</button>
                                            <button id="addMore" type="button"
                                                class="btn btn_grey append_stop_detail_cards"><i
                                                    class="fa-solid fa-plus"></i>Add More</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section custom_cards_design">
                                        <h1>Section 03: Itinerary Map</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Kicker</label>
                                                    <input type="text" class="form-control myinput"
                                                        value="One Week Itinerary for our Our VIP Client" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" class="form-control myinput"
                                                        value="Discover Thailand" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Sub Heading</label>
                                                    <input type="text" class="form-control myinput"
                                                        value="From 23 Jan 2023 to 30 Jan 2023 on board SUPER YACHT"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6"></div>
                                            <div class="col-md-12">
                                                <div id="map" class="google_map">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section custom_cards_design">
                                        <h1>Section 04: Crew Details</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Kicker</label>
                                                    <input type="text" name="crew_detail_kicker"
                                                        class="form-control myinput" placeholder="kicker"
                                                        value="<?php echo e(@$trip->crewDetail->kicker ?? ''); ?>" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" name="crew_detail_heading"
                                                        class="form-control myinput" placeholder="Discover Thailand"
                                                        value="<?php echo e(@$trip->crewDetail->heading ?? ''); ?>" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Sub Heading</label>
                                                    <input type="text" name="crew_detail_sub_heading"
                                                        class="form-control myinput"
                                                        value="<?php echo e(@$trip->crewDetail->sub_heading ?? ''); ?>"
                                                        placeholder="From 23 Jan 2023 to 30 Jan 2023 on board SUPER YACHT"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6"></div>
                                            <div class="col-md-12">
                                                <div class="txt_field custom_select">
                                                    <label>Select Crew:</label>
                                                    <div class="onboarded_crew_list">
                                                        <?php $__currentLoopData = $crew; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="custom_check_box">
                                                                <input name="crews[]"
                                                                    class="form-check-input append_checkbox_item"
                                                                    data-profile="<?php echo e($item->image); ?>" type="checkbox"
                                                                    value="<?php echo e($item->id); ?>"
                                                                    <?php if(in_array($item->id, $assignedCrewIds)): ?> checked <?php endif; ?>
                                                                    id="Check<?php echo e($item->id); ?>">
                                                                <label
                                                                    for="Check<?php echo e($item->id); ?>"><?php echo e($item->name ?? ''); ?></label>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="onboarded_crew">
                                                    <h3>On Boarded Crew</h3>
                                                    <div class="append_crew_detail"></div>
                                                </div>
                                                
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section custom_cards_design">
                                        <h1>Section 05: Menu Plan</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Kicker</label>
                                                    <input type="text" name="menu_kicker" class="form-control myinput"
                                                        placeholder="kicker" value="<?php echo e(@$trip->menu->kicker ?? ''); ?>"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" name="menu_heading"
                                                        class="form-control myinput" placeholder="Discover Thailand"
                                                        value="<?php echo e(@$trip->menu->heading ?? ''); ?>" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Sub Heading</label>
                                                    <input type="text" name="menu_sub_heading"
                                                        class="form-control myinput"
                                                        value="<?php echo e(@$trip->menu->sub_heading ?? ''); ?>"
                                                        placeholder="From 23 Jan 2023 to 30 Jan 2023 on board SUPER YACHT"
                                                        readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="append_sub_section">
                                            <?php $__currentLoopData = $trip->menuPlans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="sub_sections" data-menu="<?php echo e($plan->id); ?>">
                                                    <h2>Sub Section 01</h2>
                                                    <div class="cms_section custom_cards_design">
                                                        <div class="row custom_row">
                                                            <div class="col-md-12">
                                                                <div class="txt_field">
                                                                    <label>Name</label>
                                                                    <input class="form-control myinput"
                                                                        name="menu_plans[<?php echo e($plan->id); ?>][name]"
                                                                        value="<?php echo e($plan->name); ?>" type="text"
                                                                        readonly>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <div class="add_breakfast_section">
                                                                    <div class="append_menu">
                                                                        <?php $__currentLoopData = $plan->details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <div class="append_items">
                                                                                <div class="add_image">
                                                                                    <h2>Image</h2>
                                                                                    <div class="profile_picture">
                                                                                        <div class="profile_image">
                                                                                            <!--begin::Image input-->
                                                                                            <div class="image-input image-input-outline"
                                                                                                data-kt-image-input="true">
                                                                                                <!--begin::Image preview wrapper-->
                                                                                                <div class="image-input-wrapper">
                                                                                                    <?php if($detail->image != null): ?>
                                                                                                        <img class="input_image_field"
                                                                                                            src="<?php echo e(asset('website/' . $detail->image)); ?>"
                                                                                                            data-original-src="<?php echo e(asset('website/' . $detail->image)); ?>">
                                                                                                    <?php else: ?>
                                                                                                        <img class="input_image_field"
                                                                                                            src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"
                                                                                                            data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png">
                                                                                                    <?php endif; ?>
                                                                                                </div>
                                                                                                <!--end::Image preview wrapper-->

                                                                                                <!--begin::Edit button-->
                                                                                                <label
                                                                                                    class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                                    data-kt-image-input-action="change"
                                                                                                    data-bs-toggle="tooltip"
                                                                                                    data-bs-dismiss="click"
                                                                                                    title="Change avatar">
                                                                                                    <i
                                                                                                        class="ki-duotone ki-pencil fs-6"><span
                                                                                                            class="path1"></span><span
                                                                                                            class="path2"></span></i>

                                                                                                    <!--begin::Inputs-->
                                                                                                    <input type="file"
                                                                                                        name="menu_plans[<?php echo e($plan->id); ?>][details][<?php echo e($detail->id); ?>][image]"
                                                                                                        accept=".png, .jpg, .jpeg"
                                                                                                        class="custom_file_input" />
                                                                                                    <input type="hidden"
                                                                                                        name="avatar_remove" />
                                                                                                    <!--end::Inputs-->
                                                                                                </label>
                                                                                                <!--end::Edit button-->

                                                                                                <!--begin::Cancel button-->
                                                                                                <span
                                                                                                    class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                                    data-kt-image-input-action="cancel"
                                                                                                    data-bs-toggle="tooltip"
                                                                                                    data-bs-dismiss="click"
                                                                                                    title="Cancel avatar">
                                                                                                    <i
                                                                                                        class="ki-outline ki-cross fs-3"></i>
                                                                                                </span>
                                                                                                <!--end::Cancel button-->
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="txt_field">
                                                                                    <label>Name</label>
                                                                                    <input class="form-control myinput"
                                                                                        type="text"
                                                                                        name="menu_plans[<?php echo e($plan->id); ?>][details][<?php echo e($detail->id); ?>][title]"
                                                                                        value="<?php echo e($detail->title); ?>"
                                                                                        readonly>
                                                                                </div>
                                                                                <div class="txt_field txt_description">
                                                                                    <label>Description</label>
                                                                                    <textarea class="form-control myinput" name="menu_plans[<?php echo e($plan->id); ?>][details][<?php echo e($detail->id); ?>][text]"
                                                                                        rows="3" placeholder="" readonly><?php echo e($detail->text ?? ''); ?></textarea>
                                                                                </div>
                                                                            </div>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <div class="add_sub_section add_btn">
                                                                    <button type="button"
                                                                        class="btn btn_grey add_breakfast"><i
                                                                            class="fa-solid fa-plus"></i></button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <div class="add_sub_section">
                                            <button type="button" class="btn btn_grey append_breakfast_name"><i
                                                    class="fa-solid fa-plus add_menu_plan"></i>Add More</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section custom_cards_design append_activity_text activity_section">
                                        <h1>Section 06: Activities</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Kicker</label>
                                                    <input type="text" name="activity_kicker"
                                                        value="<?php echo e(@$trip->activity->kicker ?? ''); ?>"
                                                        class="form-control myinput" placeholder="Type Kicker" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" name="activity_heading"
                                                        value="<?php echo e(@$trip->activity->heading ?? ''); ?>"
                                                        class="form-control myinput" placeholder="Type Heading" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Sub Heading</label>
                                                    <input type="text" name="activity_sub_heading"
                                                        value="<?php echo e(@$trip->activity->sub_heading ?? ''); ?>"
                                                        class="form-control myinput" placeholder="Type Sub Heading"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6"></div>
                                            <div class="col-md-12 custom_text">
                                                <?php if($trip->activity): ?>
                                                    <?php $__currentLoopData = $trip->activity->texts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $text): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="txt_field txt_description">
                                                            <label>Text</label>
                                                            <textarea class="form-control myinput" name="texts[]" rows="1" placeholder="Type Here" readonly><?php echo e($text ?? ''); ?></textarea>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php endif; ?>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="add_sub_section add_btn">
                                                    <button type="button" class="btn btn_grey add_text"><i
                                                            class="fa-solid fa-plus"></i></button>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <h3>Images</h3>
                                                <div class="custom_profile_upload custom_flex append_sub_images">
                                                    <?php $__currentLoopData = $trip->activityImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="profile_picture">
                                                            <div class="profile_image">
                                                                <!--begin::Image input-->
                                                                <div class="image-input image-input-outline"
                                                                    data-kt-image-input="true">
                                                                    <!--begin::Image preview wrapper-->
                                                                    <div class="image-input-wrapper">
                                                                        <?php if($item->image != null): ?>
                                                                            <img class="input_image_field"
                                                                                src="<?php echo e(asset('website/' . $item->image)); ?>"
                                                                                data-original-src="<?php echo e(asset('website/' . $item->image)); ?>">
                                                                        <?php else: ?>
                                                                            <img class="input_image_field"
                                                                                src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"
                                                                                data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png">
                                                                        <?php endif; ?>

                                                                    </div>
                                                                    <!--end::Image preview wrapper-->

                                                                    <!--begin::Edit button-->
                                                                    <label
                                                                        class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                        data-kt-image-input-action="change"
                                                                        data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                        title="Change avatar">
                                                                        <i class="ki-duotone ki-pencil fs-6"><span
                                                                                class="path1"></span><span
                                                                                class="path2"></span></i>

                                                                        <!--begin::Inputs-->
                                                                        <input type="file"
                                                                            name="activity_images[<?php echo e($item->id); ?>]"
                                                                            accept=".png, .jpg, .jpeg"
                                                                            class="custom_file_input" />
                                                                        <input type="hidden"
                                                                            name="activity_image_exist[<?php echo e($item->id); ?>]"
                                                                            value="<?php echo e($item->id); ?>" />
                                                                        
                                                                        <!--end::Inputs-->
                                                                    </label>
                                                                    <!--end::Edit button-->

                                                                    <!--begin::Cancel button-->
                                                                    <span
                                                                        class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                        data-kt-image-input-action="cancel"
                                                                        data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                        title="Cancel avatar">
                                                                        <i class="ki-outline ki-cross fs-3"></i>
                                                                    </span>
                                                                    <!--end::Cancel button-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="add_sub_section">
                                                    <button type="button" class="btn btn_grey append_images"><i
                                                            class="fa-solid fa-plus"></i>Add More Images</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section custom_cards_design boat_specs_section">
                                        <h1>Section 07: Boat Specification</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Kicker</label>
                                                    <input type="text" name="main_header_kicker"
                                                        class="form-control myinput" placeholder="kicker"
                                                        value="<?php echo e(@$trip->boatSpec->kicker ?? ''); ?>" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" name="main_header_heading"
                                                        class="form-control myinput" placeholder="Discover Thailand"
                                                        value="<?php echo e(@$trip->boatSpec->heading ?? ''); ?>" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Sub Heading</label>
                                                    <input type="text" name="main_header_sub_heading"
                                                        class="form-control myinput"
                                                        value="<?php echo e(@$trip->boatSpec->sub_heading ?? ''); ?>"
                                                        placeholder="From 23 Jan 2023 to 30 Jan 2023 on board SUPER YACHT"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6"></div>
                                            <div class="col-md-12">
                                                <div class="txt_field txt_description">
                                                    <label>Description</label>
                                                    <textarea class="form-control myinput" placeholder="type here" name="main_header_description" rows="3"
                                                        placeholder="" readonly><?php echo e(@$trip->boatSpec->description ?? ''); ?></textarea>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <h3>Images</h3>
                                                <div class="custom_profile_upload custom_flex append_sub_images">
                                                    <?php $__currentLoopData = $trip->boatSpecImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="profile_picture">
                                                            <div class="profile_image">
                                                                <!--begin::Image input-->
                                                                <div class="image-input image-input-outline"
                                                                    data-kt-image-input="true">
                                                                    <!--begin::Image preview wrapper-->
                                                                    <div class="image-input-wrapper">
                                                                        <?php if($item->image != null): ?>
                                                                            <img class="input_image_field"
                                                                                src="<?php echo e(asset('website/' . $item->image)); ?>"
                                                                                data-original-src="<?php echo e(asset('website/' . $item->image)); ?>">
                                                                        <?php else: ?>
                                                                            <img class="input_image_field"
                                                                                src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"
                                                                                data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png">
                                                                        <?php endif; ?>

                                                                    </div>
                                                                    <!--end::Image preview wrapper-->

                                                                    <!--begin::Edit button-->
                                                                    <label
                                                                        class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                        data-kt-image-input-action="change"
                                                                        data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                        title="Change avatar">
                                                                        <i class="ki-duotone ki-pencil fs-6"><span
                                                                                class="path1"></span><span
                                                                                class="path2"></span></i>

                                                                        <!--begin::Inputs-->
                                                                        <input type="file"
                                                                            name="boat_specs_images[<?php echo e($item->id); ?>]"
                                                                            accept=".png, .jpg, .jpeg"
                                                                            class="custom_file_input" />
                                                                        <input type="hidden"
                                                                            name="boat_specs_image_exist[<?php echo e($item->id); ?>]"
                                                                            value="<?php echo e($item->id); ?>" />
                                                                        
                                                                        <!--end::Inputs-->
                                                                    </label>
                                                                    <!--end::Edit button-->

                                                                    <!--begin::Cancel button-->
                                                                    <span
                                                                        class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                        data-kt-image-input-action="cancel"
                                                                        data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                        title="Cancel avatar">
                                                                        <i class="ki-outline ki-cross fs-3"></i>
                                                                    </span>
                                                                    <!--end::Cancel button-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="add_sub_section">
                                                    <button type="button" class="btn btn_grey append_images"><i
                                                            class="fa-solid fa-plus"></i>Add More Images</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section custom_cards_design safety_on_board_section">
                                        <h1>Section 08: Safety On Board</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Kicker</label>
                                                    <input type="text" name="safety_on_board_kicker"
                                                        class="form-control myinput" placeholder="kicker"
                                                        value="<?php echo e(@$trip->safetyOnBoard->kicker ?? ''); ?>" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" name="safety_on_board_heading"
                                                        class="form-control myinput" placeholder="Discover Thailand"
                                                        value="<?php echo e(@$trip->safetyOnBoard->heading ?? ''); ?>" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Sub Heading</label>
                                                    <input type="text" name="safety_on_board_sub_heading"
                                                        class="form-control myinput"
                                                        value="<?php echo e(@$trip->safetyOnBoard->sub_heading ?? ''); ?>"
                                                        placeholder="From 23 Jan 2023 to 30 Jan 2023 on board SUPER YACHT"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6"></div>
                                            <div class="col-md-12">
                                                <div class="txt_field txt_description">
                                                    <label>Description</label>
                                                    <textarea class="form-control myinput" placeholder="type here" name="safety_on_board_description" rows="3"
                                                        placeholder="" readonly><?php echo e(@$trip->safetyOnBoard->description ?? ''); ?></textarea>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <h3>Images</h3>
                                                <div class="custom_profile_upload custom_flex append_sub_images">
                                                    <?php $__currentLoopData = $trip->safetyOnBoardImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="profile_picture">
                                                            <div class="profile_image">
                                                                <!--begin::Image input-->
                                                                <div class="image-input image-input-outline"
                                                                    data-kt-image-input="true">
                                                                    <!--begin::Image preview wrapper-->
                                                                    <div class="image-input-wrapper">
                                                                        <?php if($item->image != null): ?>
                                                                            <img class="input_image_field"
                                                                                src="<?php echo e(asset('website/' . $item->image)); ?>"
                                                                                data-original-src="<?php echo e(asset('website/' . $item->image)); ?>">
                                                                        <?php else: ?>
                                                                            <img class="input_image_field"
                                                                                src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"
                                                                                data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png">
                                                                        <?php endif; ?>

                                                                    </div>
                                                                    <!--end::Image preview wrapper-->

                                                                    <!--begin::Edit button-->
                                                                    <label
                                                                        class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                        data-kt-image-input-action="change"
                                                                        data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                        title="Change avatar">
                                                                        <i class="ki-duotone ki-pencil fs-6"><span
                                                                                class="path1"></span><span
                                                                                class="path2"></span></i>

                                                                        <!--begin::Inputs-->
                                                                        <input type="file"
                                                                            name="safety_on_board_images[<?php echo e($item->id); ?>]"
                                                                            accept=".png, .jpg, .jpeg"
                                                                            class="custom_file_input" />
                                                                        <input type="hidden"
                                                                            name="safety_on_board_image_exist[<?php echo e($item->id); ?>]"
                                                                            value="<?php echo e($item->id); ?>" />
                                                                        
                                                                        <!--end::Inputs-->
                                                                    </label>
                                                                    <!--end::Edit button-->

                                                                    <!--begin::Cancel button-->
                                                                    <span
                                                                        class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                        data-kt-image-input-action="cancel"
                                                                        data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                        title="Cancel avatar">
                                                                        <i class="ki-outline ki-cross fs-3"></i>
                                                                    </span>
                                                                    <!--end::Cancel button-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="add_sub_section">
                                                    <button type="button" class="btn btn_grey append_images"><i
                                                            class="fa-solid fa-plus"></i>Add More Images</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="upload_btn">
                                        <button type="submit" class="btn btn_dark_green">Upload</button>
                                        <a href="<?php echo e(url('itinerary_overview')); ?>" class="btn btn_grey">Preview</a>
                                        <a href="#!" class="btn btn_transparent">Cancel</a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA0z3VFl1wO12-FWBFTC1_fdfOMJgaL9R4&libraries=places&callback=initMap"
        defer></script>
    <script>
        $(document).ready(function() {
            var count = 1;
            //        trip detail section 2 append sub section
            $(".cms_section .add_sub_section .append_itenary_cards").click(function() {
                count++;
                $(this).closest(".cms_section").find(".append_sub_section").append(
                    '<div class="sub_sections"><h2>Sub Section 0' + count +
                    '</h2><div class="cms_section custom_cards_design"><div class="row custom_row"><div class="col-md-6"> <div class="txt_field"><label>Number</label><input class="form-control myinput" type="number" value="01" readonly></div></div>' +
                    '<div class="col-md-6"><div class="txt_field"> <label>Duration</label> <input class="form-control myinput" type="text" value="Day" readonly> </div> </div>' +
                    '<div class="col-md-6"><h3>Location</h3><div class="txt_field custom_time"><label>Start Point:</label><input class="form-control myinput" type="text" value="Phuket" readonly></div></div>' +
                    '<div class="col-md-6"><div class="txt_field custom_time custom_margin"><label>End Point:</label><input class="form-control myinput" type="text" value="Phang Nga Bay" readonly> </div></div>' +
                    '<div class="col-md-6"><div class="txt_field"><label>Time</label><input class="form-control myinput" type="text" value="26nm - 2h 15m - Embark at midday." readonly></div></div>' +
                    '<div class="col-md-6"></div><div class="col-md-12"><div class="txt_field txt_description"><label>Description:</label><textarea class="form-control myinput" rows="4" placeholder="Type here" readonly></textarea></div></div></div></div></div>'
                );
            });

            //        trip detail section 3 append sub section
            // $(document).on("click", ".cms_section .add_sub_section .append_stop_detail_cards", function() {
            //     count++;
            //     $(this).closest(".cms_section").find(".append_sub_section").append(
            //         '<div class="sub_sections appended_section"><h2>Sub Section 0' + count +
            //         '</h2><div class="cms_section custom_cards_design"><div class="row custom_row"><div class="col-md-6"> <div class="txt_field"> <label>Number</label> <input class="form-control myinput" type="number" value="01" readonly> </div> </div>' +
            //         '<div class="col-md-6"><div class="txt_field"><label>Duration</label><input class="form-control myinput" type="text" value="Day" readonly></div> </div>' +
            //         '<div class="col-md-6"><div class="txt_field"><label>Date</label><input class="form-control myinput" type="text" value="Monday, 23 Jan 2023" readonly> </div> </div>' +
            //         '<div class="col-md-6"></div><div class="col-md-6"><h3>Location</h3><div class="txt_field custom_time"> <label>Start Point:</label> <input class="form-control myinput" type="text" value="Phuket" readonly> </div> </div>' +
            //         '<div class="col-md-6"><div class="txt_field custom_time custom_margin"><label>End Point:</label><input class="form-control myinput" type="text" value="Phang Nga Bay" readonly> </div> </div>' +
            //         '<div class="col-md-6"><div class="txt_field"> <label>Time</label><input class="form-control myinput" type="text" value="26nm - 2h 15m - Embark at midday." readonly> </div> </div>' +
            //         '<div class="col-md-6"></div><div class="col-md-12"> <div class="txt_field txt_description"><label>Description:</label> <textarea class="form-control myinput" rows="4" placeholder="Type here" readonly></textarea></div></div>' +
            //         '<div class="col-md-12"><h3>Images</h3> <div class="custom_profile_upload custom_flex"> <div class="profile_picture"> <div class="profile_image"><div class="image-input image-input-outline" data-kt-image-input="true"> <div class="image-input-wrapper"> <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"> </div>' +
            //         '<label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click"title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i> <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/> <input type="hidden" name="avatar_remove" /> </label>' +
            //         '<span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click"title="Cancel avatar"> <i class="ki-outline ki-cross fs-3"></i> </span> </div> </div> </div>' +
            //         '<div class="profile_picture"><div class="profile_image"><div class="image-input image-input-outline" data-kt-image-input="true"> <div class="image-input-wrapper"> <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"> </div>' +
            //         '<label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="change"data-bs-toggle="tooltip" data-bs-dismiss="click"title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i> <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/> <input type="hidden" name="avatar_remove" /> </label>' +
            //         '<span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click"title="Cancel avatar"> <i class="ki-outline ki-cross fs-3"></i> </span> </div> </div> </div> </div></div> ' +
            //         '<div class="col-md-12"><div class="add_sub_section"><button type="button" class="btn btn_grey append_images"><i class="fa-solid fa-plus"></i>Add More Images</button></div></div> </div> </div></div>'
            //     );
            // });

            //        trip detail section 4 append sub section
            function updateCrewDetails() {
                var selectedNames = []; // Array to store selected names
                var $cmsSection = $(".cms_section"); // Store reference to the .cms_section element

                // Loop through each checked checkbox and get the corresponding student name and image
                $('input.append_checkbox_item:checked').each(function() {
                    var crew_name = $(this).closest('.custom_check_box').find('label').html();
                    var image_url = "<?php echo e(asset('website')); ?>/" + $(this).data('profile');
                    if (image_url == '' || image_url == null) {
                        image_url = "<?php echo e(asset('website')); ?>/assets/images/crew_image.png";
                    }
                    selectedNames.push({
                        name: crew_name,
                        image: image_url
                    }); // Store both name and image
                });

                // Clear the existing crew details to avoid duplication
                $cmsSection.find(".append_crew_detail").empty();

                // Append each selected name and corresponding image
                selectedNames.forEach(function(item) {
                    $cmsSection.find(".append_crew_detail").append(
                        '<div class="crew_listing custom_flex">' +
                        '<div class="crew_image"><img src="' + item.image +
                        '" alt="Profile Image"></div>' + // Use dynamic image URL
                        '<h4>' + item.name + '</h4>' + '</div>'
                    );
                });
            }

            // Initial update on page load for already checked checkboxes
            updateCrewDetails();

            // Update crew details when checkboxes change
            $('input.append_checkbox_item').change(function() {
                updateCrewDetails();
            });
            // $(".cms_section .add_sub_section .crew_detail").click(function() {
            //     var selectedNames = []; // Array to store selected names
            //     var $cmsSection = $(this).closest(
            //         ".cms_section"); // Store reference to the .cms_section element

            //     // Loop through each checked checkbox and get the corresponding student name
            //     $('input.append_checkbox_item:checked').each(function() {
            //         var student_name = $(this).closest('.custom_check_box').find('label').html();
            //         selectedNames.push(student_name);
            //     });

            //     // Append each selected name
            //     selectedNames.forEach(function(name) {
            //         $cmsSection.find(".append_crew_detail").append(
            //             '<div class="crew_listing custom_flex"><div class="crew_image"><img src="<?php echo e(asset('website')); ?>/assets/images/crew_image.png"></div>' +
            //             '<h4>' + name +
            //             '</h4><a href="#!"><i class="fa fa-solid fa-close"></i></a></div>'
            //         );
            //     });

            //     // Uncheck all checkboxes
            //     $('input.append_checkbox_item').prop('checked', false);
            // });

            // $(document).on("click", ".append_crew_detail .crew_listing a", function() {
            //     $(this).closest(".crew_listing").remove();
            // });


            //        trip detail section 5 append breakfast sub section
            $(document).on("click", ".cms_section .add_sub_section .add_breakfast", function() {
                // currentnewMenu = $(this).closest(".append_sub_section").find(".sub_sections").length;
                currentnewMenu = $(this).closest(".sub_sections").data("menu");
                console.log("current new mwnu: " + currentnewMenu);

                $(this).closest(".cms_section").find(".append_menu").each(function() {
                    var subSectionCount = "new_" + ($(this).find(".append_items").length + 1);
                    console.log("append_items count: " + subSectionCount);
                    $(this).closest(".cms_section").find(".append_menu").append(
                        '<div class="append_items appended_section"><div class="add_image"><h2>Image</h2><div class="profile_picture"><div class="profile_image"> <div class="image-input image-input-outline" data-kt-image-input="true"> <div class="image-input-wrapper"> <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"> </div>' +
                        '<label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="change"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>' +
                        '<input type="file" name="menu_plans[' + currentnewMenu +
                        '][details][' + subSectionCount +
                        '][image]" accept=".png, .jpg, .jpeg" class="custom_file_input"/>' +
                        '<input type="hidden" name="avatar_remove" /></label>' +
                        '<span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="cancel"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Cancel avatar"> <i class="ki-outline ki-cross fs-3"></i> </span> </div> </div> </div> </div>' +
                        '<div class="txt_field"><label>Breakfast Name</label><input class="form-control myinput" type="text" name="menu_plans[' +
                        currentnewMenu + '][details][' + subSectionCount +
                        '][title]" placeholder="Day" readonly> </div> ' +
                        '<div class="txt_field txt_description"><label>Breakfast Description</label><textarea class="form-control myinput" name="menu_plans[' +
                        currentnewMenu + '][details][' + subSectionCount +
                        '][text]" rows="3" placeholder="Type Here" readonly></textarea></div></div>'
                    );
                });

            });

            //        trip detail section 5 append breakfast name sub section
            $(".cms_section .add_sub_section .append_breakfast_name").click(function() {
                $(this).closest(".cms_section").find(".append_sub_section").each(function() {
                    var newMenu = "new_" + ($(this).find(".sub_sections").length + 1);

                    console.log("Sub-sections count: " + newMenu);
                    $(this).closest(".cms_section").find(".append_sub_section").append(
                        '<div class="sub_sections appended_section" data-menu="' + newMenu +
                        '"><h2>Sub Section 01</h2><div class="cms_section custom_cards_design" ><div class="row custom_row">' +
                        '<div class="col-md-12"><div class="txt_field"> <label>Name</label> <input class="form-control myinput" type="text" name="menu_plans[' +
                        newMenu + '][name]" palceholder="Break Fast" readonly> </div> </div>' +
                        '<div class="col-md-12"><div class="add_breakfast_section"><div class="append_menu"><div class="append_items"> <div class="add_image"> <h2>Image</h2> <div class="profile_picture"> <div class="profile_image">' +
                        '<div class="image-input image-input-outline" data-kt-image-input="true"><div class="image-input-wrapper"> <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"> </div>' +
                        '<label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="change"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>' +
                        '<input type="file" name="menu_plans[' + newMenu +
                        '][details][new_1][image]" accept=".png, .jpg, .jpeg" class="custom_file_input"/>' +
                        '<input type="hidden" name="avatar_remove" />' +
                        '</label>' +
                        '<span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="cancel"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Cancel avatar"> <i class="ki-outline ki-cross fs-3"></i> </span> </div> </div> </div> </div>' +
                        '<div class="txt_field"><label>Breakfast Name</label><input class="form-control myinput" type="text" name="menu_plans[' +
                        newMenu +
                        '][details][new_1][title]" placeholder="Day" readonly></div><div class="txt_field txt_description"><label>Breakfast Description</label><textarea class="form-control myinput" name="menu_plans[' +
                        newMenu +
                        '][details][new_1][text]" rows="3" placeholder="Type here" readonly></textarea></div></div>' +
                        '</div></div></div>' +
                        '<div class="col-md-12"><div class="add_sub_section add_btn"><button type="button" class="btn btn_grey add_breakfast"><i class="fa-solid fa-plus"></i></button></div></div></div></div></div>'
                    );
                });
            });

            //        trip detail section 6 append activities section
            $(".append_activity_text .add_sub_section .add_text").click(function() {
                $(this).closest(".append_activity_text").find(".custom_text").append(
                    '<div class="txt_field txt_description"><label>Text</label><textarea name="texts[]" class="form-control myinput" rows="1" placeholder="Type Here" readonly></textarea></div>'
                );
            });

            //         Add More Images Section jquery

            $(document).on("click", ".add_sub_section .append_images", function() {
                var section = $(this).closest(".cms_section, .append_sub_section .sub_sections");
                var profilePictureCount = section.find(".profile_picture").length + 1;
                console.log(profilePictureCount);
                // Conditional logic to change the name attribute for different sections
                if (section.hasClass('main_header_section')) {
                    inputFileName = "main_header_images";
                } else if (section.hasClass('safety_on_board_section')) {
                    inputFileName = "safety_on_board_images";
                } else if (section.hasClass('boat_specs_section')) {
                    inputFileName = "boat_specs_images";
                } else if (section.hasClass('activity_section')) {
                    inputFileName = "activity_images";
                } else if (section.hasClass('location_section')) {
                    currentlocations = section.data("location");
                    console.log("current location: " + currentlocations);
                    inputFileName = 'itinerary[' + currentlocations + '][images]'
                } else {
                    inputFileName = "image"
                }
                var inputFileNameWithCount = inputFileName + '[new_' + profilePictureCount + ']';
                section.find(
                    ".append_sub_images").append(
                    '<div class="appended_section profile_picture"><div class="profile_image"><div class="image-input image-input-outline" data-kt-image-input="true"><div class="image-input-wrapper"><img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"></div>' +
                    '<label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i><input type="file" name="' +
                    inputFileNameWithCount +
                    '"  accept=".png, .jpg, .jpeg" class="custom_file_input"/>' +
                    '<input type="hidden" name="avatar_remove" /></label><span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Cancel avatar"><i class="ki-outline ki-cross fs-3"></i></span></div></div></div>'
                );
            });

            $(".cms_edit_btn button").click(function() {
                var allInputField = $(this).closest("form").find(".myinput");
                $(allInputField).each(function() {
                    var input = $(this);
                    input.removeAttr("readonly", !input.attr("readonly"));
                    if (input.attr("readonly")) {}
                });
            });

            //         // Re-initialize the input[type="file"] functionality for newly appended sections
            $(document).on('change', '.appended_section input[type="file"]', function() {
                var reader = new FileReader();
                var $imageInputWrapper = $(this).closest('.appended_section .image-input').find(
                    '.image-input-wrapper');

                reader.onload = function(e) {
                    $imageInputWrapper.html('<img class="custom_img" src="' + e.target.result +
                        '" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"/>'
                    );
                }

                // Load the selected image into the preview
                reader.readAsDataURL(this.files[0]);
            });

            // Add event listener to remove the image when clicking the 'cancel' button
            $(document).on('click', '.appended_section [data-kt-image-input-action="cancel"]', function() {
                var newImg = $(this).closest('.appended_section .image-input').find(
                    '.image-input-wrapper img.custom_img');
                var originalSrc = newImg.attr('data-original-src');
                newImg.attr('src', originalSrc);
            });
        })
    </script>
    

    <script>
        let routeCount = $(".route-container").length;
        let markers = []; // To store markers on the map

        function initMap() {
            const map = new google.maps.Map($("#map")[0], {
                zoom: 6,
                center: {
                    lat: 41.85,
                    lng: -87.65
                }, // Default center
            });

            // Initialize autocomplete for start and end fields
            initAutocomplete(map);

            // Event listener for the Submit button
            $("#showlocationsonmap").on("click", () => {
                displayPinsOnMap(map);
            });

            // Event listener for the Add More button
            $("#addMore").on("click", addRoute);
            $(document).on('change', '.end-point', function() {
                displayPinsOnMap(map);
            });
        }

        // Initialize Autocomplete for start and end fields
        function initAutocomplete(map) {
            $("input.start-point, input.end-point").each(function() {
                const input = this;
                const autocomplete = new google.maps.places.Autocomplete(input);

                // Set the event listener when a place is selected
                autocomplete.addListener("place_changed", function() {
                    const place = autocomplete.getPlace();
                    if (!place.geometry) {
                        console.error("No geometry found for the place");
                        return;
                    }

                    // Fill the latitude and longitude fields
                    const lat = place.geometry.location.lat();
                    const lng = place.geometry.location.lng();
                    const latField = $(input).hasClass('start-point') ? '.start-lat' : '.end-lat';
                    const lngField = $(input).hasClass('start-point') ? '.start-lng' : '.end-lng';

                    $(latField).val(lat);
                    $(lngField).val(lng);

                    // Optionally place a marker on the map
                    new google.maps.Marker({
                        position: place.geometry.location,
                        map: map,
                    });

                    map.setCenter(place.geometry.location); // Center the map on the selected location
                });
            });
        }

        // Function to display pins (markers) for start, end, and waypoints
        function displayPinsOnMap(map) {
            const routeContainers = $(".route-container");

            // Clear previous markers
            markers.forEach(marker => marker.setMap(null));
            markers = [];

            // Loop through each route container
            routeContainers.each((index, routeContainer) => {
                const startPoint = $(routeContainer).find(".start-point").val();
                const endPoint = $(routeContainer).find(".end-point").val();

                if (startPoint && index === 0) {
                    geocodeAddress(startPoint, 'Start', '.start-lat', '.start-lng', map);
                }

                if (endPoint) {
                    geocodeAddress(endPoint, 'End', '.end-lat', '.end-lng', map);
                }
            });
        }

        // Geocode the address to get the LatLng and place a marker
        function geocodeAddress(address, title, latField, lngField, map) {
            const geocoder = new google.maps.Geocoder();
            geocoder.geocode({
                address: address
            }, (results, status) => {
                if (status === google.maps.GeocoderStatus.OK) {
                    const location = results[0].geometry.location;
                    $(latField).val(location.lat());
                    $(lngField).val(location.lng());

                    new google.maps.Marker({
                        position: location,
                        map: map,
                        title: title,
                    });

                    map.setCenter(location); // Optionally, center the map on the first marker
                } else {
                    console.error("Geocode was not successful: " + status);
                }
            });
        }

        // Add a new route (with dynamic start/end points)
        function addRoute() {
            routeCount++;
            const lastRouteEndPoint = $(`#route${routeCount - 1} .end-point`).val();

            const newRouteDiv = $(`
            <div class="sub_sections route-container" id="route${routeCount}">
                <h2>Sub Section ${routeCount}</h2>
                <div class="cms_section custom_cards_design location_section" data-location="${routeCount}_new">
                    <div class="row custom_row">
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Number</label>
                                <input name="itinerary[${routeCount}_new][number]" class="form-control myinput" type="number" value="${routeCount}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Duration</label>
                                <input name="itinerary[${routeCount}_new][duration]" class="form-control myinput" type="text" placeholder="Day">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Date</label>
                                <input name="itinerary[${routeCount}_new][date]" class="form-control myinput" type="text" placeholder="Monday, 23 Jan 2023">
                            </div>
                        </div>
                        <div class="col-md-6"></div>
                        <div class="col-md-6">
                            <h3>Location</h3>
                            <div class="txt_field custom_time">
                                <label>Start Point:</label>
                                <input name="itinerary[${routeCount}_new][start_point]" class="form-control myinput start-point" id="start${routeCount}" type="text" placeholder="Enter starting location">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="txt_field custom_time custom_margin">
                                <label>End Point:</label>
                                <input name="itinerary[${routeCount}_new][end_point]" class="form-control myinput end-point" id="end${routeCount}" type="text" placeholder="Enter destination">
                            </div>
                        </div>
                        <input name="itinerary[${routeCount}_new][start_lat]" type="hidden" class="start-lat" id="start-lat${routeCount}">
                        <input name="itinerary[${routeCount}_new][start_lng]" type="hidden" class="start-lng" id="start-lng${routeCount}">
                        <input name="itinerary[${routeCount}_new][end_lat]" type="hidden" class="end-lat" id="end-lat${routeCount}">
                        <input name="itinerary[${routeCount}_new][end_lng]" type="hidden" class="end-lng" id="end-lng${routeCount}">
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Time</label>
                                <input name="itinerary[${routeCount}_new][time]" class="form-control myinput" type="text" placeholder="26nm - 2h 15m - Embark at midday.">
                            </div>
                        </div>
                        <div class="col-md-6"></div>
                        <div class="col-md-12">
                            <div class="txt_field txt_description">
                                <label>Description:</label>
                                <textarea name="itinerary[${routeCount}_new][description]" class="form-control myinput" rows="4" placeholder="Type here"></textarea>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <h3>Image</h3>
                            <div class="custom_profile_upload custom_flex append_sub_images">
                                <div class="appended_section profile_picture"><div class="profile_image"><div class="image-input image-input-outline" data-kt-image-input="true"><div class="image-input-wrapper"><img class="input_image_field" src="http://127.0.0.1:8000/website/assets/images/cmsimage1.png" data-original-src="http://127.0.0.1:8000/website/assets/images/cmsimage1.png"></div><label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i><input type="file" name="itinerary[${routeCount}_new][images][new_1]" accept=".png, .jpg, .jpeg" class="custom_file_input"><input type="hidden" name="avatar_remove"></label><span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Cancel avatar"><i class="ki-outline ki-cross fs-3"></i></span></div></div></div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="add_sub_section">
                                <span class="remove-btn" onclick="removeRoute('route${routeCount}')">X</span>
                                <button type="button" class="btn btn_grey append_images"><i class="fa-solid fa-plus"></i>Add More Images</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>`);

            $("#routes").append(newRouteDiv);

            // Set the start point of this new route as the end point of the previous route
            newRouteDiv.find(".start-point").val(lastRouteEndPoint);

            initAutocomplete(map); // Reinitialize autocomplete after adding a new route
        }

        // Remove a specific route
        function removeRoute(routeId) {
            $(`#${routeId}`).remove();
            updateRouteStartPoints();
        }

        // Update the start points of routes after one is removed
        function updateRouteStartPoints() {
            const routeContainers = $(".route-container");
            routeContainers.each((index, routeContainer) => {
                const startInput = $(routeContainer).find(".start-point");
                if (index === 0) {
                    startInput.prop("disabled", false); // First route: allow user to choose start point
                } else {
                    const prevEndPoint = $(routeContainers[index - 1]).find(".end-point");
                    startInput.val(prevEndPoint.val()); // Set start point to the previous route's end point
                    startInput.prop("disabled", true); // Disable to prevent editing
                }
            });
        }

        window.initMap = initMap;
    </script>
    
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\Owner\TripDetailManagement\trip_detail.blade.php ENDPATH**/ ?>