

<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="payment_management">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_table custom_cards_design">
                        <div class="custom_justify_between">
                            <h1>Crew Management</h1>
                            <button type="button" data-bs-toggle="modal" data-bs-target="#add_crew"
                                class="btn btn_dark_green">Add Crew</button>
                        </div>
                        <table class="table myTable datatable">
                            <thead>
                                <tr>
                                    <th>Sr#</th>
                                    <th>Name</th>
                                    <th>Role</th>
                                    <th>Description</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $crews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $crew): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr id="crew-<?php echo e($crew->id); ?>">
                                        <td><?php echo e($loop->iteration); ?></td>
                                        <td><?php echo e($crew->name ?? ''); ?></td>
                                        <td><?php echo e($crew->role ?? ''); ?></td>
                                        <td><?php echo e(Str::limit($crew->description ?? '', 50, '...')); ?></td>
                                        <td>
                                            <span class="status-label <?php echo e($crew->status === 1 ? 'success' : 'danger'); ?>">
                                                <?php echo e($crew->status === 1 ? 'Active' : 'In-Active'); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="dropdown-toggle" type="button" id="dropdownMenuButton11"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                    <li><a href="<?php echo e(route('crews.show', [$crew->id])); ?>"
                                                            class="dropdown-item"><i class="fa-solid fa-eye"></i>View</a>
                                                    </li>
                                                    
                                                    <li>
                                                        <a class="dropdown-item toggle-status"
                                                            data-slug="<?php echo e($crew->id); ?>">
                                                            <i
                                                                class="fa-solid <?php echo e($crew->status === 1 ? 'fa-close' : 'fa-check'); ?>"></i>
                                                            <?php echo e($crew->status === 1 ? 'Deactivate' : 'Activate'); ?></a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>


    

    <div class="modal fade custom_modal" id="add_crew" tabindex="-1" aria-labelledby="createModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title" id="createModalLabel">Create Crew</h1>
                    
                </div>
                <div class="modal-body ">
                    <form method="post" action="<?php echo e(route('crews.store')); ?>" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="row custom_row">
                            <div class="col-md-6">
                                <div class="txt_field">
                                    <label>Name:</label>
                                    <input type="text" name="name" class="form-control" placeholder="Type Here"
                                        required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="txt_field">
                                    <label>Role:</label>
                                    <input type="text" name="role" class="form-control" placeholder="Type Here"
                                        required>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field">
                                    <label>Description:</label>
                                    <textarea class="form-control" name="description" rows="1" placeholder="Type Here" required></textarea>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="txt_field profile_picture">
                                    <label>Add Image:</label>
                                    <div class="profile_image">
                                        <!--begin::Image input-->
                                        <div class="image-input" data-kt-image-input="true">
                                            <!--begin::Image preview wrapper-->
                                            <div class="image-input-wrapper">
                                                <img class="input_image_field"
                                                    src="<?php echo e(asset('website')); ?>/assets/images/crew_img.png"
                                                    data-original-src="<?php echo e(asset('website')); ?>/assets/images/crew_img.png">
                                            </div>
                                            <!--end::Image preview wrapper-->

                                            <!--begin::Edit button-->
                                            <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                data-bs-dismiss="click" title="Change avatar">
                                                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                        class="path2"></span></i>

                                                <!--begin::Inputs-->
                                                <input type="file" name="image" accept=".png, .jpg, .jpeg" required
                                                    class="custom_file_input" />
                                                <input type="hidden" name="avatar_remove" />
                                                <!--end::Inputs-->
                                            </label>
                                            <!--end::Edit button-->

                                            <!--begin::Cancel button-->
                                            <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                data-bs-dismiss="click" title="Cancel avatar">
                                                <i class="ki-outline ki-cross fs-3"></i>
                                            </span>
                                            <!--end::Cancel button-->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <button type="submit" class="btn btn_dark_green">Create</button>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn_transparent" data-bs-dismiss="modal">Cancel</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            $('.toggle-status').on('click', function() {
                let button = $(this);
                let crewSlug = button.data('slug');
                let row = $(`#crew-${crewSlug}`);

                $.ajax({
                    url: "<?php echo e(url('crews')); ?>" + `/${crewSlug}/toggle-status`,
                    type: 'GET',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>',
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            // Update the button text and status
                            let newStatus = response.new_status;
                            button.html(
                                `<i class="fa-solid ${
                        newStatus === 1 ? 'fa-close' : 'fa-check'
                    }"></i> ${
                        newStatus === 1 ? 'Deactivate' : 'Activate'
                    }`
                            );
                            button.data('status', newStatus);

                            // Update the status label in the row
                            let statusLabel = row.find('.status-label');
                            statusLabel
                                .text(newStatus === 1 ? 'Active' : 'In-Active') // Capitalize the status
                                .removeClass('success danger')
                                .addClass(newStatus === 1 ? 'success' : 'danger');

                            // Show SweetAlert success message
                            Swal.fire({
                                title: 'Success!',
                                text: `Crew ${newStatus === 1 ? 'Activated' : 'Deactivated'} successfully!`,
                                icon: 'success',
                                confirmButtonText: 'OK',
                            });
                        } else {
                            // Show SweetAlert error message
                            Swal.fire({
                                title: 'Error!',
                                text: 'Failed to update status. Please try again.',
                                icon: 'error',
                                confirmButtonText: 'OK',
                            });
                        }
                    },
                    error: function() {
                        // Show SweetAlert error message
                        Swal.fire({
                            title: 'Error!',
                            text: 'An error occurred. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK',
                        });
                    },
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\crews\index.blade.php ENDPATH**/ ?>