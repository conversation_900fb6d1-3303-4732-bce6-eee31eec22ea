

<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="trip_management">
        <div class="container-fluid">
            <div class="row custom_row">
                <div class="col-md-8">
                    <form>
                        <div class="custom_cards_design">
                            <div class="row custom_row">
                                <div class="col-md-12">
                                    <h1>Trip Management</h1>
                                </div>
                                <div class="col-md-12">
                                    <div class="edit_user_trip custom_justify_between">
                                        <div class="profile_image">
                                            <!--begin::Image input-->
                                            <div class="image-input image-input-circle" data-kt-image-input="true">
                                                <!--begin::Image preview wrapper-->
                                                <div class="image-input-wrapper">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/building.png">
                                                </div>
                                                <!--end::Image preview wrapper-->

                                                <!--begin::Edit button-->
                                                <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                       data-kt-image-input-action="change"
                                                       data-bs-toggle="tooltip"
                                                       data-bs-dismiss="click"
                                                       title="Change avatar">
                                                    <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                    <!--begin::Inputs-->
                                                    <input type="file" name="avatar" accept=".png, .jpg, .jpeg" />
                                                    <input type="hidden" name="avatar_remove" />
                                                    <!--end::Inputs-->
                                                </label>
                                                <!--end::Edit button-->

                                                <!--begin::Cancel button-->
                                                <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                      data-kt-image-input-action="cancel"
                                                      data-bs-toggle="tooltip"
                                                      data-bs-dismiss="click"
                                                      title="Cancel avatar">
                                                <i class="ki-outline ki-cross fs-3"></i>
                                            </span>
                                                <!--end::Cancel button-->
                                            </div>
                                        </div>
                                        <div class="profile_changes">
                                            <div class="edit_change custom_flex">
                                                <a href='#!' class="btn btn_dark_green edit_profile">Edit</a>
                                                <a href="#!" class="btn btn_transparent">Deactivate</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <h2>Trip Information</h2>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Trip name:</label>
                                        <input type="text" class="form-control myinput" id="" value="Yacht IQ" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">URL:</label>
                                        <input type="text" class="form-control myinput" id="" placeholder="Name Here" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Duration:</label>
                                        <input type="email" class="form-control myinput" id="" value="23Hours" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Package Type:</label>
                                        <input type="tel" class="form-control myinput" id="" value="123456789" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="col-md-4">
                    <div class="user_trip_details">
                        <div class="row custom_row_card custom_row">
                            <div class="col-md-12 custom_column_card">
                                <div class="custom_card">
                                    <h2>Total Stops</h2>
                                    <h1>500</h1>
                                </div>
                            </div>
                            <div class="col-md-12 custom_column_card">
                                <div class="custom_card">
                                    <h2>Starting point</h2>
                                    <h1>100</h1>
                                </div>
                            </div>
                            <div class="col-md-6 custom_column_card">
                                <div class="custom_card">
                                    <h2>Ending point</h2>
                                    <h1>200</h1>
                                </div>
                            </div>
                            <div class="col-md-6 custom_column_card">
                                <div class="custom_card">
                                    <h2>Trip Status</h2>
                                    <span class="success">Active</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="custom_table custom_cards_design">
                        <h1>Trip Summary</h1>
                        <table class="table myTable datatable">
                            <thead>
                            <tr>
                                <th>Stop No</th>
                                <th>Pickup Location</th>
                                <th>Drop off Location</th>
                                <th>Duration</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php for($i=0;$i<6;$i++): ?>
                                <tr>
                                    <td>01</td>
                                    <td>Thailand</td>
                                    <td>Thailand</td>
                                    <td>23h 3min</td>
                                </tr>
                            <?php endfor; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\Admin\TripManagement\trip_view.blade.php ENDPATH**/ ?>