

<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>

<section class="content_management">
    <div class="container-fluid">
        <div class="row custom_row">
            <div class="col-md-12">
                <div class="cms_tabs">
                    <?php echo $__env->make('cms_navbar.cms_navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div>
                    <div>
                        <form action="<?php echo e(route('pricings.update', $pricing->id)); ?>" method="POST" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <div class="row custom_row">
                                <div class="col-md-12">
                                    <div class="cms_edit_btn">
                                        <button type="button" id="edit" class="btn btn_dark_green"><i class="fa-solid fa-pen-to-square"></i>Edit</button>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section custom_cards_design">
                                        <h1>Section: Main Header</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-12">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" class="form-control myinput  <?php $__errorArgs = ['heading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="heading" value="<?php echo e($pricing->heading); ?>">
                                                    <?php $__errorArgs = ['heading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="invalid-feedback"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="txt_field">
                                                    <label>Description</label> 
                                                    <textarea rows="2" class="form-control myinput <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="description"><?php echo e($pricing->description); ?></textarea>
                                                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="invalid-feedback"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <h3>Background Image</h3>
                                                <div class="custom_profile_upload custom_flex">
                                                    <div class="profile_picture">
                                                        <div class="profile_image">
                                                            <!--begin::Image input-->
                                                            <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                <!--begin::Image preview wrapper-->
                                                                <div class="image-input-wrapper">
                                                                    <img class="input_image_field" src="<?php echo e(asset('website/'. $pricing->image)); ?>" data-original-src="<?php echo e(asset('website/'. $pricing->image)); ?>">
                                                                </div>
                                                                <!--end::Image preview wrapper-->

                                                                <!--begin::Edit button-->
                                                                <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon position-absolute top-10 end-0" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Change avatar">
                                                                    <i class="ki-duotone ki-pencil fs-6">
                                                                        <span class="path1"></span>
                                                                        <span class="path2"></span>
                                                                    </i>

                                                                    <!--begin::Inputs-->
                                                                    <input type="file" name="main_image" accept=".png, .jpg, .jpeg" class="custom_file_input" />
                                                                    <!--end::Inputs-->
                                                                </label>
                                                                <!--end::Edit button-->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section custom_cards_design">
                                        <h1>Section: Pricing</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Kicker</label>
                                                    <input type="text" class="form-control myinput <?php $__errorArgs = ['pricing_kicker'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="pricing_kicker" value="<?php echo e($pricing->pricing_kicker); ?>" >
                                                    <?php $__errorArgs = ['pricing_kicker'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="invalid-feedback"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" class="form-control myinput <?php $__errorArgs = ['pricing_heading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" value="<?php echo e($pricing->pricing_heading); ?>" name="pricing_heading">
                                                    <?php $__errorArgs = ['pricing_heading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="invalid-feedback"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="txt_field txt_description">
                                                    <label>Description</label>
                                                    <textarea class="form-control myinput <?php $__errorArgs = ['pricing_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" rows="3" name="pricing_description"><?php echo e($pricing->description); ?></textarea>
                                                    <?php $__errorArgs = ['pricing_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="invalid-feedback"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="txt_field txt_description">
                                                    <label>Button Text</label>
                                                    <input type="text" class="form-control myinput <?php $__errorArgs = ['button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" value="<?php echo e($pricing->button_text); ?>" name="button_text">
                                                    <?php $__errorArgs = ['button_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="invalid-feedback"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="upload_btn">
                                        <button type="submit" class="btn btn_dark_green">Upload</button>
                                        <a href="#home" class="btn btn_transparent">Cancel</a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function () {
            
            // Re-initialize the input[type="file"] functionality for newly appended sections
            $(document).on('change', '.appended_section input[type="file"]', function () {
                var reader = new FileReader();
                var $imageInputWrapper = $(this).closest('.appended_section .image-input').find('.image-input-wrapper');

                reader.onload = function (e) {
                    $imageInputWrapper.html('<img class="custom_img" src="' + e.target.result + '" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"/>');
                }

                // Load the selected image into the preview
                reader.readAsDataURL(this.files[0]);
            });

            // Add event listener to remove the image when clicking the 'cancel' button
            $(document).on('click', '.appended_section [data-kt-image-input-action="cancel"]', function () {
                var newImg = $(this).closest('.appended_section .image-input').find('.image-input-wrapper img.custom_img');
                var originalSrc = newImg.attr('data-original-src');
                newImg.attr('src', originalSrc);
            });

        })
    </script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\pricings\edit.blade.php ENDPATH**/ ?>