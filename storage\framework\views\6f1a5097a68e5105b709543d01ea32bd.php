<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="homepage_section">
        <div class="container-fluid">
            <div class="row custom_row">
                <div class="col-md-12">
                    <div class="homepage_cards">
                        <div class="row">
                            
                                
                            
                            <div class="col-md-12">
                                <div class="cards_wrapper">
                                    <div class="custom_card">
                                        <h4>Total Revenue</h4>
                                        <h1>$12,426</h1>
                                    </div>
                                    <div class="custom_card">
                                        <h4>Total Boat Managers</h4>
                                        <h1>500+</h1>
                                    </div>
                                    <div class="custom_card">
                                        <h4>Active Boat Managers</h4>
                                        <h1>84,382</h1>
                                    </div>
                                    <div class="custom_card">
                                        <h4>Total Boat Managers</h4>
                                        <h1>2,426</h1>
                                    </div>
                                    <div class="custom_card">
                                        <h4>Total Subscriptions Purchase</h4>
                                        <h1>$12,426</h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="card card-bordered custom_cards_design graph_chart">
                        <h2>Monthly Earnings</h2>
                        <canvas id="line-chart" style="height: 250px"></canvas>
                    </div>

                </div>
                
                
                
                
                <div class="col-md-12">
                    <div class="custom_table custom_cards_design">
                        <h2>Users</h2>
                        <div class="custom_scroll_tbl">
                            <table class="table without_pagination_tbl datatable">
                                <thead>
                                    <tr>
                                        <th>Sr#</th>
                                        <th>Company Name</th>
                                        <th>Email</th>
                                        <th>Subscription Type</th>
                                        <th>Total Crew</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($loop->iteration); ?></td>
                                            <td><?php echo e($user->company_name ?? ''); ?></td>
                                            <td>
                                                <a href="javascript:void(0);"><?php echo e($user->email ?? ''); ?></a>
                                            </td>
                                            <td><?php echo e($user->activeSubscription ? $user->activeSubscription->package->name ?? '' : '-'); ?>

                                            </td>
                                            <td><?php echo e($user->crews->count()); ?></td>
                                            <td> <span class="success">Active</span></td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="dropdown-toggle" type="button"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('user-list')): ?>
                                                            <li><a href="<?php echo e(route('users.show', $user->id)); ?>" class="dropdown-item"><i
                                                                        class="fa-solid fa-eye"></i>View</a></li>
                                                        <?php endif; ?>
                                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('user-edit')): ?>
                                                            <li><a href="<?php echo e(route('users.edit', $user->id)); ?>" class="dropdown-item"><i
                                                                        class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                        <?php endif; ?>
                                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check(\Illuminate\Support\Str::slug('user') . '-delete')): ?>
                                                            <form method="POST"
                                                                action="<?php echo e(route('users.destroy', [$user->id])); ?>"
                                                                accept-charset="UTF-8" style="display:inline">
                                                                <?php echo e(method_field('DELETE')); ?>

                                                                <?php echo e(csrf_field()); ?>

                                                                <button type="submit"
                                                                    class="menu-link px-3 user_mng_delete_btn"
                                                                    data-kt-customer-table-filter="delete_row"
                                                                    title="Delete <?php echo e(preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'User')); ?>"
                                                                    onclick="return confirm(&quot;Confirm delete?&quot;)">
                                                                    Delete
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        $(document).ready(function() {
            var dataTable = $('.without_pagination_tbl').DataTable({
                "searching": true,
                "bLengthChange": false,
                "paging": false,
                "info": false,
            });
            $(document).on("input", '.custom_search_box', function() {
                var searchValue = $(this).val();
                dataTable.search(searchValue).draw();
            });
            $(".without_pagination_tbl").DataTable();
        })
    </script>
    
    <script>
        // Pass the PHP data arrays to JavaScript
        var data = <?php echo json_encode($data, 15, 512) ?>; // Subscription amounts for each month
        var labels = <?php echo json_encode($monthsYear, 15, 512) ?>; // Month-Year labels (e.g., "Jan 2025", "Dec 2024", ...)

        // Set up the chart context and gradient
        var lineChart = document.getElementById("line-chart").getContext('2d');
        var gradientOne = lineChart.createLinearGradient(0, 0, 0, 600);
        gradientOne.addColorStop(0, 'rgba(34, 128, 194, 0.5)');
        gradientOne.addColorStop(1, 'rgba(255, 255, 255, 0)');

        // Define the datasets for the chart
        var datasets = [{
            label: 'Admin Commission',
            data: data, // Dynamic data from the backend
            borderColor: '#027F8A',
            fill: 'start',
            backgroundColor: gradientOne,
            tension: 0.4,
            borderWidth: 2,
        }];

        // Create the chart
        new Chart(lineChart, {
            type: 'line',
            data: {
                labels: labels, // Use the dynamic labels (e.g., "Jan 2025", "Dec 2024", ...)
                datasets: datasets
            },
            options: {
                plugins: {
                    tooltip: {
                        callbacks: {
                            title: function(tooltipItems) {
                                const index = tooltipItems[0].dataIndex;
                                return `${labels[index]}`; // Display Month Year in tooltip
                            },
                            label: function(tooltipItem) {
                                return `Value: ${tooltipItem.raw}`; // Show value in tooltip
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: true
                        },
                    },
                    y: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            display: false
                        }
                    }
                }
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\dashboard_index.blade.php ENDPATH**/ ?>