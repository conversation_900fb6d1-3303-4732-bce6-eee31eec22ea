<?php $__env->startPush('css'); ?>
    
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <?php if(auth()->user()->hasRole('owner')): ?>
        <section class="subscription_sec">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="subscription_wrapper custom_cards_design">
                            <h1>My Subscription</h1>
                            <div class="row">
                                <div class="col-md-6">
                                    <?php if($activeSubcription && $activeSubcription->package): ?>
                                        <div class="basic_package_wrapper">
                                            <?php if($activeSubcription->stripe_subscription_id != null): ?>
                                                <div class="d-flex justify-content-between">
                                                    <span><?php echo e($activeSubcription->package->name ?? ''); ?>

                                                        <?php echo e($activeSubcription->stripe_subscription_id == null ? '(Trial)' : ''); ?></span>
                                                    <span id="toggleSubscription">
                                                        <?php echo e($buttonText ?? ''); ?>

                                                    </span>
                                                </div>
                                                <h1>£<?php echo e($activeSubcription->package->amount ?? ''); ?></h1>
                                            <?php endif; ?>
                                            <div class="show-arrow">
                                                <?php echo $activeSubcription->package->description ?? ''; ?>

                                            </div>
                                            <?php if(!in_array($activeSubcription->package->id, [5, 6]) && $activeSubcription->no_of_trips <= 0): ?>
                                                <h4>Reached the limit of your trips.</h4>
                                                <button type="button" id="renew-subscription-btn"
                                                    data-package-id="<?php echo e($activeSubcription->package->id); ?>"
                                                    class="btn  btn_grey">Renew Subsctiption
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg">
                                                </button>
                                            <?php endif; ?>
                                            <?php if(
                                                !in_array($activeSubcription->package->slug, ['gold-package-yearly', 'gold-package']) ||
                                                    $activeSubcription->stripe_subscription_id == null): ?>
                                                <h4>Upgrade to Unlock More Features.</h4>
                                            <?php endif; ?>
                                            <button type="button" class="btn  btn_grey"
                                                data-bs-target="#update_subscription" data-bs-toggle="modal">Buy
                                                Subscription<img
                                                    src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg">
                                            </button>
                                        </div>
                                    <?php else: ?>
                                        <div class="basic_package_wrapper">
                                            <button type="button" class="btn  btn_grey"
                                                data-bs-target="#update_subscription" data-bs-toggle="modal">Buy
                                                Subscription<img
                                                    src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg">
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        

        <div class="modal fade custom_modal" id="update_subscription" tabindex="-1" aria-labelledby="createModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title" id="createModalLabel">Buy a Subscription</h1>
                    </div>
                    <div class="modal-body ">
                        <form id="updateForm">
                            <?php echo csrf_field(); ?>
                            <div class="row custom_row">
                                <?php if($activeSubcription): ?>
                                    <div class="col-md-12">
                                        <div class="txt_field">
                                            <label>Current Subscription Name:</label>
                                            <input type="text" class="form-control"
                                                placeholder="<?php echo e($activeSubcription->package->name ?? ''); ?>" readonly
                                                required>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Select Package:</label>
                                        <select class="form-select" aria-label="Default select example" name="package"
                                            id="package">
                                            <?php if($remainingPackages): ?>
                                                <?php $__currentLoopData = $remainingPackages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($item->slug); ?>"
                                                        data-amount="<?php echo e($item->amount ?? 0); ?>"
                                                        data-package-id="<?php echo e($item->id); ?>"
                                                        <?php if($loop->first): ?> selected <?php endif; ?>>
                                                        <?php echo e($item->name ?? ''); ?>(<?php echo e($item->type ?? ''); ?>)</option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label>Amount:</label>
                                        <input id="amount" type="number" class="form-control" readonly required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" id="subscription-btn" class="btn btn_dark_green">Pay</button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn_transparent"
                                        data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            $('#amount').val($('#package option:selected').data('amount'));
            $('#package').on('change', function() {
                var amount = $('#package option:selected').data('amount');
                $('#amount').val(amount);
                console.log("Selected Package Amount: " + amount);
            });
        });
    </script>
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        $(document).ready(function() {
            $('#subscription-btn').on('click', function() {
                const packageId = $('#package option:selected').data('package-id');
                gettingSubscription(packageId);
            });
            $('#renew-subscription-btn').on('click', function() {
                const packageId = $(this).data('package-id');
                gettingSubscription(packageId);
            });

            function gettingSubscription(packageId) {
                $.ajax({
                    url: "<?php echo e(route('checkout.session')); ?>",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    contentType: 'application/json',
                    data: JSON.stringify({
                        package_id: packageId,
                        success_url: "<?php echo e(route('packages.index')); ?>",
                        // success_url: "<?php echo e(route('checkout.success')); ?>",
                        cancel_url: "<?php echo e(route('packages.index')); ?>"
                    }),
                    success: function(data) {
                        if (data.url) {
                            window.location.href = data.url; // Redirect to Stripe Checkout
                        } else {
                            alert(data.error || 'Something went wrong.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        alert('An error occurred. Please try again.');
                    }
                });
            }

            // Toggle Subscription on button click
            $("#toggleSubscription").click(function() {
                let button = $(this); // Store reference to button
                let buttonText = button.text().trim(); // Get the current button text

                // Show confirmation dialog
                Swal.fire({
                    title: "Are you sure?",
                    text: `Do you want to ${buttonText.toLowerCase()} your subscription?`,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yes, proceed!",
                    cancelButtonText: "Cancel"
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Proceed with AJAX request
                        $.ajax({
                            url: "<?php echo e(route('toggle.subscription')); ?>",
                            type: "POST",
                            headers: {
                                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                            },
                            success: function(response) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Success!',
                                    text: response.message,
                                    confirmButtonText: 'OK'
                                });

                                // Update button text dynamically
                                if (response.status === "canceling") {
                                    button.text("Resume");
                                } else {
                                    button.text("Cancel");
                                }
                            },
                            error: function(xhr) {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Oops...',
                                    text: 'An error occurred. Please try again.',
                                    confirmButtonText: 'OK'
                                });
                            }
                        });
                    }
                });
            });

        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\packages\index_ownerOLD.blade.php ENDPATH**/ ?>