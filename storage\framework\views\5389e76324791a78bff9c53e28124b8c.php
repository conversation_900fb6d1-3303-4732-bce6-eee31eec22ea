

<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="trip_management">
        <div class="container-fluid">
            <div class="row custom_row">
                <div class="col-md-8">
                    <div class="custom_cards_design">
                        <form method="post" action="<?php echo e(route('crews.update', $crew->id)); ?>" enctype="multipart/form-data">
                            <?php echo e(method_field('PATCH')); ?>

                            <?php echo e(csrf_field()); ?>

                            <div class="row custom_row">
                                <div class="col-md-12">
                                    <h1>Crew Details</h1>
                                </div>
                                <div class="col-md-12">
                                    <div class="edit_user_trip custom_justify_between icon_image">
                                        <div class="profile_image">
                                            <!--begin::Image input-->
                                            <div class="image-input image-input-circle" data-kt-image-input="true">
                                                <!--begin::Image preview wrapper-->
                                                <div class="image-input-wrapper">
                                                    <?php if($crew->image): ?>
                                                        <img class="input_image_field"
                                                            src="<?php echo e(asset('website') . '/' . $crew->image); ?>"
                                                            data-original-src="<?php echo e(asset('website')); ?>/assets/images/buildings.png">
                                                    <?php else: ?>
                                                        <img class="input_image_field"
                                                            src="<?php echo e(asset('website')); ?>/assets/images/buildings.png"
                                                            data-original-src="<?php echo e(asset('website')); ?>/assets/images/buildings.png">
                                                    <?php endif; ?>

                                                </div>
                                                <!--end::Image preview wrapper-->

                                                <!--begin::Edit button-->
                                                <label
                                                    class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                    data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                    data-bs-dismiss="click" title="Change avatar">
                                                    <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                            class="path2"></span></i>

                                                    <!--begin::Inputs-->
                                                    <input type="file" name="image" accept=".png, .jpg, .jpeg"
                                                        class="custom_file_input" />
                                                    <input type="hidden" name="avatar_remove" />
                                                    <!--end::Inputs-->
                                                </label>
                                                <!--end::Edit button-->

                                                <!--begin::Cancel button-->
                                                <span
                                                    class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                    data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                    data-bs-dismiss="click" title="Cancel avatar">
                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                </span>
                                                <!--end::Cancel button-->
                                            </div>
                                        </div>
                                        <div class="profile_changes">
                                            <div class="edit_change custom_flex">
                                                <a class="btn btn_dark_green edit_profile">Edit</a>
                                                <a class="btn btn_transparent toggle-status"
                                                    data-slug="<?php echo e($crew->id); ?>"><?php echo e($crew->status === 1 ? 'Deactivate' : 'Activate'); ?></a>
                                            </div>
                                            <div class="save_cancel_btn custom_flex">
                                                <button type="submit" class="btn btn_dark_green">Save changes</button>
                                                <button type="button"
                                                    class="btn btn_transparent cancel_edit">Cancel</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Name:</label>
                                        <input type="text" class="form-control myinput" required id=""
                                            name="name" value="<?php echo e($crew->name ?? ''); ?>" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Role:</label>
                                        <input name="role" value="<?php echo e($crew->role ?? ''); ?>" type="text"
                                            class="form-control myinput" required id="" placeholder="Name Here"
                                            readonly>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Description:</label>
                                        <textarea name="description" rows="1" class="form-control myinput" required readonly><?php echo e($crew->description ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="user_trip_details">
                        <div class="row custom_row_card custom_row">
                            <div class="col-md-12 custom_column_card">
                                <div class="custom_card">
                                    <h2>Total Trips On Boarded</h2>
                                    <h1 class="f_30"><?php echo e($trips->count()); ?></h1>
                                </div>
                            </div>
                            <div class="col-md-12 custom_column_card">
                                <div class="custom_card">
                                    <h2>Last on Boarded Trip</h2>
                                    <h1 class="f_30"><?php echo e($trips->first()->name ?? 'N/A'); ?></h1>
                                </div>
                            </div>
                            <div class="col-md-12 custom_column_card">
                                <div class="custom_card">
                                    <h2>Joining Date</h2>
                                    <h1 class="f_30"><?php echo e($crew->created_at->format('d/m/Y')); ?></h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="custom_table custom_cards_design">
                        <h1>On Boarding Summary</h1>
                        <div class="custom_scroll_tbl">
                            <table class="table without_pagination_tbl datatable">
                                <thead>
                                    <tr>
                                        <th>SR#</th>
                                        <th>Trip Name</th>
                                        <th>Starting Point</th>
                                        <th>Ending Point</th>
                                        <th>Duration</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if($trips->isNotEmpty()): ?>
                                        <?php $__currentLoopData = $trips->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $trip): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($loop->iteration); ?></td>
                                                <td><?php echo e($trip->name ?? ''); ?></td>
                                                <td><?php echo e(optional($trip->itineraries->first())->start_point ?? ''); ?></td>
                                                <td><?php echo e(optional($trip->itineraries->last())->end_point ?? ''); ?></td>
                                                <td><?php echo e($trip->duration ?? ''); ?></td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="5">No trips available.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            $('.toggle-status').on('click', function() {
                let button = $(this);
                let crewSlug = button.data('slug');
                let row = $(`#crew-${crewSlug}`);

                $.ajax({
                    url: "<?php echo e(url('crews')); ?>" + `/${crewSlug}/toggle-status`,
                    type: 'GET',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>',
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            // Update the button text and status
                            let newStatus = response.new_status;
                            button.html(newStatus === 1 ? 'Deactivate' : 'Activate');
                            button.data('status', newStatus);

                            // Update the status label in the row
                            // let statusLabel = row.find('.status-label');
                            // statusLabel
                            //     .text(newStatus === 1 ? 'Active' :
                            //     'In-Active') // Capitalize the status
                            //     .removeClass('success danger')
                            //     .addClass(newStatus === 1 ? 'success' : 'danger');

                            // Show SweetAlert success message
                            Swal.fire({
                                title: 'Success!',
                                text: `Crew ${newStatus === 1 ? 'Activated' : 'Deactivated'} successfully!`,
                                icon: 'success',
                                confirmButtonText: 'OK',
                            });
                        } else {
                            // Show SweetAlert error message
                            Swal.fire({
                                title: 'Error!',
                                text: 'Failed to update status. Please try again.',
                                icon: 'error',
                                confirmButtonText: 'OK',
                            });
                        }
                    },
                    error: function() {
                        // Show SweetAlert error message
                        Swal.fire({
                            title: 'Error!',
                            text: 'An error occurred. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK',
                        });
                    },
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\crews\show.blade.php ENDPATH**/ ?>