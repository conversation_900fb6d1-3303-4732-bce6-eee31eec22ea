<?php $__env->startPush('css'); ?>
    <style>
        .navbar.navbar-expand-lg.packages_navbar,
        .navbar_header_sec .header_menus_wrapper,
        footer {
            display: none;
        }

        .navbar_header_sec a {
            margin: auto;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!--begin::Authentication - Sign-up -->
    <section class="login_pg_sec">
        <div class="container-fluid">
            <div class="row custom_login_row">
                <div class="col-md-6">
                    <div class="login_pg_img_sec">
                        <img src="<?php echo e(asset('website')); ?>/assets/images/image_round.png">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="sign_in_wrapper_whole">
                        <form class="form" id="register_form" novalidate="novalidate" method="POST" method="POST"
                            action="<?php echo e(route('register')); ?>">
                            <?php echo csrf_field(); ?>
                            <div class="sign_in_fields">
                                <!--begin::Heading-->
                                
                                
                                
                                
                                
                                
                                
                                
                                <!--begin::Heading-->
                                <!--begin::Login options-->
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                <!--end::Login options-->
                                <!--begin::Separator-->
                                
                                
                                
                                <!--end::Separator-->
                                <!--begin::Input group=-->
                                <div class="sign_in_heading">
                                    <h3>Sign Up</h3>
                                </div>
                                <div class="login_fields_wrapper">
                                    <div class="input_filed_wrapper_sign form-floating">
                                        <!--begin::Email-->
                                        
                                        <input id="name" type="text" placeholder=""
                                            class="form-control  bg-transparent <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            name="name" value="<?php echo e(old('name')); ?>" required autocomplete="name"
                                            autofocus>
                                        <label for="name">Name*</label>
                                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback" role="alert">
                                                <strong><?php echo e($message); ?></strong>
                                            </span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <!--end::Email-->
                                    </div>
                                    
                                    <div class="input_filed_wrapper_sign form-floating">
                                        <!--begin::Email-->
                                        
                                        <input id="company_name" type="text" placeholder=""
                                            class="form-control  bg-transparent <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            name="company_name" value="<?php echo e(old('company_name')); ?>" required
                                            autocomplete="name" autofocus>
                                        <label for="company_name">Company Name*</label>
                                        <?php $__errorArgs = ['company_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback" role="alert">
                                                <strong><?php echo e($message); ?></strong>
                                            </span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <!--end::Email-->
                                    </div>
                                    <!--begin::Input group=-->
                                    <div class="input_filed_wrapper_sign form-floating">
                                        <!--begin::Email-->
                                        
                                        <input id="email" type="email" placeholder=""
                                            class="form-control  bg-transparent <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            name="email" value="<?php echo e(old('email')); ?>" required autocomplete="email">
                                        <label for="email">Email*</label>
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback" role="alert">
                                                <strong><?php echo e($message); ?></strong>
                                            </span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <!--end::Email-->
                                    </div>
                                    <!--begin::Input group-->
                                    <div class="" data-kt-password-meter="true">
                                        <!--begin::Wrapper-->
                                        <div class="">
                                            <!--begin::Input wrapper-->
                                            <div class="input_filed_wrapper_sign form-floating input_wrapper">
                                                
                                                
                                                <input id="password" type="password" placeholder=""
                                                    class="form-control  pass_log  bg-transparent <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                    name="password" required autocomplete="new-password">
                                                <label for="password">Password*</label>
                                                <i class="fa-solid input_icon fa-eye"></i>
                                                <i class="fa-solid input_icon fa-eye-slash"></i>
                                                <p>Please Enter Password should be atleast 8 characters long,alphanumeric
                                                    and
                                                    contain atleast one capital letter.</p>
                                                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <span class="invalid-feedback" role="alert">
                                                        <strong><?php echo e($message); ?></strong>
                                                    </span>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                <span
                                                    class="btn btn-sm btn-icon position-absolute translate-middle top-50 end-0 me-n2"
                                                    data-kt-password-meter-control="visibility">
                                                    <i class="ki-duotone ki-eye-slash fs-2"></i>
                                                    <i class="ki-duotone ki-eye fs-2 d-none"></i>
                                                </span>
                                            </div>
                                            <!--end::Input wrapper-->
                                            <!--begin::Meter-->
                                            
                                            
                                            
                                            
                                            
                                            
                                            <!--end::Meter-->
                                        </div>
                                        <!--end::Wrapper-->
                                        <!--begin::Hint-->

                                        <!--end::Hint-->
                                    </div>
                                    <!--end::Input group=-->
                                    <!--end::Input group=-->
                                    <div class="input_filed_wrapper_sign form-floating input_wrapper">
                                        <!--begin::Repeat Password-->
                                        
                                        <input id="password-confirm" type="password" placeholder=""
                                            class="form-control pass_log bg-transparent" name="password_confirmation"
                                            required autocomplete="new-password">
                                        <label for="password-confirm">Confirm Password*</label>
                                        <i class="fa-solid input_icon fa-eye"></i>
                                        <i class="fa-solid input_icon fa-eye-slash"></i>
                                        <!--end::Repeat Password-->
                                    </div>
                                </div>

                                <!--end::Input group=-->
                                <!--begin::Accept-->
                                <div class="forget_checkbox_wrapper forget_checkbox_wrapper_sign_up">
                                    <label class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" name="toc" value="1" />
                                        <span class="form-check-label">I Accept the Terms</span>
                                    </label>
                                </div>
                                <span class="for-span-error"></span>
                                <!--end::Accept-->
                                <!--begin::Submit button-->
                                <div class="">
                                    <button type="submit" id="kt_sign_up_submit"
                                        class=" btn_global yellow_btn arrow_up_right_btn_img">
                                        <!--begin::Indicator label-->
                                        <span class="indicator-label">Sign up</span>
                                        <!--end::Indicator label-->
                                        <!--begin::Indicator progress-->
                                        <span class="indicator-progress">Please wait...
                                            <span class="spinner-border spinner-border-sm"></span></span>
                                        <!--end::Indicator progress-->
                                    </button>
                                </div>
                                <!--end::Submit button-->
                                <!--begin::Sign up-->
                                <p class=" already_an_account">Already have an Account?
                                    <a href="<?php echo e(route('login')); ?>" class="">Sign in</a>
                                </p>
                                <!--end::Sign up-->
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>


        <!--end::Body-->
    </section>
    <!--end::Authentication - Sign-up-->
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            $(".fa-eye").hide();
            $(".fa-eye-slash").show();
            $(".input_icon").click(function() {
                $(this).closest(".input_wrapper").find(".input_icon").toggleClass("fa-eye fa-eye-slash")
                var input = $(this).siblings(".pass_log");
                input.attr('type') === 'password' ? input.attr('type', 'text') : input.attr('type',
                    'password')
            });

            $.validator.addMethod("pwcheck", function(value) {
                    return /[A-Z]/.test(value) && /[a-z]/.test(value) && /\d/.test(value);
                },
                "Your password must contain at least one uppercase letter, one lowercase letter, and one number."
            );

            // Validate the form
            $('#register_form').validate({
                rules: {
                    name: {
                        required: true,
                        minlength: 2
                    },
                    company_name: {
                        required: true,
                        minlength: 2
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    password: {
                        required: true,
                        minlength: 8,
                        pwcheck: true // Custom password rule
                    },
                    password_confirmation: {
                        required: true,
                        equalTo: "#password"
                    },
                    toc: {
                        required: true
                    }
                },
                messages: {
                    name: {
                        required: "Please enter your name.",
                        minlength: "Your name must be at least 2 characters long."
                    },
                    company_name: {
                        required: "Please enter your company name.",
                        minlength: "Your company name must be at least 2 characters long."
                    },
                    email: {
                        required: "Please enter your email.",
                        email: "Please enter a valid email address."
                    },
                    password: {
                        required: "Please provide a password.",
                        minlength: "Your password must be at least 8 characters long."
                    },
                    password_confirmation: {
                        required: "Please confirm your password.",
                        equalTo: "Passwords do not match."
                    },
                    toc: {
                        required: "You must accept the terms and conditions."
                    }
                },
                errorElement: "span",
                errorClass: "invalid-feedback",
                highlight: function(element, errorClass) {
                    $(element).addClass("is-invalid");
                },
                unhighlight: function(element, errorClass) {
                    $(element).removeClass("is-invalid");
                },
                errorPlacement: function(error, element) {
                    if (element.attr("name") === "toc") {
                        // Place the error inside the checkbox wrapper
                        error.appendTo(element.closest(
                            '.forget_checkbox_wrapper.forget_checkbox_wrapper_sign_up').next(
                            '.for-span-error'));
                    } else {
                        error.insertAfter(element);
                    }
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\auth\register.blade.php ENDPATH**/ ?>