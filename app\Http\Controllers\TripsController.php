<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\Trip;
use App\Http\Requests\TripRequest;
use App\Models\ActivityImages;
use App\Models\BoatSpecImages;
use App\Models\Crew;
use App\Models\Itinerary;
use App\Models\ItineraryImage;
use App\Models\MainHeaderImages;
use App\Models\MenuPlan;
use App\Models\MenuPlanDetail;
use App\Models\OpenAiResponse;
use App\Models\SafetyOnBoardImage;
use App\Models\Section;
use App\Models\TripSpaDetails;
use App\Services\OpenAiService;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;



class TripsController extends Controller
{
    protected $openAiService;

    function __construct(OpenAiService $openAiService)
    {
        $this->openAiService = $openAiService;
        $this->middleware('permission:trips-list|trips-create|trips-edit|trips-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:trips-create', ['only' => ['create', 'store']]);
        $this->middleware('permission:trips-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:trips-delete', ['only' => ['destroy']]);
        $this->middleware('permission:trips-list', ['only' => ['show']]);
    }

    public function index()
    {
        $user = auth()->user();
        $activeSub = $user->activeSubscription;
        if ($user->hasRole('admin')) {
            $trips = Trip::orderBy('created_at', 'desc')->get();
        } else {
            $trips = $user->trips()->orderBy('created_at', 'desc')->get();
        }
        return view('trips.index', compact('trips', 'activeSub'));
    }
    public function getAiResponse($id)
    {
        $response = OpenAiResponse::find($id)->response_data_other;
        $start = strpos($response, "**Activities:**") + strlen("**Activities:**");
        $activitiesText = str_replace(["**"], "", substr($response, $start));
        $activitySections = explode("\n\n", $activitiesText);
        $activitySections = array_slice($activitySections, 1);
        $activities = [];
        // Loop through each activity section
        foreach ($activitySections as $activity) {
            // Replace newline (\n) with colon (:) in the description
            $activity = str_replace("\n", ":", $activity);
            // Append the formatted activity to the activities array
            $activities[] = $activity;
        }

        return $activities;


        // return $this->openAiService->extractTripOverview($response);
        $activities = $this->openAiService->extractActivities($response);
        $trip = Trip::findOrFail($id);
    }

    public function create()
    {
        $user = auth()->user();
        $activeSub = $user->activeSubscription;
        return view('trips.create', compact('activeSub'));
    }


    public function storeOLD(TripRequest $request)
    {
        $user = auth()->user();
        $slug = Str::slug($request->input('name'));
        $existingSlug = Trip::where('slug', $slug)->first();
        if ($existingSlug) {
            $slug = $this->makeSlugUnique($slug);
        }
        $sectionIds = [1, 2, 3, 4, 5, 6, 7];
        $trip = new Trip;
        $trip->name = $request->input('name');
        $trip->slug = $slug;
        $trip->user_id = $user->id;
        $trip->template_id = 1;
        $trip->company_slug = Str::slug($user->company_name);
        $trip->package_id = 6;
        $trip->start_date = $request->input('start_date');
        $trip->end_date = $request->input('end_date');
        $trip->url_slug = Str::slug($request->input('url_slug'));
        $trip->duration = $request->input('duration');
        $trip->save();

        $trip->sections()->sync($sectionIds);

        return response()->json([
            'status' => 'success',
            'message' => 'Trip created successfully!',
            'trip' => $trip
        ]);
    }
    public function store(TripRequest $request)
    {
        $user = auth()->user();
        $activeSub = $user->activeSubscription;

        if ($activeSub && $activeSub->no_of_trips > 0) {
            $slug = Str::slug($request->input('name'));
            $existingSlug = Trip::where('slug', $slug)->first();
            if ($existingSlug) {
                $slug = $this->makeSlugUnique($slug);
            }
            // $sectionIds = $activeSub->package->sections()->pluck('sections.id')->toArray();
            $packageId = $activeSub->package->id;
            if ($packageId == 1 || $packageId == 2) {
                $sectionIds = [1, 2];
            } else if ($packageId == 3 || $packageId == 4) {
                $sectionIds = [1, 2];
            } else if ($packageId == 5 || $packageId == 6) {
                $sectionIds = [1, 2, 3, 4, 5, 6, 7];
            } else {
                $sectionIds = [];
            }
            $trip = new Trip;
            $trip->name = $request->input('name');
            $trip->slug = $slug;
            $trip->user_id = $user->id;
            $trip->template_id = 1;
            $trip->company_slug = Str::slug($user->company_name);
            $trip->package_id = $activeSub->package_id;
            $trip->start_date = $request->input('start_date');
            $trip->end_date = $request->input('end_date');
            $trip->url_slug = Str::slug($request->input('url_slug'));
            $trip->duration = $request->input('duration');
            $trip->save();
            if (!in_array($packageId, [5, 6])) {
                $activeSub->decrement('no_of_trips', 1);
            }

            $trip->sections()->sync($sectionIds);
            try {
                // Send the email directly with an inline view
                Mail::send('email.new_trip_email', [
                    'user' => $user,
                    'trip' => $trip,
                ], function ($message) use ($user) {
                    $message->to($user->email)
                        ->subject('New Trip Created');
                });

                Log::info("Email sent to {$user->email}");
            } catch (\Exception $e) {
                // Log the error message
                Log::error("Failed to send email: " . $e->getMessage());
            }
            // return to_route('trips.index');
            return response()->json([
                'status' => 'success',
                'message' => 'Trip created successfully!',
                'trip' => $trip
            ]);
        } else {
            return to_route('trips.index');
        }
    }

    public function MakeItineraries(Request $request)
    {
        $request->validate([
            'locations' => 'required|array|min:2',
            'slug' => 'required|string',
            'trip_area' => 'required|string',
        ]);

        $trip = Trip::whereSlug($request->input('slug'))->first();

        if (!$trip) {
            return response()->json([
                'status' => false,
                'message' => 'Trip not found.',
            ], 404);
        }

        $locations = collect($request->input('locations'))->filter()->values()->all();
        // $stops = collect($locations)->slice(1, count($locations) - 2)->all();
        $preferences = $request->input('preferences', null);

        if (count($locations) < 2) {
            return response()->json([
                'status' => false,
                'message' => 'At least two locations are required to generate an itinerary.',
            ], 400);
        }

        $response = $this->openAiService->fetchOpenAiResponse($trip->id, $locations, $preferences);
        $details = $this->openAiService->fetchOpenAiResponseForOther($trip->id, $locations, $preferences, $trip->duration);
        $tripOverview = $this->openAiService->extractTripOverview($details);
        $activities = $this->openAiService->extractActivities($details);


        if (isset($response['error'])) {
            \Log::error('OpenAI Error:', $response);
            return response()->json([
                'status' => false,
                'message' => $response['error'],
            ], 400);
        }

        $itineraries = $this->openAiService->extractItineraryRecords($response);

        if (isset($itineraries['error'])) {
            \Log::error('OpenAI Error:', $itineraries);
            return response()->json([
                'status' => false,
                'message' => $itineraries['error'],
            ], 400);
        }

        if (empty($itineraries)) {
            \Log::error('No itineraries found.');
            return response()->json([
                'status' => false,
                'message' => 'Itineraries could not be generated.',
            ], 400);
        }

        // $startPoint = $itineraries[0]['start_point'];
        // $endPoint = end($itineraries)['end_point'];
        // $duration = $trip->duration ?? '';
        // $area = $request->input('trip_area') ?? ''; // Ensure stops are extracted correctly.
        // $stops = [];
        // if (count($itineraries) > 1) {
        //     // Collect all end points except the first and last.
        //     $stops = array_map(function ($itinerary) {
        //         return $itinerary['end_point'];
        //     }, array_slice($itineraries, 0, count($itineraries) - 1));
        // }

        // $tripOverview = "$duration day yacht charter itinerary";
        // if (!empty($area)) {
        //     $tripOverview .= " around $area";
        // }

        // $tripOverview .= " starting in $startPoint and ending in $endPoint";

        // if (!empty($stops)) {
        //     $stopsList = implode(", ", $stops);
        //     $tripOverview .= ", taking in good spots and a stop off at $stopsList.";
        // }
        $trip->mainHeader()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'description' => $tripOverview,
            ]
        );

        $trip->activity()->updateOrCreate(
            ['trip_id' => $trip->id],
            [
                'texts' => $activities, // Convert the activities array to JSON format
            ]
        );

        return response()->json([
            'status' => true,
            'message' => 'Itinerary regenerated successfully',
            'data' => $itineraries,
            'slug' => $trip->slug,
            'tripOverview' => $tripOverview,
        ], 200);
    }

    public function RegenerateItinerary(Request $request)
    {
        $request->validate([
            'slug' => 'required|string',
            'itinerary_number' => 'required|integer',
        ]);

        $trip = Trip::whereSlug($request->input('slug'))->first();
        $previousResponse = $trip->aiResponse->response_data ?? null;
        $itineraryToChange = $request->input('itinerary_number');

        $response = $this->openAiService->regenerateOpenAiResponse($trip->id,  $preferences = null, $itineraryToChange, $previousResponse);

        if (isset($response['error'])) {
            \Log::error('OpenAI Error:', $response);
            return response()->json([
                'status' => false,
                'message' => $response['error'],
            ], 400);
        }

        $itineraries = $this->openAiService->extractItineraryRecords($response);

        if (isset($itineraries['error'])) {
            \Log::error('OpenAI Error:', $itineraries);
            return response()->json([
                'status' => false,
                'message' => $itineraries['error'],
            ], 400);
        }

        if (empty($itineraries)) {
            \Log::error('No itineraries found.');
            return response()->json([
                'status' => false,
                'message' => 'Itineraries could not be generated.',
            ], 400);
        }

        return response()->json([
            'status' => true,
            'message' => 'Itinerary regenerated successfully!',
            'data' => $itineraries,
            'slug' => $trip->slug,
        ], 200);
    }

    public function SaveItineraries(Request $request)
    {
        $request->validate([
            'slug' => 'required|string',
            'itineraries' => 'required|array',
        ]);

        $trip = Trip::whereSlug($request->input('slug'))->first();

        $itineraries = $request->input('itineraries');

        foreach ($itineraries as $itinerary) {
            Itinerary::create([
                'trip_id' => $trip->id,
                'number' => $itinerary['itinerary_number'],
                'duration' => $itinerary['duration'],
                'start_point' => $itinerary['start_point'],
                'end_point' => $itinerary['end_point'],
                'start_lat' => $itinerary['start_lat'],
                'start_lng' => $itinerary['start_lng'],
                'end_lat' => $itinerary['end_lat'],
                'end_lng' => $itinerary['end_lng'],
                'description' => $itinerary['description'],
            ]);
        }

        return response()->json([
            'status' => true,
            'url' => route('trips.edit', $trip->slug),
            'message' => 'Trip updated successfully with AI-generated itinerary!',
        ], 201);
    }




    public function show($id)
    {
        $trip = Trip::whereSlug($id)->first();
        return view('trips.show', ['trip' => $trip]);
    }

    public function edit($id)
    {
        $trip = Trip::whereSlug($id)->first();
        $templates = $trip->package->templates;
        $assignedCrewIds = $trip->crews->pluck('id')->toArray();
        $crew = Crew::where('user_id', auth()->user()->id)->where('status', 1)->orWhereIn('id', $assignedCrewIds)->get();
        // $crew = Crew::where(function ($query) use ($assignedCrewIds) {
        //     $query->where('user_id', auth()->user()->id)
        //         ->where('status', 1)
        //         ->orWhereIn('id', $assignedCrewIds);
        // })->get();
        $chooseSections = Section::where('id', '>', 2)->limit(5)->get();
        $assignedSectionIds = $trip->sections->pluck('id')->toArray();
        return view('trips.edit', compact('trip', 'crew', 'assignedCrewIds', 'templates', 'chooseSections', 'assignedSectionIds'));
    }

    public function update(Request $request, $id)
    {
        $isMenuPlanPdf = false;
        $isSpaMenuPdf = false;
        $trip = Trip::whereUrlSlug($id)->first();

        $defaultSections = [1, 2]; // Default sections for most packages
        $allowedSections = [3, 4, 5, 6, 7]; // Allowed values for dynamic section attachment
        $sectionForAttach = $request->input('section_id', []); // Get sections from request
        if (in_array($trip->package_id, [3, 4]) && count($sectionForAttach) > 2) {
            $sectionForAttach = array_slice($sectionForAttach, 0, 2);
        }
        if (in_array($trip->package_id, [1, 2, 7])) {
            $sectionIds = $defaultSections;
        } elseif (in_array($trip->package_id, [3, 4, 5, 6])) {
            // Filter request values to include only allowed sections
            $sectionForAttach = array_intersect($sectionForAttach, $allowedSections);
            $sectionIds = array_merge($defaultSections, $sectionForAttach);
            // } elseif (in_array($trip->package_id, [5, 6])) {
            //     $sectionIds = array_merge($defaultSections, $allowedSections); // Include all allowed sections
        } else {
            $sectionIds = $defaultSections;
        }
        $trip->sections()->sync($sectionIds);
        $validated = $request->validate([
            'template_id' => 'required',
            'menu_plan_type' => 'nullable|in:pdf,image',
            'spa_menu_type' => 'nullable|in:pdf,image',
            'menu_plan_pdf' => 'file|mimes:pdf',
            'spa_menu_pdf' => 'file|mimes:pdf'
        ], [
            'template_id.required' => 'Please select a template!',
        ]);
        $trip->update($validated);
        //main header
        $trip->mainHeader()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'kicker' => $request->main_header_kicker,
                'heading' => $request->main_header_heading,
                'sub_heading' => $request->main_header_sub_heading,
                'description' => $request->main_header_description,
            ]
        );
        //tripItinerarySummary
        $trip->tripItinerarySummary()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'kicker' => $request->itinerary_summary_kicker,
                'heading' => $request->itinerary_summary_heading,
                'sub_heading' => $request->itinerary_summary_sub_heading,
            ]
        );
        //itinerary
        $itiImagesArray = [];
        // Get existing itineraries for the trip
        if ($request->itinerary) {
            $itineraries = $trip->itineraries;
            foreach ($request->itinerary as $index => $itinerRequest) {
                // Create or update the itinerary
                $itiner = $trip->itineraries()->updateOrCreate(
                    ['trip_id' => $trip->id, 'id' => $index],  // Assuming $index is the itinerary ID
                    $itinerRequest  // Pass the specific itinerary data
                );

                // Handle images if present in the request
                if (isset($itinerRequest['images'])) {

                    foreach ($itinerRequest['images'] as $imageIndex => $image) {
                        $path = $this->storeImage("itinerary_images", $image);
                        $itiImage = $itiner->images()->create([
                            'image' => $path,
                        ]);
                    }
                    $itiImagesArray[$index] = $itiner;
                }
                $existingImages = $itiner->images;  // Assuming a one-to-many relationship with images

            }
            foreach ($itineraries as $existingItinerary) {
                if (!array_key_exists($existingItinerary->id, $request->itinerary)) {
                    foreach ($existingItinerary->images as $existingImage) {
                        if ($existingImage->image) {
                            Storage::disk('website')->delete($existingImage->image);
                        }
                        $existingImage->delete();
                    }
                    $existingItinerary->delete();
                }
            }
        }

        //tripItineraryMap
        $trip->tripItineraryMap()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'kicker' => $request->itinerary_map_kicker,
                'heading' => $request->itinerary_map_heading,
                'sub_heading' => $request->itinerary_map_sub_heading,
            ]
        );

        //crew
        $trip->crewDetail()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'kicker' => $request->crew_detail_kicker,
                'heading' => $request->crew_detail_heading,
                'sub_heading' => $request->crew_detail_sub_heading,
            ]
        );
        if ($request->crews) {
            $orderedCrews = explode(',', $request->ordered_crews); // Parse ordered crew IDs from the hidden input
            $crewData = [];
            foreach ($orderedCrews as $index => $crewId) {
                $crewData[$crewId] = ['sequence' => $index + 1]; // Add sequence data
            }
            $trip->crews()->sync($crewData);
        }
        if (in_array($trip->package_id, [3, 4, 5, 6])) {
            // menu
            $trip->menu()->updateOrCreate(
                ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
                [
                    'kicker' => $request->menu_kicker,
                    'heading' => $request->menu_heading,
                    'sub_heading' => $request->menu_sub_heading,
                ]
            );
            //menu plans images
            if ($request->hasFile('menu_plan_pdf')) {
                $pdfMenuPlanImages = $trip->menuPlanImages()->where('type', 'pdf')->get();
                foreach ($pdfMenuPlanImages as $menuPlanImage) {
                    if ($menuPlanImage->image) {
                        Storage::disk('website')->delete($menuPlanImage->image);
                    }
                    $menuPlanImage->delete();
                }
                $isMenuPlanPdf = true;
                $pdfFile = $request->file('menu_plan_pdf');
                $menuPlanResponse = $this->convertPdfToImagesAndStore($pdfFile, $trip, 'menu_plan_images', 'menuPlanImages');
            }
            if ($request->hasFile('spa_menu_pdf')) {
                $pdfSpaMenuImages = $trip->spaMenuImages()->where('type', 'pdf')->get();
                foreach ($pdfSpaMenuImages as $spaMenuImage) {
                    if ($spaMenuImage->image) {
                        Storage::disk('website')->delete($spaMenuImage->image);
                    }
                    $spaMenuImage->delete();
                }
                $isSpaMenuPdf = true;
                $pdfFile = $request->file('spa_menu_pdf');
                $spaMenuResponse = $this->convertPdfToImagesAndStore($pdfFile, $trip, 'spa_menu_images', 'spaMenuImages');
            }

            //activites
            $trip->activity()->updateOrCreate(
                ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
                [
                    'kicker' => $request->activity_kicker,
                    'heading' => $request->activity_heading,
                    'sub_heading' => $request->activity_sub_heading,
                    'texts' => $request->texts,
                ]
            );
            //boat specification
            $trip->boatSpec()->updateOrCreate(
                ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
                [
                    'kicker' => $request->boat_specs_kicker,
                    'heading' => $request->boat_specs_heading,
                    'sub_heading' => $request->boat_specs_sub_heading,
                    'description' => $request->boat_specs_description,
                ]
            );
            //safety on board
            $trip->safetyOnBoard()->updateOrCreate(
                ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
                [
                    'kicker' => $request->safety_on_board_kicker,
                    'heading' => $request->safety_on_board_heading,
                    'sub_heading' => $request->safety_on_board_sub_heading,
                    'description' => $request->safety_on_board_description,
                ]
            );
            // spa
            $trip->spa()->updateOrCreate(
                ['trip_id' => $trip->id],
                [
                    'kicker' => $request->spa_menu_kicker,
                    'heading' => $request->spa_menu_heading,
                    'sub_heading' => $request->spa_menu_sub_heading,
                ]
            );
        }

        return response()->json(['message' => 'Trip details updated.', 'itiImagesArray' => $itiImagesArray, 'isMenuPlanPdf' => $isMenuPlanPdf, 'isSpaMenuPdf' => $isSpaMenuPdf]);
    }
    public function updateOLD2(Request $request, $id)
    {
        $trip = Trip::whereUrlSlug($id)->first();

        $defaultSections = [1, 2]; // Default sections for most packages
        $allowedSections = [3, 4, 5, 6, 7]; // Allowed values for dynamic section attachment
        $sectionForAttach = $request->input('section_id', []); // Get sections from request
        if (count($sectionForAttach) > 2) {
            $sectionForAttach = array_slice($sectionForAttach, 0, 2);
        }
        if (in_array($trip->package_id, [1, 2, 7])) {
            $sectionIds = $defaultSections;
        } elseif (in_array($trip->package_id, [3, 4])) {
            // Filter request values to include only allowed sections
            $sectionForAttach = array_intersect($sectionForAttach, $allowedSections);
            $sectionIds = array_merge($defaultSections, $sectionForAttach);
        } elseif (in_array($trip->package_id, [5, 6])) {
            $sectionIds = array_merge($defaultSections, $allowedSections); // Include all allowed sections
        } else {
            $sectionIds = $defaultSections;
        }
        $trip->sections()->sync($sectionIds);
        $validated = $request->validate([
            'template_id' => 'required',
        ], [
            'template_id.required' => 'Please select a template!',
        ]);
        $trip->update($validated);
        //main header
        $trip->mainHeader()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'kicker' => $request->main_header_kicker,
                'heading' => $request->main_header_heading,
                'sub_heading' => $request->main_header_sub_heading,
                'description' => $request->main_header_description,
            ]
        );
        //tripItinerarySummary
        $trip->tripItinerarySummary()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'kicker' => $request->itinerary_summary_kicker,
                'heading' => $request->itinerary_summary_heading,
                'sub_heading' => $request->itinerary_summary_sub_heading,
            ]
        );
        //itinerary
        $itiImagesArray = [];
        // Get existing itineraries for the trip
        if ($request->itinerary) {
            $itineraries = $trip->itineraries;
            foreach ($request->itinerary as $index => $itinerRequest) {
                // Create or update the itinerary
                $itiner = $trip->itineraries()->updateOrCreate(
                    ['trip_id' => $trip->id, 'id' => $index],  // Assuming $index is the itinerary ID
                    $itinerRequest  // Pass the specific itinerary data
                );

                // Handle images if present in the request
                if (isset($itinerRequest['images'])) {

                    foreach ($itinerRequest['images'] as $imageIndex => $image) {
                        $path = $this->storeImage("itinerary_images", $image);
                        $itiImage = $itiner->images()->create([
                            'image' => $path,
                        ]);
                    }
                    $itiImagesArray[$index] = $itiner;
                }
                $existingImages = $itiner->images;  // Assuming a one-to-many relationship with images

            }
            foreach ($itineraries as $existingItinerary) {
                if (!array_key_exists($existingItinerary->id, $request->itinerary)) {
                    foreach ($existingItinerary->images as $existingImage) {
                        if ($existingImage->image) {
                            Storage::disk('website')->delete($existingImage->image);
                        }
                        $existingImage->delete();
                    }
                    $existingItinerary->delete();
                }
            }
        }

        //tripItineraryMap
        $trip->tripItineraryMap()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'kicker' => $request->itinerary_map_kicker,
                'heading' => $request->itinerary_map_heading,
                'sub_heading' => $request->itinerary_map_sub_heading,
            ]
        );

        //crew
        $trip->crewDetail()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'kicker' => $request->crew_detail_kicker,
                'heading' => $request->crew_detail_heading,
                'sub_heading' => $request->crew_detail_sub_heading,
            ]
        );
        if ($request->crews) {
            $orderedCrews = explode(',', $request->ordered_crews); // Parse ordered crew IDs from the hidden input
            $crewData = [];
            foreach ($orderedCrews as $index => $crewId) {
                $crewData[$crewId] = ['sequence' => $index + 1]; // Add sequence data
            }
            $trip->crews()->sync($crewData);
        }

        if (in_array($trip->package_id, [3, 4, 5, 6])) {
            // menu
            $trip->menu()->updateOrCreate(
                ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
                [
                    'kicker' => $request->menu_kicker,
                    'heading' => $request->menu_heading,
                    'sub_heading' => $request->menu_sub_heading,
                ]
            );
            //menu plans
            $existingMenuPlans = $trip->menuPlans;
            if ($request->menu_plans) {
                foreach ($request->menu_plans as $index => $plan) {
                    // Find or create MenuPlan
                    $menuPlan = $trip->menuPlans()->where('id', $index ?? null)->first() ?? new MenuPlan();
                    $menuPlan->trip_id = $trip->id;
                    $menuPlan->name = $plan['name'];
                    $menuPlan->save();

                    // Update Menu Plan Details
                    if (isset($plan['details'])) {
                        $existingDetails = $menuPlan->details;
                        foreach ($plan['details'] as $detailIndex => $detail) {
                            // Find or create MenuPlanDetail
                            $menuDetail = $menuPlan->details()->where('id', $detailIndex ?? null)->first() ?? new MenuPlanDetail();
                            $menuDetail->menu_plan_id = $menuPlan->id; // Associate with Menu Plan

                            $menuDetail->title = $detail['title'];
                            $menuDetail->text = $detail['text'];

                            // Handle Image Update
                            if ($request->hasFile("menu_plans.{$index}.details.{$detailIndex}.image") && $request->file("menu_plans.{$index}.details.{$detailIndex}.image")->isValid()) {
                                // Upload new image
                                $imagePath = Storage::disk('website')->put('menu_detail_images', $request->file("menu_plans.{$index}.details.{$detailIndex}.image"));

                                // If there's an old image, delete it
                                if ($menuDetail->image) {
                                    Storage::disk('website')->delete($menuDetail->image);
                                }

                                // Save the new image path
                                $menuDetail->image = $imagePath;
                            }

                            // Save the MenuPlanDetail
                            $menuDetail->save();
                        }
                        foreach ($existingDetails as $existingDetail) {
                            // Check if the existing detail ID is in the request details (not new_ key)
                            if (!array_key_exists($existingDetail->id, $plan['details'])) {
                                // Delete the image first (if it exists)
                                if ($existingDetail->image) {
                                    Storage::disk('website')->delete($existingDetail->image);
                                }
                                // Delete the record
                                $existingDetail->delete();
                            }
                        }
                    }
                }
                // Check if any existing MenuPlanDetails are not in the request, and delete them along with the images
                foreach ($existingMenuPlans as $existingMenuPlan) {
                    // Check if this existing menu plan ID is in the request
                    if (!array_key_exists($existingMenuPlan->id, $request->menu_plans)) {
                        // Delete the MenuPlan's details first
                        foreach ($existingMenuPlan->details as $existingDetail) {
                            if ($existingDetail->image) {
                                Storage::disk('website')->delete($existingDetail->image);
                            }
                            $existingDetail->delete();
                        }

                        // Delete the MenuPlan itself
                        $existingMenuPlan->delete();
                    }
                }
            }
            //activites
            $trip->activity()->updateOrCreate(
                ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
                [
                    'kicker' => $request->activity_kicker,
                    'heading' => $request->activity_heading,
                    'sub_heading' => $request->activity_sub_heading,
                    'texts' => $request->texts,
                ]
            );
            //boat specification
            $trip->boatSpec()->updateOrCreate(
                ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
                [
                    'kicker' => $request->boat_specs_kicker,
                    'heading' => $request->boat_specs_heading,
                    'sub_heading' => $request->boat_specs_sub_heading,
                    'description' => $request->boat_specs_description,
                ]
            );
            //safety on board
            $trip->safetyOnBoard()->updateOrCreate(
                ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
                [
                    'kicker' => $request->safety_on_board_kicker,
                    'heading' => $request->safety_on_board_heading,
                    'sub_heading' => $request->safety_on_board_sub_heading,
                    'description' => $request->safety_on_board_description,
                ]
            );
            // spa
            $trip->spa()->updateOrCreate(
                ['trip_id' => $trip->id],
                [
                    'kicker' => $request->spa_menu_kicker,
                    'heading' => $request->spa_menu_heading,
                    'sub_heading' => $request->spa_menu_sub_heading,
                ]
            );
            // Spa Menu Details
            if (isset($request->spa_menu_details)) {
                $existingSpaDetails = $trip->spaDetails;
                foreach ($request->spa_menu_details as $index => $detail) {
                    // Find or create MenuPlanDetail
                    $spaMenuDetail = $trip->spaDetails()->where('id', $index ?? null)->first() ?? new TripSpaDetails();
                    $spaMenuDetail->trip_id = $trip->id;
                    $spaMenuDetail->title = $detail['title'];
                    $spaMenuDetail->sub_title = $detail['sub_title'];
                    $spaMenuDetail->description = $detail['description'];
                    // Handle Image Update
                    if ($request->hasFile("spa_menu_details.{$index}.image") && $request->file("spa_menu_details.{$index}.image")->isValid()) {
                        $imagePath = Storage::disk('website')->put('menu_detail_images', $request->file("spa_menu_details.{$index}.image"));
                        if ($spaMenuDetail->image) {
                            Storage::disk('website')->delete($spaMenuDetail->image);
                        }
                        $spaMenuDetail->image = $imagePath;
                    }
                    // Save the MenuPlanDetail
                    $spaMenuDetail->save();
                }
                foreach ($existingSpaDetails as $existingDetail) {
                    if (!array_key_exists($existingDetail->id, $request->spa_menu_details)) {
                        if ($existingDetail->image) {
                            Storage::disk('website')->delete($existingDetail->image);
                        }
                        $existingDetail->delete();
                    }
                }
            }
        }

        return response()->json(['message' => 'Trip details updated.', 'itiImagesArray' => $itiImagesArray]);
    }

    public function updateOLD(Request $request, $id)
    {
        $trip = Trip::whereUrlSlug($id)->first();

        $defaultSections = [1, 2]; // Default sections for most packages
        $allowedSections = [3, 4, 5, 6, 7]; // Allowed values for dynamic section attachment
        $sectionForAttach = $request->input('section_id', []); // Get sections from request
        if (count($sectionForAttach) > 2) {
            $sectionForAttach = array_slice($sectionForAttach, 0, 2);
        }
        if (in_array($trip->package_id, [1, 2, 7])) {
            $sectionIds = $defaultSections;
        } elseif (in_array($trip->package_id, [3, 4])) {
            // Filter request values to include only allowed sections
            $sectionForAttach = array_intersect($sectionForAttach, $allowedSections);
            $sectionIds = array_merge($defaultSections, $sectionForAttach);
        } elseif (in_array($trip->package_id, [5, 6])) {
            $sectionIds = array_merge($defaultSections, $allowedSections); // Include all allowed sections
        } else {
            $sectionIds = $defaultSections;
        }
        $trip->sections()->sync($sectionIds);
        $validated = $request->validate([
            // 'slug' => 'required|unique:trips,slug,' . $trip->id,
            // 'package_id' => 'required|exists:packages,id',
            'template_id' => 'required',
        ], [
            'template_id.required' => 'Please select a template!',
        ]);
        $trip->update($validated);
        //main header
        $trip->mainHeader()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'kicker' => $request->main_header_kicker,
                'heading' => $request->main_header_heading,
                'sub_heading' => $request->main_header_sub_heading,
                'description' => $request->main_header_description,
            ]
        );
        // if ($request->main_header_images) {
        //     foreach ($request->main_header_images as $index => $image) {
        //         $mainHeaderImages = $trip->mainHeaderImages()->where('id', $index)->first() ?? new MainHeaderImages();
        //         $mainHeaderImages->trip_id = $trip->id;
        //         if ($request->hasFile("main_header_images.{$index}") && $request->file("main_header_images.{$index}")->isValid()) {
        //             $imagePath = Storage::disk('website')->put('main_header_images', $request->file("main_header_images.{$index}"));
        //             if ($mainHeaderImages->image) {
        //                 Storage::disk('website')->delete($mainHeaderImages->image);
        //             }
        //             $mainHeaderImages->image = $imagePath;
        //         }
        //         $mainHeaderImages->save();
        //     }
        // }
        // //deleting Trip Overview images
        // $existingImages = $trip->mainHeaderImages;
        // foreach ($existingImages as $existingImage) {
        //     if (!in_array($existingImage->id, $request->input('main_header_image_exist', []))) {
        //         if ($existingImage->image) {
        //             Storage::disk('website')->delete($existingImage->image);
        //         }
        //         $existingImage->delete();
        //     }
        // }
        //tripItinerarySummary
        $trip->tripItinerarySummary()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'kicker' => $request->itinerary_summary_kicker,
                'heading' => $request->itinerary_summary_heading,
                'sub_heading' => $request->itinerary_summary_sub_heading,
            ]
        );
        //itinerary
        // Get existing itineraries for the trip
        if ($request->itinerary) {
            $itineraries = $trip->itineraries;
            foreach ($request->itinerary as $index => $itinerRequest) {
                // Create or update the itinerary
                $itiner = $trip->itineraries()->updateOrCreate(
                    ['trip_id' => $trip->id, 'id' => $index],  // Assuming $index is the itinerary ID
                    $itinerRequest  // Pass the specific itinerary data
                );

                // Handle images if present in the request
                if (isset($itinerRequest['images'])) {
                    foreach ($itinerRequest['images'] as $imageIndex => $image) {
                        // Either find the existing image or create a new one
                        $itinerImage = $itiner->images()->where('id', $imageIndex)->first() ?? new ItineraryImage();
                        $itinerImage->itinerary_id = $itiner->id;  // Associate the image with the itinerary

                        // If a new file is uploaded and it's valid
                        if ($request->hasFile("itinerary.{$index}.images.{$imageIndex}") && $request->file("itinerary.{$index}.images.{$imageIndex}")->isValid()) {
                            $imagePath = Storage::disk('website')->put('itinerary_images', $request->file("itinerary.{$index}.images.{$imageIndex}"));

                            // Delete the old image if exists
                            if ($itinerImage->image) {
                                Storage::disk('website')->delete($itinerImage->image);
                            }

                            $itinerImage->image = $imagePath;
                        }
                        // Save or update the image record
                        $itinerImage->save();
                    }
                }
                // Handle image deletions for images no longer in the request
                $existingImages = $itiner->images;  // Assuming a one-to-many relationship with images
                //working
                // foreach ($existingImages as $existingImage) {
                //     if (!array_key_exists($existingImage->id, $itinerRequest['imagesOLD'])) {
                //         // Delete image and record if it's not in the request anymore
                //         if ($existingImage->image) {
                //             Storage::disk('website')->delete($existingImage->image);
                //         }
                //         $existingImage->delete();
                //     }
                // }
            }
            // Handle deletion of itineraries not in the request
            foreach ($itineraries as $existingItinerary) {
                if (!array_key_exists($existingItinerary->id, $request->itinerary)) {
                    // Delete images associated with the itinerary
                    foreach ($existingItinerary->images as $existingImage) {
                        if ($existingImage->image) {
                            Storage::disk('website')->delete($existingImage->image);
                        }
                        $existingImage->delete();
                    }

                    // Delete the itinerary itself
                    $existingItinerary->delete();
                }
            }
        }

        //tripItineraryMap
        $trip->tripItineraryMap()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'kicker' => $request->itinerary_map_kicker,
                'heading' => $request->itinerary_map_heading,
                'sub_heading' => $request->itinerary_map_sub_heading,
            ]
        );

        //crew
        $trip->crewDetail()->updateOrCreate(
            ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
            [
                'kicker' => $request->crew_detail_kicker,
                'heading' => $request->crew_detail_heading,
                'sub_heading' => $request->crew_detail_sub_heading,
            ]
        );
        // if ($request->crews) {
        //     $trip->crews()->sync($request->crews);
        // }
        if ($request->crews) {
            $orderedCrews = explode(',', $request->ordered_crews); // Parse ordered crew IDs from the hidden input
            $crewData = [];
            foreach ($orderedCrews as $index => $crewId) {
                $crewData[$crewId] = ['sequence' => $index + 1]; // Add sequence data
            }
            $trip->crews()->sync($crewData);
        }

        if (in_array($trip->package_id, [3, 4, 5, 6])) {
            // menu
            $trip->menu()->updateOrCreate(
                ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
                [
                    'kicker' => $request->menu_kicker,
                    'heading' => $request->menu_heading,
                    'sub_heading' => $request->menu_sub_heading,
                ]
            );
            //menu plans
            $existingMenuPlans = $trip->menuPlans;
            if ($request->menu_plans) {
                foreach ($request->menu_plans as $index => $plan) {
                    // Find or create MenuPlan
                    $menuPlan = $trip->menuPlans()->where('id', $index ?? null)->first() ?? new MenuPlan();
                    $menuPlan->trip_id = $trip->id;
                    $menuPlan->name = $plan['name'];
                    $menuPlan->save();

                    // Update Menu Plan Details
                    if (isset($plan['details'])) {
                        $existingDetails = $menuPlan->details;
                        foreach ($plan['details'] as $detailIndex => $detail) {
                            // Find or create MenuPlanDetail
                            $menuDetail = $menuPlan->details()->where('id', $detailIndex ?? null)->first() ?? new MenuPlanDetail();
                            $menuDetail->menu_plan_id = $menuPlan->id; // Associate with Menu Plan

                            $menuDetail->title = $detail['title'];
                            $menuDetail->text = $detail['text'];

                            // Handle Image Update
                            if ($request->hasFile("menu_plans.{$index}.details.{$detailIndex}.image") && $request->file("menu_plans.{$index}.details.{$detailIndex}.image")->isValid()) {
                                // Upload new image
                                $imagePath = Storage::disk('website')->put('menu_detail_images', $request->file("menu_plans.{$index}.details.{$detailIndex}.image"));

                                // If there's an old image, delete it
                                if ($menuDetail->image) {
                                    Storage::disk('website')->delete($menuDetail->image);
                                }

                                // Save the new image path
                                $menuDetail->image = $imagePath;
                            }

                            // Save the MenuPlanDetail
                            $menuDetail->save();
                        }
                        foreach ($existingDetails as $existingDetail) {
                            // Check if the existing detail ID is in the request details (not new_ key)
                            if (!array_key_exists($existingDetail->id, $plan['details'])) {
                                // Delete the image first (if it exists)
                                if ($existingDetail->image) {
                                    Storage::disk('website')->delete($existingDetail->image);
                                }
                                // Delete the record
                                $existingDetail->delete();
                            }
                        }
                    }
                }
                // Check if any existing MenuPlanDetails are not in the request, and delete them along with the images
                foreach ($existingMenuPlans as $existingMenuPlan) {
                    // Check if this existing menu plan ID is in the request
                    if (!array_key_exists($existingMenuPlan->id, $request->menu_plans)) {
                        // Delete the MenuPlan's details first
                        foreach ($existingMenuPlan->details as $existingDetail) {
                            if ($existingDetail->image) {
                                Storage::disk('website')->delete($existingDetail->image);
                            }
                            $existingDetail->delete();
                        }

                        // Delete the MenuPlan itself
                        $existingMenuPlan->delete();
                    }
                }
            }
            //activites
            $trip->activity()->updateOrCreate(
                ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
                [
                    'kicker' => $request->activity_kicker,
                    'heading' => $request->activity_heading,
                    'sub_heading' => $request->activity_sub_heading,
                    'texts' => $request->texts,
                ]
            );
            //activity images
            // if ($request->activity_images) {
            //     $existingActivityImages = $trip->activityImages;
            //     foreach ($request->activity_images as $index => $image) {
            //         $activityImages = $trip->activityImages()->where('id', $index)->first() ?? new ActivityImages();
            //         $activityImages->trip_id = $trip->id;
            //         if ($request->hasFile("activity_images.{$index}") && $request->file("activity_images.{$index}")->isValid()) {
            //             $imagePath = Storage::disk('website')->put('activity_images', $request->file("activity_images.{$index}"));
            //             if ($activityImages->image) {
            //                 Storage::disk('website')->delete($activityImages->image);
            //             }
            //             $activityImages->image = $imagePath;
            //         }
            //         $activityImages->save();
            //     }

            //     foreach ($existingActivityImages as $existingImage) {
            //         if (!in_array($existingImage->id, $request->input('activity_image_exist', []))) {
            //             if ($existingImage->image) {
            //                 Storage::disk('website')->delete($existingImage->image);
            //             }
            //             $existingImage->delete();
            //         }
            //     }
            // }
            //boat specification
            $trip->boatSpec()->updateOrCreate(
                ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
                [
                    'kicker' => $request->boat_specs_kicker,
                    'heading' => $request->boat_specs_heading,
                    'sub_heading' => $request->boat_specs_sub_heading,
                    'description' => $request->boat_specs_description,
                ]
            );
            // if ($request->boat_specs_images) {
            //     $existingboatSpecImages = $trip->boatSpecImages;
            //     foreach ($request->boat_specs_images as $index => $image) {
            //         $boatSpecImages = $trip->boatSpecImages()->where('id', $index)->first() ?? new BoatSpecImages();
            //         $boatSpecImages->trip_id = $trip->id;
            //         if ($request->hasFile("boat_specs_images.{$index}") && $request->file("boat_specs_images.{$index}")->isValid()) {
            //             $imagePath = Storage::disk('website')->put('boat_specs_images', $request->file("boat_specs_images.{$index}"));
            //             if ($boatSpecImages->image) {
            //                 Storage::disk('website')->delete($boatSpecImages->image);
            //             }
            //             $boatSpecImages->image = $imagePath;
            //         }
            //         $boatSpecImages->save();
            //     }

            //     foreach ($existingboatSpecImages as $existingImage) {
            //         if (!in_array($existingImage->id, $request->input('boat_specs_image_exist', []))) {
            //             if ($existingImage->image) {
            //                 Storage::disk('website')->delete($existingImage->image);
            //             }
            //             $existingImage->delete();
            //         }
            //     }
            // }
            //safety on board
            $trip->safetyOnBoard()->updateOrCreate(
                ['trip_id' => $trip->id], // Look for an existing MainHeader by trip_id
                [
                    'kicker' => $request->safety_on_board_kicker,
                    'heading' => $request->safety_on_board_heading,
                    'sub_heading' => $request->safety_on_board_sub_heading,
                    'description' => $request->safety_on_board_description,
                ]
            );
            // //safety on board images
            // if ($request->safety_on_board_images) {
            //     $existingsafetyOnBoardImages = $trip->safetyOnBoardImages;
            //     foreach ($request->safety_on_board_images as $index => $image) {
            //         $safetyOnBoardImages = $trip->safetyOnBoardImages()->where('id', $index)->first() ?? new SafetyOnBoardImage();
            //         $safetyOnBoardImages->trip_id = $trip->id;
            //         if ($request->hasFile("safety_on_board_images.{$index}") && $request->file("safety_on_board_images.{$index}")->isValid()) {
            //             $imagePath = Storage::disk('website')->put('safety_on_board_images', $request->file("safety_on_board_images.{$index}"));
            //             if ($safetyOnBoardImages->image) {
            //                 Storage::disk('website')->delete($safetyOnBoardImages->image);
            //             }
            //             $safetyOnBoardImages->image = $imagePath;
            //         }
            //         $safetyOnBoardImages->save();
            //     }

            //     foreach ($existingsafetyOnBoardImages as $existingImage) {
            //         if (!in_array($existingImage->id, $request->input('safety_on_board_image_exist', []))) {
            //             if ($existingImage->image) {
            //                 Storage::disk('website')->delete($existingImage->image);
            //             }
            //             $existingImage->delete();
            //         }
            //     }
            // }
            // spa
            $trip->spa()->updateOrCreate(
                ['trip_id' => $trip->id],
                [
                    'kicker' => $request->spa_menu_kicker,
                    'heading' => $request->spa_menu_heading,
                    'sub_heading' => $request->spa_menu_sub_heading,
                ]
            );
            // Spa Menu Details
            if (isset($request->spa_menu_details)) {
                $existingSpaDetails = $trip->spaDetails;
                foreach ($request->spa_menu_details as $index => $detail) {
                    // Find or create MenuPlanDetail
                    $spaMenuDetail = $trip->spaDetails()->where('id', $index ?? null)->first() ?? new TripSpaDetails();
                    $spaMenuDetail->trip_id = $trip->id;
                    $spaMenuDetail->title = $detail['title'];
                    $spaMenuDetail->sub_title = $detail['sub_title'];
                    $spaMenuDetail->description = $detail['description'];
                    // Handle Image Update
                    if ($request->hasFile("spa_menu_details.{$index}.image") && $request->file("spa_menu_details.{$index}.image")->isValid()) {
                        $imagePath = Storage::disk('website')->put('menu_detail_images', $request->file("spa_menu_details.{$index}.image"));
                        if ($spaMenuDetail->image) {
                            Storage::disk('website')->delete($spaMenuDetail->image);
                        }
                        $spaMenuDetail->image = $imagePath;
                    }
                    // Save the MenuPlanDetail
                    $spaMenuDetail->save();
                }
                foreach ($existingSpaDetails as $existingDetail) {
                    if (!array_key_exists($existingDetail->id, $request->spa_menu_details)) {
                        if ($existingDetail->image) {
                            Storage::disk('website')->delete($existingDetail->image);
                        }
                        $existingDetail->delete();
                    }
                }
            }
        }

        // return redirect()->back();
        return response()->json(['message' => 'Trip details updated.']);
    }


    public function destroy($id)
    {
        $trip = Trip::findOrFail($id);
        $trip->delete();

        return to_route('trips.index');
    }
    public function updateBasic(TripRequest $request, $id)
    {
        $trip = Trip::whereUrlSlug($id)->firstOrFail();
        $trip->name = $request->input('name');
        $trip->start_date = $request->input('start_date');
        $trip->end_date = $request->input('end_date');
        $trip->url_slug = Str::slug($request->input('url_slug'));
        $trip->duration = $request->input('duration');
        $trip->save();
        return redirect()->back();
    }

    public function toggleStatus($slug)
    {
        $trip = Trip::whereSlug($slug)->firstOrFail();
        $trip->status = $trip->status === 'active' ? 'inactive' : 'active';
        $trip->save();
        return response()->json(['status' => 'success', 'new_status' => $trip->status]);
    }

    private function makeSlugUnique($slug)
    {
        return $slug . now()->format('YmdHis');
    }
    public function tripImageAdd(Request $request, $tripId, $relation)
    {
        $trip = Trip::findOrFail($tripId);
        $request->validate([
            'file' => 'required|file|max:10240',
        ]);
        $folderMappings = [
            'mainHeaderImages' => 'main_header_images',
            'activityImages' => 'activity_images',
            'boatSpecImages' => 'boat_specs_images',
            'safetyOnBoardImages' => 'safety_on_board_images',
            'menuPlanImages' => 'menu_plan_images',
            'spaMenuImages' => 'spa_menu_images',
        ];
        $folder = $folderMappings[$relation] ?? 'trip_images';
        $path = $this->storeImage($folder, $request->file('file'));
        $name = $request->file('file')->getClientOriginalName();
        if (!method_exists($trip, $relation)) {
            return response()->json(['error' => 'Invalid relation'], 400);
        }
        $image = $trip->$relation()->create([
            'image' => $path,
        ]);
        return response()->json([
            'path' => $path,
            'id' => $image->id,
            'name' => $name,
        ]);
    }


    public function tripImageAddOLD(Request $request, $tripId)
    {
        $trip = Trip::find($tripId);
        $request->validate([
            'file' => 'required|file|max:10240',
        ]);
        $path = $this->storeImage("main_header_images", $request->file('file'));
        $name = $request->file('file')->getClientOriginalName();
        $tripMainHeaderImage = $trip->mainHeaderImages()->create([
            'image' => $path,
        ]);
        return response()->json(['path' => $path, 'id' => $tripMainHeaderImage->id, 'name' => $name]);
    }

    public function tripImageDelete(Request $request, $tripId, $relation)
    {
        $trip = Trip::find($tripId);
        $id = $request->get('id'); // Retrieve the image ID from the request
        $tripImage = $trip->$relation()->findOrFail($id);
        $this->deleteImage($tripImage->image);
        $tripImage->delete();
        return response()->json(['message' => 'File deleted successfully']);
    }
    public function itiImageAdd(Request $request, $itiId)
    {
        $itiner = Itinerary::find($itiId);
        $request->validate([
            'file' => 'required|file|max:10240',
        ]);
        $path = $this->storeImage("itinerary_images", $request->file('file'));
        $name = $request->file('file')->getClientOriginalName();
        $itiImage = $itiner->images()->create([
            'image' => $path,
        ]);
        return response()->json(['path' => $path, 'id' => $itiImage->id, 'name' => $name]);
    }

    public function itiImageDelete(Request $request, $itiId)
    {
        $itiner = Itinerary::find($itiId);
        $id = $request->get('id'); // Retrieve the image ID from the request
        $itiImage = $itiner->images()->findOrFail($id);
        $this->deleteImage($itiImage->image);
        $itiImage->delete();
        return response()->json(['message' => 'File deleted successfully']);
    }

    public function convertPdfToImagesAndStore($pdfFile, $trip, $folderName = 'menu_detail_images', $relation)
    {
        $apiKey = env('PDFCO_API_KEY');
        $client = new Client();

        // Delete existing images from database & folder before saving new ones
        $oldImages = $trip->$relation()->where('type', 'pdf')->get();

        foreach ($oldImages as $oldImage) {
            // Remove file from storage
            Storage::disk('website')->delete($oldImage->image);
            // Remove record from the database
            $oldImage->delete();
        }

        // Store the PDF temporarily for processing
        $pdfPath = $pdfFile->store('temp');
        $pdfFullPath = storage_path("app/" . $pdfPath);

        try {
            // Step 1: Upload the PDF to PDF.co temporary storage
            $uploadResponse = $client->post("https://api.pdf.co/v1/file/upload", [
                'headers' => ['x-api-key' => $apiKey],
                'multipart' => [
                    [
                        'name' => 'file',
                        'contents' => fopen($pdfFullPath, 'r'),
                        'filename' => $pdfFile->getClientOriginalName(),
                    ]
                ]
            ]);

            $uploadBody = json_decode($uploadResponse->getBody(), true);

            if (!isset($uploadBody['url'])) {
                throw new \Exception('Failed to upload PDF');
            }

            $pdfUrl = $uploadBody['url'];
            Log::info("Uploaded File URL: $pdfUrl");

            // Step 2: Get PDF info to get total page count
            $infoResponse = $client->post("https://api.pdf.co/v1/pdf/info", [
                'headers' => ['x-api-key' => $apiKey],
                'json' => ['url' => $pdfUrl],
            ]);

            $infoBody = json_decode($infoResponse->getBody(), true);
            if (!isset($infoBody['info']['PageCount'])) {
                throw new \Exception('Invalid PDF format or file corrupted.');
            }

            $totalPages = (int) $infoBody['info']['PageCount'];
            Log::info("Total Pages Found: $totalPages");

            // Step 3: Convert each page to image
            foreach (range(0, $totalPages - 1) as $i) {
                try {
                    Log::info("Converting Page $i...");

                    $convertResponse = $client->post("https://api.pdf.co/v1/pdf/convert/to/jpg", [
                        'headers' => ['x-api-key' => $apiKey],
                        'json' => ['url' => $pdfUrl, 'pages' => (string) $i, 'outputFormat' => 'jpg'],
                    ]);

                    $convertBody = json_decode($convertResponse->getBody(), true);
                    if (isset($convertBody['urls'][0])) {
                        $imageUrl = $convertBody['urls'][0];
                        Log::info("Page $i converted: " . $imageUrl);

                        // Download the image content from the URL
                        $imageContent = file_get_contents($imageUrl);
                        $imageName = "{$relation}_{$trip->id}_" . ($i + 1) . ".jpg";

                        // Define the storage path
                        $storagePath = $folderName . '/' . $imageName;

                        // Step 4: Store the image in the `website` disk inside the trip-specific folder
                        Storage::disk('website')->put($storagePath, $imageContent);

                        // Step 5: Save the image path in the database
                        $trip->$relation()->create([
                            'trip_id' => $trip->id,
                            'image' => $storagePath,
                            'type' => 'pdf', // Indicating this is a converted image from a PDF
                        ]);
                    } else {
                        Log::error("Conversion failed for page $i: 'url' key missing in response.");
                    }
                } catch (\Exception $e) {
                    Log::error("Conversion error for page $i: " . $e->getMessage());
                }
            }

            // Step 6: Delete Temporary PDF File
            Storage::delete($pdfPath);

            return "PDF Converted and Images saved successfully!";
        } catch (\Exception $e) {
            Log::error("Error: " . $e->getMessage());
        }
    }
}
