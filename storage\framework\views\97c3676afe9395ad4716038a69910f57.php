

<?php $__env->startPush('css'); ?>
    
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <?php if(auth()->user()->hasRole('admin') || auth()->user()->hasRole('developer')): ?>
        <section class="subscription_management">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="custom_cards_design">
                            <h2>My Subscriptions </h2>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="custom_card">
                                        <div class="custom_justify_between subs_card_clr">
                                            <h1>Gold</h1>
                                            <div class="dropdown">
                                                <button class="dropdown-toggle" type="button" id="dropdownMenuButton11"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                    <li><a href="#!" class="dropdown-item"
                                                            data-bs-target="#edit_subscription" data-bs-toggle="modal"><i
                                                                class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                    <li><a href="#!" class="dropdown-item"><i
                                                                class="fa-solid fa-close"></i>Delete</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="amount">
                                            <label>Amount:</label>
                                            <span>$1000</span>
                                        </div>
                                        <div class="custom_select">
                                            <label>Section Add:</label>
                                            <div>
                                                <select multiple
                                                    class="custom_multi_select form-select form-select-transparent"
                                                    data-control="select2" data-placeholder="Section">
                                                    <option></option>
                                                    <option value="1">Itinerary</option>
                                                    <option value="2">Meals</option>
                                                    <option value="3">Crew Members</option>
                                                    <option value="4">Boat Specifications</option>
                                                    <option value="5">Activities</option>
                                                    <option value="6">Add Ons</option>
                                                    <option value="7">Safety Onboard</option>
                                                    <option value="8">Content</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="custom_justify_between ">
                                            <h3>Total Trips</h3>
                                            <div class="txt_field">
                                                <input type="number" class="total_tips_count" value="03">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="custom_card">
                                        <div class="custom_justify_between subs_card_clr">
                                            <h1>Gold</h1>
                                            <div class="dropdown">
                                                <button class="dropdown-toggle" type="button" id="dropdownMenuButton11"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                    <li><a href="#!" class="dropdown-item"
                                                            data-bs-target="#edit_subscription" data-bs-toggle="modal"><i
                                                                class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                    <li><a href="#!" class="dropdown-item"><i
                                                                class="fa-solid fa-close"></i>Delete</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="amount">
                                            <label>Amount:</label>
                                            <span>$1000</span>
                                        </div>
                                        <div class="custom_select">
                                            <label>Section Add:</label>
                                            <div>
                                                <select multiple
                                                    class="custom_multi_select form-select form-select-transparent"
                                                    data-control="select2" data-placeholder="Section">
                                                    <option></option>
                                                    <option value="1">Itinerary</option>
                                                    <option value="2">Meals</option>
                                                    <option value="3">Crew Members</option>
                                                    <option value="4">Boat Specifications</option>
                                                    <option value="5">Activities</option>
                                                    <option value="6">Add Ons</option>
                                                    <option value="7">Safety Onboard</option>
                                                    <option value="8">Content</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="custom_justify_between ">
                                            <h3>Total Trips</h3>
                                            <div class="txt_field">
                                                <input type="number" class="total_tips_count" value="03">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="custom_card">
                                        <div class="custom_justify_between subs_card_clr">
                                            <h1>Gold</h1>
                                            <div class="dropdown">
                                                <button class="dropdown-toggle" type="button" id="dropdownMenuButton11"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                    <li><a href="#!" class="dropdown-item"
                                                            data-bs-target="#edit_subscription" data-bs-toggle="modal"><i
                                                                class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                    <li><a href="#!" class="dropdown-item"><i
                                                                class="fa-solid fa-close"></i>Delete</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="amount">
                                            <label>Amount:</label>
                                            <span>$1000</span>
                                        </div>
                                        <div class="custom_select">
                                            <label>Section Add:</label>
                                            <div>
                                                <select multiple
                                                    class="custom_multi_select form-select form-select-transparent"
                                                    data-control="select2" data-placeholder="Section">
                                                    <option></option>
                                                    <option value="1">Itinerary</option>
                                                    <option value="2">Meals</option>
                                                    <option value="3">Crew Members</option>
                                                    <option value="4">Boat Specifications</option>
                                                    <option value="5">Activities</option>
                                                    <option value="6">Add Ons</option>
                                                    <option value="7">Safety Onboard</option>
                                                    <option value="8">Content</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="custom_justify_between ">
                                            <h3>Total Trips</h3>
                                            <div class="txt_field">
                                                <input type="number" class="total_tips_count" value="03">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        

        <div class="modal fade custom_modal" id="edit_subscription" tabindex="-1" aria-labelledby="createModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title" id="createModalLabel">Upgrade Subscription</h1>
                        
                    </div>
                    <div class="modal-body ">
                        <form>
                            <div class="row custom_row">
                                <div class="col-md-12">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Upgrade To:</label>
                                        <select class="form-select" aria-label="Default select example" id="">
                                            <option selected disabled>Gold</option>
                                            <option value="1">Silver</option>
                                            <option value="2">Bronze</option>
                                            <option value="3">Gold</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label>Amount:</label>
                                        <input type="number" class="form-control" value="500.00" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label>Total Trips:</label>
                                        <input type="number" class="total_tips_count form-control" value="03"
                                            required>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="txt_field custom_select">
                                        <label>Section Add:</label>
                                        <div class="select_modal">
                                            <select multiple
                                                class="custom_multi_select form-select form-select-transparent"
                                                data-control="select2" data-placeholder="Section">
                                                <option></option>
                                                <option value="1">Itinerary</option>
                                                <option value="2">Meals</option>
                                                <option value="3">Crew Members</option>
                                                <option value="4">Boat Specifications</option>
                                                <option value="5">Activities</option>
                                                <option value="6">Add Ons</option>
                                                <option value="7">Safety Onboard</option>
                                                <option value="8">Content</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn_dark_green">Update</button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn_transparent"
                                        data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php elseif(auth()->user()->hasRole('owner')): ?>
        <section class="subscription_sec">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="subscription_wrapper custom_cards_design">
                            <h1>My Subscription</h1>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="basic_package_wrapper">
                                        <span>Basic Package</span>
                                        <h1>$500.00</h1>
                                        <h2>Ideal for extended journeys with added services.</h2>
                                        <ul class="basic_pkg_pts">
                                            <li><i class="fa-solid fa-circle-arrow-right"></i>
                                                <h3>Full Trip Planning & Custom Itineraries</h3>
                                            </li>
                                            <li><i class="fa-solid fa-circle-arrow-right"></i>
                                                <h3>Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)</h3>
                                            </li>
                                            <li><i class="fa-solid fa-circle-arrow-right"></i>
                                                <h3>24/7 Concierge Support</h3>
                                            </li>
                                            <li class="blur_points"><i class="fa-solid fa-circle-arrow-right"></i>
                                                <h3>24/7 Concierge Support</h3>
                                            </li>
                                            <li class="blur_points"><i class="fa-solid fa-circle-arrow-right"></i>
                                                <h3>24/7 Concierge Support</h3>
                                            </li>
                                        </ul>
                                        <h4>Upgrade to Unlock More Features.</h4>
                                        <button type="button" class="btn  btn_grey"
                                            data-bs-target="#update_subscription" data-bs-toggle="modal">Upgrade<img
                                                src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg"> </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        

        <div class="modal fade custom_modal" id="update_subscription" tabindex="-1" aria-labelledby="createModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title" id="createModalLabel">Upgrade Subscription</h1>
                        
                    </div>
                    <div class="modal-body ">
                        <form>
                            <div class="row custom_row">
                                <div class="col-md-12">
                                    <div class="txt_field">
                                        <label>Current Subscription Name:</label>
                                        <input type="text" class="form-control" value="Basic" required>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Upgrade To:</label>
                                        <select class="form-select" aria-label="Default select example" id="">
                                            <option selected disabled>Gold</option>
                                            <option value="1">Silver</option>
                                            <option value="2">Bronze</option>
                                            <option value="3">Gold</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Payment Type:</label>
                                        <select class="form-select" aria-label="Default select example" id="">
                                            <option selected disabled>Monthly</option>
                                            <option value="1">Monthly</option>
                                            <option value="2">Yearly</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label>Amount:</label>
                                        <input type="number" class="form-control" value="500.00" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn_dark_green">Pay</button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn_transparent"
                                        data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.custom_multiselect').select2({
                placeholder: "Select An Option",
                allowClear: true
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\SubscriptionManagement\subscription_index.blade.php ENDPATH**/ ?>