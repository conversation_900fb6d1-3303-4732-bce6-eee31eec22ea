<?php $__env->startPush('css'); ?>
    <style>
        .navbar.navbar-expand-lg.packages_navbar,
        footer {
            display: none;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <!--begin::Authentication - Sign-in -->

    <section class="login_pg_sec">
        <div class="container-fluid">
            <div class="row custom_login_row">
                <div class="col-md-6">
                    <div class=" login_pg_img_sec">
                        <!--begin::Aside-->
                        <img src="<?php echo e(asset('website')); ?>/assets/images/image_round.png ">
                        <!--begin::Logo-->
                        
                        
                        
                        
                        <!--end::Title-->
                        <!--begin::Aside-->
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="sign_in_wrapper_whole">
                        <form class="form" id="sign_in_form" novalidate="novalidate" method="POST"
                            action="<?php echo e(route('login')); ?>">
                            <?php echo csrf_field(); ?>
                            <div class="sign_in_fields">
                                <!--begin::Heading-->
                                
                                
                                
                                
                                
                                
                                
                                
                                <!--begin::Heading-->
                                <!--begin::Login options-->
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                <!--end::Login options-->
                                <!--begin::Separator-->
                                
                                
                                
                                <!--end::Separator-->
                                <!--begin::Input group=-->
                                <div class="sign_in_heading">
                                    <h3>Sign In</h3>
                                </div>
                                <div class="login_fields_wrapper">
                                    <div class=" input_filed_wrapper_sign form-floating">
                                        <!--begin::Email-->
                                        <input id="email " type="email" placeholder=""
                                            class="form-control  bg-transparent <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            name="email" value="<?php echo e(old('email')); ?>" required autocomplete="email"
                                            autofocus>
                                        <label for="email">Email*</label>
                                        <p> Please Enter Email</p>
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback" role="alert">
                                                <strong><?php echo e($message); ?></strong>
                                            </span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                                        <!--end::Email-->
                                    </div>
                                    <!--end::Input group=-->
                                    <div class="input_filed_wrapper_sign form-floating ">
                                        <!--begin::Password-->
                                        <input id="password" type="password" placeholder=""
                                            class="pass_show_hide form-control bg-transparent <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            name="password" required autocomplete="current-password">
                                        <label for="password">Password*</label>
                                        <i class="fa-solid fa-eye eye_np input-icon"></i>
                                        <i class="fa-solid fa-eye-slash eye_slash_np input-icon"></i>
                                        <p>Please Enter Password</p>
                                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback" role="alert">
                                                <strong><?php echo e($message); ?></strong>
                                            </span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <!--end::Password-->
                                    </div>
                                </div>

                                <!--end::Input group=-->
                                <!--begin::Wrapper-->
                                <div class="input_field_belwo_wapper">
                                    <div></div>
                                    <!--begin::Link-->
                                    <div class="forget_checkbox_wrapper">
                                        <div>
                                            <input id="remember_me" type="checkbox">
                                            <label for="remember_me">Remember Me</label>
                                        </div>
                                        <a href="<?php echo e(route('password.request')); ?>" class="">Forgot Password ?</a>
                                    </div>
                                    <!--end::Link-->
                                </div>
                                <!--end::Wrapper-->
                                <!--begin::Submit button-->
                                
                                
                                
                                
                                
                                

                                
                                <div class="  btn_wrapper">
                                    <button type="submit" id="sign_in_submit"
                                        class=" btn_global yellow_btn arrow_up_right_btn_img">
                                        <!--begin::Indicator label-->
                                        <span class="indicator-label">Sign In</span>

                                        
                                        
                                        
                                        <!--end::Indicator label-->
                                        <!--begin::Indicator progress-->
                                        <span class="indicator-progress">Please wait...
                                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                        <!--end::Indicator progress-->
                                    </button>
                                </div>
                                <!--end::Submit button-->
                                <!--begin::Sign up-->
                                <p class="already_an_account">Not a Member yet?
                                    <a href="<?php echo e(route('register')); ?>" class="">Sign up</a>
                                </p>
                                <!--end::Sign up-->
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    
    <script>
        // $(document).on('click','.password_field_wrapper .toggle_password',function(){
        //     $(this).toggleClass("fa-eye fa-eye-slash");
        //
        //     var input = $(this).closest('.password_field_wrapper').find('input').attr("type");
        //
        //     if ($(this).closest('.password_field_wrapper').find('input').attr("type") === "password") {
        //         $(this).closest('.password_field_wrapper').find('input').attr("type", "text");
        //     } else {
        //         $(this).closest('.password_field_wrapper').find('input').attr("type", "password");
        //     }
        // });
        $(document).ready(function() {
            $(".eye_np").hide(); // Initially hide the open eye icon

            $(".input-icon").click(function() {
                var input = $(".pass_show_hide");

                // Toggle input type and icon visibility
                if (input.attr("type") === "password") {
                    input.attr("type", "text");
                    $(".eye_slash_np").hide();
                    $(".eye_np").show();
                } else {
                    input.attr("type", "password");
                    $(".eye_np").hide();
                    $(".eye_slash_np").show();
                }
            });
            $("#sign_in_form").submit(function(event) {
                event.preventDefault(); // Prevent default form submission for testing

                $("#sign_in_submit").text("Signing In...").prop("disabled", true);

                setTimeout(() => {
                    this.submit(); // Uncomment this line for actual submission
                }, 2000);
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\auth\login.blade.php ENDPATH**/ ?>