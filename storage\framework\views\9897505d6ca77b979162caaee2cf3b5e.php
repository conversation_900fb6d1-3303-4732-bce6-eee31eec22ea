<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Waypoints in Directions</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA0z3VFl1wO12-FWBFTC1_fdfOMJgaL9R4&libraries=places&callback=initMap"
        defer></script>
    <style>
        #map {
            height: 400px;
            width: 100%;
        }

        #routes {
            margin-top: 20px;
        }

        .route-container {
            margin-bottom: 10px;
            position: relative;
        }

        .remove-btn {
            color: red;
            cursor: pointer;
            font-size: 20px;
            position: absolute;
            top: 5px;
            right: 10px;
        }
    </style>
</head>

<body>

    <div id="container">
        <div id="map"></div>
        <div id="sidebar">
            <div id="routes">
                <div class="route-container" id="route1">
                    <b>Start:</b>
                    <input type="text" class="start-point" id="start1" placeholder="Enter starting location">
                    <br />
                    <b>End:</b>
                    <input type="text" class="end-point" id="end1" placeholder="Enter destination">
                    <input type="hidden" class="start-lat" id="start-lat1">
                    <input type="hidden" class="start-lng" id="start-lng1">
                    <input type="hidden" class="end-lat" id="end-lat1">
                    <input type="hidden" class="end-lng" id="end-lng1">
                    <span class="remove-btn" onclick="removeRoute('route1')">×</span>
                </div>
            </div>
            <input type="button" value="Add More" id="addMore" />
            <input type="submit" id="submit" />
            <div id="directions-panel"></div>
        </div>
    </div>

    <script>
        let routeCount = $(".route-container").length;
        let markers = []; // To store markers on the map

        function initMap() {
            const map = new google.maps.Map($("#map")[0], {
                zoom: 6,
                center: {
                    lat: 41.85,
                    lng: -87.65
                }, // Default center
            });

            // Initialize autocomplete for start and end fields
            initAutocomplete();

            // Event listener for the Submit button
            $("#submit").on("click", () => {
                displayPinsOnMap(map);
            });

            // Event listener for the Add More button
            $("#addMore").on("click", addRoute);
        }

        // Initialize Autocomplete for start and end fields
        function initAutocomplete() {
            $("input.start-point, input.end-point").each(function() {
                new google.maps.places.Autocomplete(this);
            });
        }

        // Function to display pins (markers) for start, end, and waypoints
        function displayPinsOnMap(map) {
            const routeContainers = $(".route-container");

            // Clear previous markers
            markers.forEach(marker => marker.setMap(null));
            markers = [];

            // Geocoder to get LatLng from address
            const geocoder = new google.maps.Geocoder();
            const distanceService = new google.maps.DistanceMatrixService();

            // Function to geocode an address and place a marker
            function geocodeAddress(address, title, latField, lngField) {
                geocoder.geocode({
                    address: address
                }, (results, status) => {
                    if (status === google.maps.GeocoderStatus.OK) {
                        const location = results[0].geometry.location;

                        // Set hidden latitude and longitude values
                        $(latField).val(location.lat());
                        $(lngField).val(location.lng());

                        // Place marker on the map
                        const marker = new google.maps.Marker({
                            position: location,
                            map: map,
                            title: title,
                        });
                        markers.push(marker);
                        map.setCenter(location); // Optionally, center the map on the first marker
                    } else {
                        console.error("Geocode was not successful for the following reason: " + status);
                    }
                });
            }

            // Collect all start and end points
            let waypoints = [];
            routeContainers.each((index, routeContainer) => {
                const startPoint = $(routeContainer).find(".start-point").val();
                const endPoint = $(routeContainer).find(".end-point").val();

                // Geocode the start point for the first route (Location 1)
                if (index === 0 && startPoint) {
                    geocodeAddress(startPoint, 'Location 1', '.start-lat', '.start-lng');
                }

                // Geocode the end points for all routes (Location 2, Location 3, etc.)
                if (endPoint) {
                    geocodeAddress(endPoint, `Location ${index + 2}`, '.end-lat', '.end-lng');
                    waypoints.push(endPoint); // Collect end points
                }
            });

            // Once all locations are geocoded, calculate the distances between each pair
            setTimeout(() => {
                const origins = waypoints.map(point => new google.maps.LatLng(parseFloat($(`#start-lat${routeCount}`).val()), parseFloat($(`#start-lng${routeCount}`).val())));
                const destinations = waypoints.map(point => new google.maps.LatLng(parseFloat($(`#end-lat${routeCount}`).val()), parseFloat($(`#end-lng${routeCount}`).val())));

                distanceService.getDistanceMatrix({
                    origins: origins,
                    destinations: destinations,
                    travelMode: 'DRIVING'
                }, (response, status) => {
                    if (status === 'OK') {
                        let totalDistance = 0;
                        const summaryPanel = $("#directions-panel");

                        summaryPanel.empty();
                        response.rows[0].elements.forEach((element, index) => {
                            const segmentDistance = (element.distance.value / 1000).toFixed(2); // Convert meters to km
                            totalDistance += parseFloat(segmentDistance);

                            summaryPanel.append(`
                                <b>Route Segment ${index + 1}:</b><br>
                                Distance: ${segmentDistance} km<br><br>
                            `);
                        });

                        // Display total distance
                        const totalKm = totalDistance.toFixed(2);
                        summaryPanel.append(`<b>Total Distance:</b> ${totalKm} km`);
                    }
                });
            }, 1000); // Wait for geocoding results before calculating distances
        }

        // Add a new route (with dynamic start/end points)
        function addRoute() {
            routeCount++;
            const lastRouteEndPoint = $(`#route${routeCount - 1} .end-point`).val();

            const newRouteDiv = $(`
        <div class="route-container" id="route${routeCount}">
            <b>Start:</b>
            <input type="text" class="start-point" id="start${routeCount}" placeholder="Enter starting location">
            <br />
            <b>End:</b>
            <input type="text" class="end-point" id="end${routeCount}" placeholder="Enter destination">
            <input type="hidden" class="start-lat" id="start-lat${routeCount}">
            <input type="hidden" class="start-lng" id="start-lng${routeCount}">
            <input type="hidden" class="end-lat" id="end-lat${routeCount}">
            <input type="hidden" class="end-lng" id="end-lng${routeCount}">
            <span class="remove-btn" onclick="removeRoute('route${routeCount}')">×</span>
        </div>
    `);

            $("#routes").append(newRouteDiv);

            // Set the start point of this new route as the end point of the previous route
            newRouteDiv.find(".start-point").val(lastRouteEndPoint);

            initAutocomplete(); // Reinitialize autocomplete after adding a new route
        }

        // Remove a specific route
        function removeRoute(routeId) {
            $(`#${routeId}`).remove();
            updateRouteStartPoints();
        }

        // Update the start points of routes after one is removed
        function updateRouteStartPoints() {
            const routeContainers = $(".route-container");
            routeContainers.each((index, routeContainer) => {
                const startInput = $(routeContainer).find(".start-point");
                if (index === 0) {
                    startInput.prop("disabled", false); // First route: allow user to choose start point
                } else {
                    const prevEndPoint = $(routeContainers[index - 1]).find(".end-point");
                    startInput.val(prevEndPoint.val()); // Set start point to the previous route's end point
                    startInput.prop("disabled", true); // Disable to prevent editing
                }
            });
        }

        window.initMap = initMap;
    </script>

</body>

</html>
<?php /**PATH D:\guesttrip\resources\views\website\google_mapOLD3.blade.php ENDPATH**/ ?>