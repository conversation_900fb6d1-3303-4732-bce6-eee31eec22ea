 
 <?php $__env->startPush('css'); ?>
 <?php $__env->stopPush(); ?>
 <?php $__env->startSection('content'); ?>
     
     <section class="hero_section  itinerary_home_page extra_itinerary_class ">
         <div class="section_inner_wrapper">
             <div class="container custom_container">
                 <div class="row">
                     <div class="col-md-6">
                         <div class="hero_content">
                             <?php if(isset($trip->mainHeader)): ?>
                                 <h6><?php echo e($trip->mainHeader->kicker ?? ''); ?></h6>
                                 <h1><?php echo e($trip->mainHeader->heading ?? ''); ?></h1>
                                 <h4><span><?php echo e($trip->mainHeader->sub_heading ?? ''); ?></span></h4>
                                 <h5><?php echo e($trip->mainHeader->description ?? ''); ?></h5>
                             <?php endif; ?>
                         </div>

                     </div>
                     <div class="col-md-6">
                         <div class="multi_images_section">
                             <div class="row custom_row">
                                 <div class="col-md-7">
                                     <div class="custom_images">
                                         <?php if(isset($trip->mainHeaderImages) && isset($trip->mainHeaderImages[0])): ?>
                                             <img src="<?php echo e(asset('website/' . $trip->mainHeaderImages[0]->image)); ?>"
                                                 alt="Image 1">
                                         <?php endif; ?>
                                     </div>
                                 </div>
                                 <div class="col-md-5">
                                     <div class="custom_images">
                                         <?php if(isset($trip->mainHeaderImages) && isset($trip->mainHeaderImages[1])): ?>
                                             <img src="<?php echo e(asset('website/' . $trip->mainHeaderImages[1]->image)); ?>"
                                                 alt="Image 2">
                                         <?php endif; ?>
                                     </div>
                                 </div>
                                 <div class="col-md-12">
                                     <div class="swiper itenarySwiper">
                                         <div class="swiper-wrapper">
                                             <?php if(isset($trip->mainHeaderImages)): ?>
                                                 <?php $__currentLoopData = $trip->mainHeaderImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                     <?php if($index >= 2): ?>
                                                         <div class="swiper-slide">
                                                             <div class="slider_images">
                                                                 <img src="<?php echo e(asset('website/' . $image->image)); ?>"
                                                                     alt="Image <?php echo e($index + 1); ?>">
                                                             </div>
                                                         </div>
                                                     <?php endif; ?>
                                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                             <?php endif; ?>
                                         </div>
                                         <div class="swiper-pagination"></div>
                                     </div>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
     </section>
     <section class="itinerary_summary  itinerary_home_page margin_top_bottom_image">
         <div class="section_inner_wrapper">
             <div class="container custom_container">
                 <div class="row">
                     <div class="col-md-12">
                         <div class="custom_image_card_content">
                             <?php if(isset($trip->tripItinerarySummary)): ?>
                                 <h6><?php echo e($trip->tripItinerarySummary->kicker ?? ''); ?></h6>
                                 <h2><?php echo e($trip->tripItinerarySummary->heading ?? ''); ?></h2>
                                 <h6><?php echo e($trip->tripItinerarySummary->sub_heading ?? ''); ?></h6>
                             <?php endif; ?>
                             <div class="swiper itineryCardSwiper">
                                 <div class="swiper-wrapper">
                                     <?php if(isset($trip->itineraries)): ?>
                                         <?php $__currentLoopData = $trip->itineraries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $itinerary): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                             <div class="swiper-slide">
                                                 <div class="custom_img_cards">
                                                     <div class="custom_card">
                                                         <div class="card_img">
                                                             <?php if(isset($itinerary->images) && isset($itinerary->images[0])): ?>
                                                                 <img src="<?php echo e(asset('website/' . $itinerary->images[0]->image)); ?>"
                                                                     alt="Image 1">
                                                             <?php endif; ?>
                                                         </div>
                                                         <div class="card_content">
                                                             <h3><?php echo e($itinerary->duration ?? ''); ?> Day</h3>
                                                             <h5><?php echo e($itinerary->date ?? ''); ?> <?php echo e($itinerary->time ?? ''); ?></h5>
                                                             <h4><?php echo e($itinerary->start_point ?? ''); ?> -
                                                                 <?php echo e($itinerary->end_point ?? ''); ?></h4>
                                                             <h6><?php echo e($itinerary->description ?? ''); ?></h6>
                                                         </div>
                                                     </div>
                                                 </div>
                                             </div>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                     <?php endif; ?>
                                 </div>
                                 <div class="swiper-button-next"></div>
                                 <div class="swiper-button-prev"></div>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
     </section>

     <section class="google_map_sec  ">
         <div class="section_inner_wrapper">
             <div class="container custom_container">
                 <div class="row custom_row">
                     
                     <div class="col-md-10">
                         <div class="google_map_wrapper">
                             <div style="" class="map_class" id = "map"></div>
                             <div class="google_map_modal" id = "modals"></div>
                         </div>
                     </div>
                     <div class="col-md-2">
                         <div class="dist_time_whea_wrapper">
                             
                             <div class="google_distance_wrapper">
                                 <i class="fa-solid fa-cloud"></i>
                                 <div class="">
                                     <h5>Weather</h5>
                                     <h3 id="weather-info">32 ℃</h3>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
         </div>
     </section>

     <section class="image_fixed_sec" style="">
         <div class="container-fluid">
             <div class="row">
                 <div class="col-md-12">
                     <div class="image_fixed_wrapper" style="">
                         <img src="https://guesttrip.thebackendprojects.com/website/assets/images/section_img.png"
                             style="">
                     </div>
                 </div>
             </div>
         </div>
     </section>
 <?php $__env->stopSection(); ?>
 <?php $__env->startPush('js'); ?>
     <?php echo $__env->make('website.packages_templates.script.iti', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
 <?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master_trip', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\website\packages_templates\VariationThree\itinerary_overview.blade.php ENDPATH**/ ?>