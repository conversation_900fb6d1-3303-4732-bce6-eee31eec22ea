

<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="trip_management">
        <div class="container-fluid">
            <div class="row custom_row">
                <div class="col-md-8">
                    <form>
                        <div class="custom_cards_design">
                            <div class="row custom_row">
                                <div class="col-md-12">
                                    <h1>Crew Details</h1>
                                </div>
                                <div class="col-md-12">
                                    <div class="edit_user_trip custom_justify_between icon_image">
                                        <div class="profile_image">
                                            <!--begin::Image input-->
                                            <div class="image-input image-input-circle" data-kt-image-input="true">
                                                <!--begin::Image preview wrapper-->
                                                <div class="image-input-wrapper">
                                                    <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/buildings.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/buildings.png">
                                                </div>
                                                <!--end::Image preview wrapper-->

                                                <!--begin::Edit button-->
                                                <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                       data-kt-image-input-action="change"
                                                       data-bs-toggle="tooltip"
                                                       data-bs-dismiss="click"
                                                       title="Change avatar">
                                                    <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                    <!--begin::Inputs-->
                                                    <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                    <input type="hidden" name="avatar_remove" />
                                                    <!--end::Inputs-->
                                                </label>
                                                <!--end::Edit button-->

                                                <!--begin::Cancel button-->
                                                <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                      data-kt-image-input-action="cancel"
                                                      data-bs-toggle="tooltip"
                                                      data-bs-dismiss="click"
                                                      title="Cancel avatar">
                                                <i class="ki-outline ki-cross fs-3"></i>
                                            </span>
                                                <!--end::Cancel button-->
                                            </div>
                                        </div>
                                        <div class="profile_changes">
                                            <div class="edit_change custom_flex">
                                                <a href='#!' class="btn btn_dark_green edit_profile">Edit</a>
                                                <a href="#!" class="btn btn_transparent">Deactivate</a>
                                            </div>
                                            <div class="save_cancel_btn custom_flex">
                                                <button type="submit" class="btn btn_dark_green" >Save changes</button>
                                                <button type="button" class="btn btn_transparent cancel_edit">Cancel</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Name:</label>
                                        <input type="text" class="form-control myinput" required id="" value="John Doe" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Role:</label>
                                        <input type="text" class="form-control myinput" required id="" placeholder="Name Here" readonly>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Description:</label>
                                        <textarea rows="1" class="form-control myinput" required readonly>It is a long established fact that a reader will be distracted</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="col-md-4">
                    <div class="user_trip_details">
                        <div class="row custom_row_card custom_row">
                            <div class="col-md-12 custom_column_card">
                                <div class="custom_card">
                                    <h2>Total Trips On Boarded</h2>
                                    <h1 class="f_30">50</h1>
                                </div>
                            </div>
                            <div class="col-md-12 custom_column_card">
                                <div class="custom_card">
                                    <h2>Last on Boarded Trip</h2>
                                    <h1 class="f_30">Vacation</h1>
                                </div>
                            </div>
                            <div class="col-md-12 custom_column_card">
                                <div class="custom_card">
                                    <h2>Joining Date</h2>
                                    <h1 class="f_30">20/10/2024</h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="custom_table custom_cards_design">
                        <h1>On Boarding Summary</h1>
                        <div class="custom_scroll_tbl">
                            <table class="table without_pagination_tbl datatable">
                                <thead>
                                <tr>
                                    <th>SR#</th>
                                    <th>Trip Name</th>
                                    <th>Starting Point</th>
                                    <th>Ending Point</th>
                                    <th>Duration</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php for($i=0;$i<6;$i++): ?>
                                    <tr>
                                        <td>01</td>
                                        <td>Thailand</td>
                                        <td>Thailand</td>
                                        <td>Thailand</td>
                                        <td>23h 3min</td>
                                    </tr>
                                <?php endfor; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\Owner\CrewManagement\crew_view_detail.blade.php ENDPATH**/ ?>