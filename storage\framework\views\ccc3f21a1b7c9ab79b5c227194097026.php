<ul class="nav nav-pills" id="pills-tab" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link <?php echo e(Route::currentRouteName() == 'homes.edit' ? 'active' : ''); ?>" id="pills-home-tab"
            data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home"
            aria-selected="<?php echo e(request()->routeIs('homes.edit', 1) ? 'true' : 'false'); ?>">
            <a href="<?php echo e(route('homes.edit', 1)); ?>"
                class="<?php echo e(request()->routeIs('homes.edit', 1) ? 'text-white' : 'text-dark'); ?>">Home</a></button>
    </li>

    <li class="nav-item" role="presentation">
        <button class="nav-link <?php echo e(Route::currentRouteName() == 'abouts.edit' ? 'active' : ''); ?>" id="pills-company-tab"
            data-bs-toggle="pill" data-bs-target="#pills-company" type="button" role="tab" aria-controls="pills-company"
            aria-selected="<?php echo e(request()->routeIs('abouts.edit', 1) ? 'true' : 'false'); ?>">
            <a href="<?php echo e(route('abouts.edit', 1)); ?>"
                class="<?php echo e(request()->routeIs('abouts.edit', 1) ? 'text-white' : 'text-dark'); ?>">Company</a>
        </button>
    </li>

    <li class="nav-item" role="presentation">
        <button class="nav-link <?php echo e(Route::currentRouteName() == 'pricings.edit' ? 'active' : ''); ?>"
            id="pills-pricing-tab" data-bs-toggle="pill" data-bs-target="#pills-pricing" type="button" role="tab"
            aria-controls="pills-pricing"
            aria-selected="<?php echo e(request()->routeIs('pricings.edit', 1) ? 'true' : 'false'); ?>">
            <a href="<?php echo e(route('pricings.edit', 1)); ?>"
                class="<?php echo e(request()->routeIs('pricings.edit', 1) ? 'text-white' : 'text-dark'); ?>">Pricing</a></button>
    </li>

    <li class="nav-item" role="presentation">
        <button class="nav-link <?php echo e(Route::currentRouteName() == 'ourservices.edit' ? 'active' : ''); ?>"
            id="pills-services-tab" data-bs-toggle="pill" data-bs-target="#pills-services" type="button" role="tab"
            aria-controls="pills-services"
            aria-selected="<?php echo e(request()->routeIs('ourservices.edit', 1) ? 'true' : 'false'); ?>">
            <a href="<?php echo e(route('ourservices.edit', 1)); ?>"
                class="<?php echo e(request()->routeIs('ourservices.edit', 1) ? 'text-white' : 'text-dark'); ?>">Services</a>
        </button>
    </li>

    <li class="nav-item" role="presentation">
        <button class="nav-link <?php echo e(Route::currentRouteName() == 'contacts.edit' ? 'active' : ''); ?>"
            id="pills-contact-tab" data-bs-toggle="pill" data-bs-target="#pills-contact" type="button" role="tab"
            aria-controls="pills-contact"
            aria-selected="<?php echo e(request()->routeIs('contacts.edit', 1) ? 'true' : 'false'); ?>"><a
                href="<?php echo e(route('contacts.edit', 1)); ?>"
                class="<?php echo e(request()->routeIs('contacts.edit', 1) ? 'text-white' : 'text-dark'); ?>">Contact
                Us</a></button>
    </li>
</ul><?php /**PATH D:\guesttrip\resources\views\cms_navbar\cms_navbar.blade.php ENDPATH**/ ?>