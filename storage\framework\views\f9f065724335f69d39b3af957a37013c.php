<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="payment_management">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_table custom_cards_design">
                        <div class="custom_justify_between">
                            <h1>Invoices Management</h1>
                        </div>
                        <table class="table myTable datatable">
                            <thead>
                                <tr>
                                    <th>Sr#</th>
                                    <th>Subscription Type</th>
                                    <th>Amount</th>
                                    <th>Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $subscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($loop->iteration); ?></td>
                                    <td><?php echo e($subscription->package->name ?? ''); ?></td>
                                    <td>$ <?php echo e($subscription->package->amount ?? ''); ?></td>
                                    <td><?php echo e($subscription->created_at->format('d/m/Y')); ?></td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="dropdown-toggle" type="button" id="dropdownMenuButton11"
                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fa-solid fa-ellipsis"></i>
                                            </button>
                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                <li><a href="<?php echo e($subscription->receipt_url ?? ''); ?>" class="dropdown-item download_invoice"><i
                                                            class="fa-solid fa-download"></i>Download Invoice</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\subscriptions\index_owner.blade.php ENDPATH**/ ?>