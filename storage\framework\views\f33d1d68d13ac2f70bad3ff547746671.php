<?php $__env->startPush('css'); ?>
    <link href="assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />

    <style>
        #map {
            height: 400px;
            width: 100%;
        }

        #routes {
            margin-top: 20px;
        }

        .route-container {
            margin-bottom: 10px;
            position: relative;
        }

        .remove-btn {
            color: red;
            cursor: pointer;
            /* font-size: 24px; */
            position: absolute;
            top: 5px;
            right: 10px;
        }

        .remove-btn i {
            font-size: 22px;
            color: red !important;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="content_management">
        <div class="container-fluid">
            <div class="row custom_row">
                <div class="col-md-12">
                    <div class="trip_details">
                        <form id="uploadForm" method="post" action="<?php echo e(route('trips.update', $trip->url_slug)); ?>"
                            enctype="multipart/form-data">
                            <?php echo e(method_field('PATCH')); ?>

                            <?php echo e(csrf_field()); ?>

                            <div class="stepper_form_wrapper">
                                <div class="trips_navigation">
                                    <ul>
                                        <li class="tab active" data-step="1"><button type="button"
                                                class="btn btn_transparent">Select Template</button></li>
                                        <li class="tab" data-step="2"><button type="button"
                                                class="btn btn_transparent">Trip Overview</button></li>
                                        <li class="tab" data-step="3"><button type="button"
                                                class="btn btn_transparent">Itinerary Summary</button></li>
                                        <li class="tab" data-step="4"><button type="button"
                                                class="btn btn_transparent">Crew Details</button></li>
                                        
                                        <?php if(in_array($trip->package_id, [3, 4, 5, 6])): ?>
                                            <li class="tab" data-step="5"><button type="button"
                                                    class="btn btn_transparent">Menu Plan</button></li>
                                            <li class="tab" data-step="6"><button type="button"
                                                    class="btn btn_transparent">Spa Menu</button></li>
                                            <li class="tab" data-step="7"><button type="button"
                                                    class="btn btn_transparent">Activities</button></li>
                                            <li class="tab" data-step="8"><button type="button"
                                                    class="btn btn_transparent">Boat Specification</button></li>
                                            <li class="tab" data-step="9"><button type="button"
                                                    class="btn btn_transparent">Safety On Board</button></li>
                                            
                                            <?php if($trip->package_id == 3 || $trip->package_id == 4): ?>
                                            <li class="tab" data-step="10"><button type="button"
                                                        class="btn btn_transparent">Module Selection</button></li>
                                            <?php endif; ?>
                                            
                                        <?php endif; ?>
                                    </ul>
                                </div>
                                <div id="step-1" class="stepper_step">
                                    <div class="cms_section">
                                        <div class="row custom_row">
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>URL</label>
                                                    <input type="link" class="form-control"
                                                        placeholder="<?php echo e(route('trip.itinerary.overview', [$trip->company_slug, $trip->url_slug])); ?>"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Duration</label>
                                                    <input type="number" class="form-control"
                                                        placeholder="<?php echo e($trip->duration ?? '-'); ?> Days" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="choose_template">
                                                    <h2>Select Template</h2>
                                                    <div class="custom_flex custom_template">
                                                        <?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $template): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="select_template">
                                                                <div class="custom_radio_wrapper custom_recipient_checked">
                                                                    <input class="form-check-input" type="radio"
                                                                        value="<?php echo e($template->id); ?>" name="template_id"
                                                                        id="recipientChecked<?php echo e($template->id); ?>"
                                                                        <?php echo e($template->id == $trip->template_id ? 'checked' : ''); ?>>
                                                                    <label class="select_template_label"
                                                                        for="recipientChecked<?php echo e($template->id); ?>">
                                                                        <?php echo e($template->theme ?? ''); ?></label>
                                                                </div>
                                                                <div class="template_image">
                                                                    <?php if($template->image): ?>
                                                                        <img
                                                                            src="<?php echo e(asset('website') . '/' . $template->image); ?>">
                                                                    <?php else: ?>
                                                                        <img
                                                                            src="<?php echo e(asset('website')); ?>/assets/images/template_one.png">
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="step-2" class="stepper_step">
                                    <div class="cms_section custom_cards_design main_header_section">
                                        <h1>Trip Overview</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Kicker</label>
                                                    <input type="text" name="main_header_kicker"
                                                        class="form-control myinput" placeholder="Example: 6 day trip through Costa Smeralda for VIP client"
                                                        value="<?php echo e(@$trip->mainHeader->kicker ?? ''); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" name="main_header_heading"
                                                        class="form-control myinput"
                                                        placeholder="Example: Discover Sardinia"
                                                        value="<?php echo e(@$trip->mainHeader->heading ?? ''); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="txt_field">
                                                    <label>Sub Heading</label>
                                                    <input type="text" name="main_header_sub_heading"
                                                        class="form-control myinput"
                                                        value="<?php echo e(@$trip->mainHeader->sub_heading ?? ''); ?>"
                                                        placeholder="Example: 19th - 26th August onboard M/Y Infinity">
                                                </div>
                                            </div>
                                            <div class="col-md-6"></div>
                                            <div class="col-md-12">
                                                <div class="txt_field txt_description">
                                                    <label>Description</label>
                                                    <textarea class="form-control myinput descriptionEditor" name="main_header_description" cols="30" rows="10"
                                                        placeholder="Type here"><?php echo e(@$trip->mainHeader->description ?? ''); ?>

                                                    </textarea>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <h3>Images</h3>
                                                <div class="client_upload_img">
                                                    <div class="dropzone dz-clickable" id="main-header-images-div">
                                                        <div class="dz-default dz-message">
                                                            <span class="dz-div">
                                                                <i class="fa-solid fa-image"></i>
                                                                <h6>Upload Images</h6>
                                                                <p>Drag & drop or click to upload</p>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                            
                                        </div>
                                    </div>
                                </div>
                                <div id="step-3" class="stepper_step">
                                    <div class="cms_section custom_cards_design">
                                        <h1>Itinerary Summary</h1>
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        <h1 class="custom_margin">Stop Details</h1>

                                        <div id="routes" class="append_sub_section">
                                            <?php $__currentLoopData = $trip->itineraries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $itinerary): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="sub_sections route-container"
                                                    id="route<?php echo e($loop->iteration); ?>">
                                                    <h2>Sub Section</h2>
                                                    <div class="cms_section custom_cards_design location_section"
                                                        data-location="<?php echo e($itinerary->id); ?>">
                                                        <div class="row custom_row">
                                                            <div class="col-md-6">
                                                                <div class="txt_field">
                                                                    <label>Stop Number</label>
                                                                    <input name="itinerary[<?php echo e($itinerary->id); ?>][number]"
                                                                        value="<?php echo e($itinerary->number ?? ''); ?>"
                                                                        class="form-control myinput" type="number"
                                                                        placeholder="0<?php echo e($loop->iteration); ?>">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="txt_field">
                                                                    <label>Day Number</label>
                                                                    <input
                                                                        name="itinerary[<?php echo e($itinerary->id); ?>][duration]"
                                                                        value="<?php echo e($itinerary->duration ?? ''); ?>"
                                                                        class="form-control myinput" type="number"
                                                                        placeholder="Day">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="txt_field">
                                                                    <label>Date</label>
                                                                    <input name="itinerary[<?php echo e($itinerary->id); ?>][date]"
                                                                        value="<?php echo e($itinerary->date ?? ''); ?>"
                                                                        class="form-control myinput" type="text"
                                                                        placeholder="Monday, 23 Jan 2023">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6"></div>
                                                            <div class="col-md-5">
                                                                <h3>Location</h3>
                                                                <div class="txt_field custom_time">
                                                                    <label>Start Point:</label>
                                                                    <input
                                                                        name="itinerary[<?php echo e($itinerary->id); ?>][start_point]"
                                                                        required
                                                                        value="<?php echo e($itinerary->start_point ?? ''); ?>"
                                                                        class="form-control myinput start-point"
                                                                        data-id="<?php echo e($loop->iteration); ?>"
                                                                        id="start<?php echo e($loop->iteration); ?>" type="text"
                                                                        placeholder="Enter starting location">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-2">
                                                                <div class="custom_check_box">
                                                                    <input name="" class="form-check-input" type="checkbox" value="" id="nightStay<?php echo e($loop->iteration); ?>">
                                                                    <label for="nightStay<?php echo e($loop->iteration); ?>">Night Stay</label>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-5">
                                                                <div class="txt_field custom_time custom_margin">
                                                                    <label>End Point:</label>
                                                                    <input
                                                                        name="itinerary[<?php echo e($itinerary->id); ?>][end_point]"
                                                                        required value="<?php echo e($itinerary->end_point ?? ''); ?>"
                                                                        class="form-control myinput end-point"
                                                                        data-id="<?php echo e($loop->iteration); ?>"
                                                                        id="end<?php echo e($loop->iteration); ?>" type="text"
                                                                        placeholder="Enter destination">
                                                                </div>
                                                            </div>
                                                            <input name="itinerary[<?php echo e($itinerary->id); ?>][start_lat]"
                                                                value="<?php echo e($itinerary->start_lat ?? ''); ?>" type="hidden"
                                                                class="start-lat" id="start-lat<?php echo e($loop->iteration); ?>">
                                                            <input name="itinerary[<?php echo e($itinerary->id); ?>][start_lng]"
                                                                value="<?php echo e($itinerary->start_lng ?? ''); ?>" type="hidden"
                                                                class="start-lng" id="start-lng<?php echo e($loop->iteration); ?>">
                                                            <input name="itinerary[<?php echo e($itinerary->id); ?>][end_lat]"
                                                                value="<?php echo e($itinerary->end_lat ?? ''); ?>" type="hidden"
                                                                class="end-lat" id="end-lat<?php echo e($loop->iteration); ?>">
                                                            <input name="itinerary[<?php echo e($itinerary->id); ?>][end_lng]"
                                                                value="<?php echo e($itinerary->end_lng ?? ''); ?>" type="hidden"
                                                                class="end-lng" id="end-lng<?php echo e($loop->iteration); ?>">
                                                            <div class="col-md-6">
                                                                <div class="txt_field">
                                                                    <label>Time</label>
                                                                    <input name="itinerary[<?php echo e($itinerary->id); ?>][time]"
                                                                        value="<?php echo e($itinerary->time ?? ''); ?>"
                                                                        class="form-control myinput" type="text"
                                                                        placeholder="26nm - 2h 15m - Embark at midday.">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6"></div>
                                                            <div class="col-md-12">
                                                                <div class="txt_field txt_description">
                                                                    <label>Description:</label>
                                                                    <textarea name="itinerary[<?php echo e($itinerary->id); ?>][description]" class="form-control myinput descriptionEditor" rows="10" cols="30"
                                                                        placeholder="Type Here"><?php echo e($itinerary->description ?? ''); ?></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <h3>Images</h3>
                                                                <div class="client_upload_img">
                                                                    <div class="dropzone dz-clickable"
                                                                        id="route-<?php echo e($itinerary->id); ?>-images-div">
                                                                        <div class="dz-default dz-message">
                                                                            <span class="dz-div">
                                                                                <i class="fa-solid fa-image"></i>
                                                                                <h6>Upload Images</h6>
                                                                                <p>Drag & drop or click to upload</p>
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <div class="add_sub_section">
                                                                    <span class="remove-btn"
                                                                        onclick="removeRoute('route<?php echo e($loop->iteration); ?>')"><i
                                                                            class="fa-solid fa-trash-can fa-2xl"></i></span>
                                                                </div>
                                                            </div>
                                                            
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <div class="add_sub_section">
                                            
                                            <button id="addMore" type="button"
                                                class="btn btn_grey append_stop_detail_cards"><i
                                                    class="fa-solid fa-plus"></i>Add Stop</button>
                                        </div>
                                        
                                        <div class="View_map_btn_wrap">
                                            <h1>Itinerary Map</h1>
                                            <button id="showlocationsonmap" type="button" class="btn btn_grey">View
                                                Map</button>
                                        </div>

                                        <div class="row custom_row">
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            <div class="col-md-12">
                                                <div id="map" class="google_map">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="step-4" class="stepper_step">
                                    <div class="cms_section custom_cards_design">
                                        <h1>Crew Details</h1>
                                        <div class="row custom_row">
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            <div class="col-md-12">
                                                <div class="txt_field custom_select">
                                                    <label>Select Crew:</label>
                                                    <div class="onboarded_crew_list">
                                                        <?php $__currentLoopData = $crew; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="custom_check_box">
                                                                <input name="crews[]"
                                                                    class="form-check-input append_checkbox_item"
                                                                    data-profile="<?php echo e($item->image); ?>" type="checkbox"
                                                                    value="<?php echo e($item->id); ?>"
                                                                    <?php if(in_array($item->id, $assignedCrewIds)): ?> checked <?php endif; ?>
                                                                    id="Check<?php echo e($item->id); ?>">
                                                                <label
                                                                    for="Check<?php echo e($item->id); ?>"><?php echo e($item->name ?? ''); ?></label>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="onboarded_crew">
                                                    <h3>On Boarded Crew</h3>
                                                    <div class="append_crew_detail" id="crew-sortable"></div>
                                                </div>
                                            </div>
                                            <input type="hidden" name="ordered_crews" id="ordered_crews"
                                                value="<?php echo e($trip->crews->pluck('id')->join(',')); ?>">

                                        </div>
                                    </div>
                                </div>
                                
                                <?php if(in_array($trip->package_id, [3, 4, 5, 6])): ?>
                                    <div id="step-5" class="stepper_step">
                                        <div class="cms_section custom_cards_design">
                                            <h1>Menu Plan</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Kicker</label>
                                                        <input type="text" name="menu_kicker"
                                                            class="form-control myinput" placeholder=""
                                                            value="<?php echo e(@$trip->menu->kicker ?? ''); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" name="menu_heading"
                                                            class="form-control myinput" placeholder="Daily Menu Plan"
                                                            value="<?php echo e(@$trip->menu->heading ?? ''); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Sub Heading</label>
                                                        <input type="text" name="menu_sub_heading"
                                                            class="form-control myinput"
                                                            value="<?php echo e(@$trip->menu->sub_heading ?? ''); ?>" placeholder="">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="custom_image_toggle custom_cards_design">
                                                <div class="custom_switch_toggle">
                                                    <div class="form-check form-switch">
                                                        <label class="form-check-label" for="menu_plan_type_checkbox">
                                                            Add Images <i class="fa-solid fa-image"></i>
                                                        </label>
                                                        <input type="hidden" name="menu_plan_type" value="image">
                                                        <input class="form-check-input" type="checkbox"
                                                            name="menu_plan_type" id="menu_plan_type_checkbox"
                                                            <?php echo e($trip->menu_plan_type == 'pdf' ? 'checked' : ''); ?>>
                                                        <label class="form-check-label" for="menu_plan_type_checkbox">
                                                            Upload PDF <i class="fa-solid fa-file"></i>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="client_upload_img">
                                                    <h3>Images</h3>
                                                    <div class="dropzone dz-clickable" id="menu-plan-images-div">
                                                        <div class="dz-default dz-message">
                                                            <span class="dz-div">
                                                                <i class="fa-solid fa-image"></i>
                                                                <h6>Upload Images</h6>
                                                                <p>Drag & drop or click to upload</p>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="upload_pdf">
                                                    <label>Upload PDF</label>
                                                    <div class="append_type_file">
                                                        <input type="file" name="menu_plan_pdf" class="file-input"
                                                            accept=".pdf" />
                                                        <button type="button" class="add_image">
                                                            <i class="fa-solid fa-upload"></i>Upload PDF
                                                        </button>
                                                        <button type="button" class="remove_btn"><i
                                                                class="fa-solid fa-close"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                        </div>
                                    </div>
                                    <div id="step-6" class="stepper_step">
                                        <div class="cms_section custom_cards_design">
                                            <h1>Spa Menu</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Kicker</label>
                                                        <input type="text" name="spa_menu_kicker"
                                                            class="form-control myinput" placeholder=""
                                                            value="<?php echo e(@$trip->spa->kicker ?? ''); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" name="spa_menu_heading"
                                                            class="form-control myinput" placeholder="Spa Menu"
                                                            value="<?php echo e(@$trip->spa->heading ?? ''); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Sub Heading</label>
                                                        <input type="text" name="spa_menu_sub_heading"
                                                            class="form-control myinput"
                                                            value="<?php echo e(@$trip->spa->sub_heading ?? ''); ?>" placeholder="">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="custom_image_toggle custom_cards_design">
                                                <div class="custom_switch_toggle">
                                                    <div class="form-check form-switch">
                                                        <label class="form-check-label" for="spa_menu_type">
                                                            Add Images <i class="fa-solid fa-image"></i>
                                                        </label>
                                                        <input type="hidden" name="spa_menu_type" value="image">
                                                        <input class="form-check-input" type="checkbox"
                                                            id="spa_menu_type_checkbox" name="spa_menu_type"
                                                            <?php echo e($trip->spa_menu_type == 'pdf' ? 'checked' : ''); ?>>
                                                        <label class="form-check-label" for="spa_menu_type_checkbox">
                                                            Upload PDF <i class="fa-solid fa-file"></i>
                                                        </label>
                                                    </div>
                                                </div>
                                                
                                                <div class="client_upload_img">
                                                    <h3>Images</h3>
                                                    <div class="dropzone dz-clickable" id="spa-menu-images-div">
                                                        <div class="dz-default dz-message">
                                                            <span class="dz-div">
                                                                <i class="fa-solid fa-image"></i>
                                                                <h6>Upload Images</h6>
                                                                <p>Drag & drop or click to upload</p>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="upload_pdf">
                                                    <label>Upload PDF</label>
                                                    <div class="append_type_file">
                                                        <input type="file"
                                                            class="file-input <?php $__errorArgs = ['spa_menu_pdf'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                            name="spa_menu_pdf" accept=".pdf" />
                                                        <button type="button" class="add_image">
                                                            <i class="fa-solid fa-upload"></i>Upload PDF
                                                        </button>
                                                        <button type="button" class="remove_btn"><i
                                                                class="fa-solid fa-close"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                            
                                        </div>
                                    </div>
                                    <div id="step-7" class="stepper_step">
                                        <div class="cms_section custom_cards_design append_activity_text activity_section">
                                            <h1>Activities</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Kicker</label>
                                                        <input type="text" name="activity_kicker"
                                                            value="<?php echo e(@$trip->activity->kicker ?? ''); ?>"
                                                            class="form-control myinput" placeholder="Type Kicker">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" name="activity_heading"
                                                            value="<?php echo e(@$trip->activity->heading ?? ''); ?>"
                                                            class="form-control myinput" placeholder="Type Heading">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Sub Heading</label>
                                                        <input type="text" name="activity_sub_heading"
                                                            value="<?php echo e(@$trip->activity->sub_heading ?? ''); ?>"
                                                            class="form-control myinput" placeholder="Type Sub Heading">
                                                    </div>
                                                </div>
                                                <div class="col-md-6"></div>
                                                <div class="col-md-12 custom_text">
                                                    <?php if($trip->activity && $trip->activity->texts != null): ?>
                                                        <?php $__currentLoopData = $trip->activity->texts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $text): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="txt_field txt_description">
                                                                <label>Text</label>
                                                                <textarea class="form-control myinput descriptionEditor" name="texts[]" rows="10" placeholder="Type Here"><?php echo e($text ?? ''); ?></textarea>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="add_sub_section add_btn">
                                                        <button type="button" class="btn btn_grey add_text"><i
                                                                class="fa-solid fa-plus"></i></button>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <h3>Images</h3>
                                                    <div class="client_upload_img">
                                                        <div class="dropzone dz-clickable" id="activity-images-div">
                                                            <div class="dz-default dz-message">
                                                                <span class="dz-div">
                                                                    <i class="fa-solid fa-image"></i>
                                                                    <h6>Upload Images</h6>
                                                                    <p>Drag & drop or click to upload</p>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                        </div>
                                    </div>
                                    <div id="step-8" class="stepper_step">
                                        <div class="cms_section custom_cards_design boat_specs_section">
                                            <h1>Boat Specification</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Kicker</label>
                                                        <input type="text" name="boat_specs_kicker"
                                                            class="form-control myinput" placeholder=""
                                                            value="<?php echo e(@$trip->boatSpec->kicker ?? ''); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" name="boat_specs_heading"
                                                            class="form-control myinput" placeholder="Yacht Specification"
                                                            value="<?php echo e(@$trip->boatSpec->heading ?? ''); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Sub Heading</label>
                                                        <input type="text" name="boat_specs_sub_heading"
                                                            class="form-control myinput"
                                                            value="<?php echo e(@$trip->boatSpec->sub_heading ?? ''); ?>"
                                                            placeholder="">
                                                    </div>
                                                </div>
                                                <div class="col-md-6"></div>
                                                <div class="col-md-12">
                                                    <div class="txt_field txt_description">
                                                        <label>Description</label>
                                                        <textarea class="form-control myinput descriptionEditor" placeholder="type here" name="boat_specs_description" rows="10"
                                                            placeholder="Type here"><?php echo e(@$trip->boatSpec->description ?? ''); ?></textarea>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <h3>Images</h3>
                                                    <div class="client_upload_img">
                                                        <div class="dropzone dz-clickable" id="boat-specs-images-div">
                                                            <div class="dz-default dz-message">
                                                                <span class="dz-div">
                                                                    <i class="fa-solid fa-image"></i>
                                                                    <h6>Upload Images</h6>
                                                                    <p>Drag & drop or click to upload</p>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                        </div>
                                    </div>
                                    <div id="step-9" class="stepper_step">
                                        <div class="cms_section custom_cards_design safety_on_board_section">
                                            <h1>Safety Onboard</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Kicker</label>
                                                        <input type="text" name="safety_on_board_kicker"
                                                            class="form-control myinput" placeholder=""
                                                            value="<?php echo e(@$trip->safetyOnBoard->kicker ?? ''); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" name="safety_on_board_heading"
                                                            class="form-control myinput" placeholder="Safety Onboard"
                                                            value="<?php echo e(@$trip->safetyOnBoard->heading ?? ''); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Sub Heading</label>
                                                        <input type="text" name="safety_on_board_sub_heading"
                                                            class="form-control myinput"
                                                            value="<?php echo e(@$trip->safetyOnBoard->sub_heading ?? ''); ?>"
                                                            placeholder="">
                                                    </div>
                                                </div>
                                                <div class="col-md-6"></div>
                                                <div class="col-md-12">
                                                    <div class="txt_field txt_description">
                                                        <label>Description</label>
                                                        <textarea class="form-control myinput descriptionEditor" placeholder="Type here" name="safety_on_board_description" rows="10"
                                                            ><?php echo e(@$trip->safetyOnBoard->description ?? ''); ?></textarea>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <h3>Images</h3>
                                                    <div class="client_upload_img">
                                                        <div class="dropzone dz-clickable" id="safety-onboard-images-div">
                                                            <div class="dz-default dz-message">
                                                                <span class="dz-div">
                                                                    <i class="fa-solid fa-image"></i>
                                                                    <h6>Upload Images</h6>
                                                                    <p>Drag & drop or click to upload</p>
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <?php if($trip->package_id == 3 || $trip->package_id == 4): ?>
                                        <div id="step-10" class="stepper_step">
                                            <div class="choose_section custom_cards_design">
                                                <h1>Module Selection</h1>
                                                <div class="custom_flex custom_section">
                                                    <?php $__currentLoopData = $chooseSections; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="select_section">
                                                            <div class="custom_checkbox_wrapper">
                                                                <input class="form-check-input section-checkbox"
                                                                    type="checkbox" value="<?php echo e($section->id); ?>"
                                                                    name="section_id[]"
                                                                    id="sectionChecked<?php echo e($section->id); ?>"
                                                                    <?php echo e(in_array($section->id, $assignedSectionIds ?? []) ? 'checked' : ''); ?>>
                                                                <label
                                                                    for="sectionChecked<?php echo e($section->id); ?>"><?php echo e($section->name ?? ''); ?></label>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                <?php endif; ?>
                                
                                <div class="upload_next_previous_wrapper">
                                    <div class="upload_btn">
                                        <button type="button" id="submitBtn" class="btn btn_dark_green">Upload</button>
                                        <button type="button" class="btn btn_grey" id="previewBtn">Preview</button>
                                        <a href="<?php echo e(route('trips.index')); ?>" class="btn btn_transparent">Cancel</a>
                                    </div>
                                    <div class="next_previus_wrapper">
                                        <button type="button"
                                            class="btn btn_grey stepper_form_previous">Previous</button>
                                        <button type="button" class="btn btn_dark_green stepper_form_next">Next</button>
                                    </div>
                                </div>

                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.0/Sortable.min.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAbi9IUF4TBu58oC9iGZexb045rMaQr2AQ&libraries=places&callback=initMap"
        async defer></script>
    <script>
        $(document).ready(function() {
            // Ck Editor
            document.querySelectorAll('.descriptionEditor').forEach(editorElement => {
                ClassicEditor
                    .create(editorElement)
                    .catch(error => {
                        console.error(error);
                    });
            });

            $("#previewBtn").click(function(e) {
                e.preventDefault(); // Prevent default action
                if (validateLocations()) { // Only submit if locations are valid
                    showLoader();
                    handlePreview();
                }
            });

            $("#submitBtn").click(function(e) {
                e.preventDefault(); // Prevent default action
                if (validateLocations()) { // Only submit if locations are valid
                    showLoader();
                    handleSubmit();
                }
            });

            function handlePreview() {
                let formData = new FormData($("#uploadForm")[0]); // Get form data (includes images)
                let preview_url =
                    "<?php echo e(route('trip.itinerary.overview', [$trip->company_slug, $trip->url_slug])); ?>";

                $.ajax({
                    url: $("#uploadForm").attr("action"),
                    type: "POST",
                    data: formData,
                    processData: false, // Don't process the files
                    contentType: false, // Set content type to false for form-data
                    success: function(response) {
                        if (response.itiImagesArray) {
                            changingDz(response.itiImagesArray);
                        }
                        if (response.isMenuPlanPdf) {
                            $('input[name="menu_plan_pdf"]').val('');
                        }
                        if (response.isSpaMenuPdf) {
                            $('input[name="spa_menu_pdf"]').val('');
                        }
                        window.open(preview_url, "_blank");
                    },
                    error: handleAjaxError,
                    complete: hideLoader,
                });
            }

            function handleSubmit() {
                let formData = new FormData($("#uploadForm")[0]); // Get form data (includes images)

                $.ajax({
                    url: $("#uploadForm").attr("action"),
                    type: "POST",
                    data: formData,
                    processData: false, // Don't process the files
                    contentType: false, // Set content type to false for form-data
                    success: function(response) {
                        if (response.itiImagesArray) {
                            changingDz(response.itiImagesArray);
                        }
                        if (response.isMenuPlanPdf) {
                            $('input[name="menu_plan_pdf"]').val('');
                        }
                        if (response.isSpaMenuPdf) {
                            $('input[name="spa_menu_pdf"]').val('');
                        }
                        Swal.fire({
                            icon: "success",
                            title: "Success!",
                            text: response.message,
                        });
                    },
                    error: handleAjaxError,
                    complete: hideLoader,
                });
            }

            function changingDz(arrayOfImagesIti) {
                Object.keys(arrayOfImagesIti).forEach(itineraryKey => {
                    let itinerary = arrayOfImagesIti[itineraryKey];
                    // Find the parent container before removing
                    let parentContainer = $(`#route-${itineraryKey}-images-div`).closest(
                        ".client_upload_img").parent();
                    // Remove the old div (entire .client_upload_img block)
                    $(`#route-${itineraryKey}-images-div`).closest(".client_upload_img").remove();
                    // Create a new Dropzone div
                    let newDiv = `
                        <div class="client_upload_img">
                            <div id="route-${itinerary.id}-images-div" class="dropzone">
                                <div class="dz-default dz-message">
                                    <span class="dz-div">
                                        <i class="fa-solid fa-image"></i>
                                        <h6>Upload Images</h6>
                                        <p>Drag & drop or click to upload</p>
                                    </span>
                                </div>
                            </div>
                        </div>
                    `;
                    parentContainer.append(newDiv);

                    $("*[class*='" + itineraryKey +
                        "'], *[data-location='" + itineraryKey +
                        "'], *[name*='" + itineraryKey + "']").each(function() {
                        let oldId = $(this).attr("id");
                        let oldClass = $(this).attr("class");
                        let oldData = $(this).attr("data-location");
                        let oldName = $(this).attr("name");

                        if (oldId) {
                            let newId = oldId.replace(itineraryKey,
                                itinerary.id);
                            $(this).attr("id", newId);
                        }

                        if (oldClass) {
                            let newClass = oldClass.replace(itineraryKey,
                                itinerary.id);
                            $(this).attr("class", newClass);
                        }

                        if (oldData) {
                            $(this).attr("data-location", itinerary.id);
                        }

                        if (oldName) {
                            let newName = oldName.replace(itineraryKey,
                                itinerary.id);
                            $(this).attr("name", newName);
                        }
                    });

                    let selector =
                        `#route-${itinerary.id}-images-div`; // Dynamic selector for each itinerary
                    let category = `route${itinerary.id}Images`;

                    if (!imageFiles[category]) {
                        imageFiles[category] = [];
                    }
                    // Merge new images and avoid duplicates
                    itinerary.images.forEach(newImage => {
                        if (!imageFiles[category].some(existing => existing
                                .id === newImage.id)) {
                            imageFiles[category].push({
                                id: newImage.id,
                                path: newImage.image,
                                size: newImage.size // Default size
                            });
                        }
                    });

                    // Initialize Dropzone with updated records
                    let uploadUrl =
                        `<?php echo e(route('iti.image.add', ['__ITINERARY_ID__', '__ITINERARY_IMAGES__'])); ?>`
                        .replace('__ITINERARY_ID__', itinerary.id).replace('__ITINERARY_IMAGES__',
                            `route${itinerary.id}Images`);
                    let deleteUrl =
                        `<?php echo e(route('iti.image.delete', ['__ITINERARY_ID__', '__ITINERARY_IMAGES__'])); ?>`
                        .replace('__ITINERARY_ID__', itinerary.id).replace('__ITINERARY_IMAGES__',
                            `route${itinerary.id}Images`);

                    initializeDropzone(selector, uploadUrl, deleteUrl, category);
                });
            }

            function handleAjaxError(xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    let errorMessage = "";

                    // Loop through errors and concatenate messages
                    $.each(errors, function(key, value) {
                        errorMessage += value[0] + "\n";
                    });

                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: errorMessage,
                    });
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Oops...",
                        text: "Something went wrong! Please try again.",
                    });
                }
            }

            function showLoader() {
                document.getElementById("loading").style.visibility = "visible";
            }

            function hideLoader() {
                document.getElementById("loading").style.visibility = "hidden";
            }
            var allInputFields = $(".myinput");
            allInputFields.each(function() {
                $(this).removeAttr("");
            });
            var count = 1;
            // trip detail section 2 append sub section
            $(".cms_section .add_sub_section .append_itenary_cards").click(function() {
                count++;
                $(this).closest(".cms_section").find(".append_sub_section").append(
                    '<div class="sub_sections"><h2>Sub Section 0' + count +
                    '</h2><div class="cms_section custom_cards_design"><div class="row custom_row"><div class="col-md-6"> <div class="txt_field"><label>Stop Number</label><input class="form-control myinput" type="number" value="01" ></div></div>' +
                    '<div class="col-md-6"><div class="txt_field"> <label>Day Number</label> <input class="form-control myinput" type="number" value="Day" > </div> </div>' +
                    '<div class="col-md-5"><h3>Location</h3><div class="txt_field custom_time"><label>Start Point:</label><input class="form-control myinput" type="text" value="Phuket" ></div></div>' +
                    '<div class="col-md-2"><div class="custom_check_box"> <input name="" class="form-check-input" type="checkbox" value="" id="nightStay' + count + '" > <label for="nightStay' + count + '">Night Stay</label> </div> </div>' +
                    '<div class="col-md-5"><div class="txt_field custom_time custom_margin"><label>End Point:</label><input class="form-control myinput" type="text" value="Phang Nga Bay" > </div></div>' +
                    '<div class="col-md-6"><div class="txt_field"><label>Time</label><input class="form-control myinput" type="text" value="26nm - 2h 15m - Embark at midday." ></div></div>' +
                    '<div class="col-md-6"></div><div class="col-md-12"><div class="txt_field txt_description"><label>Description:</label><textarea class="form-control myinput" rows="4" placeholder="Type here" ></textarea></div></div></div></div></div>'
                );
            });

            // When checkboxes are changed, update the selected crew list
            updateCrewDetails();
            $('input.append_checkbox_item').change(function() {
                updateCrewDetails();
            });

            function updateCrewDetails() {
                const selectedCrews = [];
                const $crewSortable = $('#crew-sortable');
                // Loop through each checked checkbox and get the crew's details
                $('input.append_checkbox_item:checked').each(function() {
                    const crewId = $(this).val();
                    const crewName = $(this).closest('.custom_check_box').find('label').html();
                    const imageUrl = $(this).data('profile') ?
                        "<?php echo e(asset('website')); ?>/" + $(this).data('profile') :
                        "<?php echo e(asset('website')); ?>/assets/images/crew_image.png";

                    selectedCrews.push({
                        id: crewId,
                        name: crewName,
                        image: imageUrl,
                    });
                });

                // Preserve initial sequence order using the value from the hidden input
                const initialOrder = $('#ordered_crews').val().split(',');
                selectedCrews.sort((a, b) => initialOrder.indexOf(a.id) - initialOrder.indexOf(b.id));

                // Clear the current list and repopulate with selected crews
                $crewSortable.empty();
                selectedCrews.forEach((crew) => {
                    $crewSortable.append(`
                        <div class="crew_listing custom_flex" data-id="${crew.id}">
                            <div class="crew_image">
                                <img src="${crew.image}" alt="Crew Image">
                            </div>
                            <h4>${crew.name}</h4>
                        </div>
                    `);
                });

                // Update the hidden input with the new order of crew IDs
                updateCrewOrder();
            }

            // Initialize SortableJS
            new Sortable(document.getElementById('crew-sortable'), {
                animation: 150,
                onEnd: function() {
                    updateCrewOrder(); // Update the order whenever items are dragged and dropped
                }
            });

            // Update the hidden input with the order of crew IDs
            function updateCrewOrder() {
                const crewOrder = [];
                $('#crew-sortable .crew_listing').each(function() {
                    crewOrder.push($(this).data('id'));
                });
                $('#ordered_crews').val(crewOrder.join(',')); // Store as a comma-separated string
                console.log('Updated Crew Order:', crewOrder); // Debugging
            }


            //        trip detail section 5 append breakfast sub section
            $(document).on("click", ".cms_section .add_sub_section .add_breakfast", function() {
                // currentnewMenu = $(this).closest(".append_sub_section").find(".sub_sections").length;
                currentnewMenu = $(this).closest(".sub_sections").data("menu");
                console.log("current new mwnu: " + currentnewMenu);

                $(this).closest(".cms_section").find(".append_menu").each(function() {
                    var subSectionCount = "new_" + ($(this).find(".append_items").length + 1);
                    console.log("append_items count: " + subSectionCount);
                    $(this).closest(".cms_section").find(".append_menu").append(
                        '<div class="append_items appended_section"><div class="add_image"><h2>Image</h2><div class="profile_picture"><div class="profile_image"> <div class="image-input image-input-outline" data-kt-image-input="true"> <div class="image-input-wrapper"> <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"> </div>' +
                        '<label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="change"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>' +
                        '<input type="file" name="menu_plans[' + currentnewMenu +
                        '][details][' + subSectionCount +
                        '][image]" accept=".png, .jpg, .jpeg" class="custom_file_input"/>' +
                        '<input type="hidden" name="avatar_remove" /></label>' +
                        '<span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="cancel"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Cancel avatar"> <i class="ki-outline ki-cross fs-3"></i> </span> </div> </div> </div> </div>' +
                        '<div class="txt_field"><label>Meal Name</label><input class="form-control myinput" type="text"  name="menu_plans[' +
                        currentnewMenu + '][details][' + subSectionCount +
                        '][title]" placeholder="Day" > </div> ' +
                        '<div class="txt_field txt_description"><label>Meal Description</label><textarea class="form-control myinput"  name="menu_plans[' +
                        currentnewMenu + '][details][' + subSectionCount +
                        '][text]" rows="3" placeholder="Type Here" ></textarea></div></div>'
                    );
                });

            });

            //        trip detail section 5 append breakfast name sub section
            $(".cms_section .add_sub_section .append_breakfast_name").click(function() {
                $(this).closest(".cms_section").find(".append_sub_section").each(function() {
                    var newMenu = "new_" + ($(this).find(".sub_sections").length + 1);

                    // console.log("Sub-sections count: " + newMenu);
                    $(this).closest(".cms_section").find(".append_sub_section").append(
                        '<div class="sub_sections appended_section" data-menu="' + newMenu +
                        '"><h2>Sub Section</h2><div class="cms_section custom_cards_design" ><div class="row custom_row">' +
                        '<div class="col-md-12"><div class="txt_field"> <label>Name</label> <input class="form-control myinput" type="text"  name="menu_plans[' +
                        newMenu + '][name]" palceholder="Break Fast" > </div> </div>' +
                        '<div class="col-md-12"><div class="add_breakfast_section"><div class="append_menu"><div class="append_items"> <div class="add_image"> <h2>Image</h2> <div class="profile_picture"> <div class="profile_image">' +
                        '<div class="image-input image-input-outline" data-kt-image-input="true"><div class="image-input-wrapper"> <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"> </div>' +
                        '<label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="change"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>' +
                        '<input type="file" name="menu_plans[' + newMenu +
                        '][details][new_1][image]" accept=".png, .jpg, .jpeg" class="custom_file_input"/>' +
                        '<input type="hidden" name="avatar_remove" />' +
                        '</label>' +
                        '<span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="cancel"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Cancel avatar"> <i class="ki-outline ki-cross fs-3"></i> </span> </div> </div> </div> </div>' +
                        '<div class="txt_field"><label>Meal Name</label><input class="form-control myinput" type="text"  name="menu_plans[' +
                        newMenu +
                        '][details][new_1][title]" placeholder="Day" ></div><div class="txt_field txt_description"><label>Meal Description</label><textarea class="form-control myinput"  name="menu_plans[' +
                        newMenu +
                        '][details][new_1][text]" rows="3" placeholder="Type here" ></textarea></div></div>' +
                        '</div></div></div>' +
                        '<div class="col-md-12"><div class="add_sub_section add_btn"><button type="button" class="btn btn_grey add_breakfast"><i class="fa-solid fa-plus"></i></button></div></div></div></div></div>'
                    );
                });
            });

            // Append Spa Menu
            $(".cms_section .add_sub_section .append_spa_menu").click(function() {
                var spaMenuLength = $(this).closest('.cms_section').find(
                        ".append_sub_section .sub_sections")
                    .length + 1;
                var newSpaMenu = "new_" + spaMenuLength;
                // console.log(newSpaMenu);
                $(this).closest(".cms_section").find(".append_sub_section").append(
                    '<div class="sub_sections appended_section"><h2>Sub Section</h2><div class="cms_section custom_cards_design"><div class="row custom_row">' +
                    '<div class="col-md-12"><div class="add_breakfast_section"> <div class="append_items"> <div class="add_image"> <h2>Image</h2> <div class="profile_picture"> <div class="profile_image">' +
                    '<div class="image-input image-input-outline" data-kt-image-input="true"><div class="image-input-wrapper"> <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"> </div>' +
                    '<label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="change"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>' +
                    '<input type="file" name="spa_menu_details[' + newSpaMenu +
                    '][image]" accept=".png, .jpg, .jpeg" class="custom_file_input"/>' +
                    '<input type="hidden" name="avatar_remove" />' +
                    '</label>' +
                    '<span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="cancel"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Cancel avatar"> <i class="ki-outline ki-cross fs-3"></i> </span> </div> </div> </div> </div>' +
                    '<div class="txt_field"><label>Title</label><input class="form-control myinput" type="text" name="spa_menu_details[' +
                    newSpaMenu +
                    '][title]" placeholder="John Doe"></div><div class="txt_field"><label>Sub - Title</label><input class="form-control myinput" type="text" name="spa_menu_details[' +
                    newSpaMenu +
                    '][sub_title]" placeholder="Ship Manager Of Operations"></div><div class="txt_field txt_description"><label>Description</label><textarea name="spa_menu_details[' +
                    newSpaMenu +
                    '][description]" class="form-control myinput" rows="3" placeholder="Type Here"></textarea></div></div>' +
                    '<div class="append_menu"></div></div></div>' +
                    '</div></div></div>')
            });

            // trip detail section 6 append activities section
            $(".append_activity_text .add_sub_section .add_text").click(function() {
                $(this).closest(".append_activity_text").find(".custom_text").append(
                    '<div class="txt_field txt_description"><label>Text</label><textarea name="texts[]" class="form-control myinput activities_des_editor" rows="10" cols="30" placeholder="Type Here"></textarea></div>'
                );
                const newEditor = $('.activities_des_editor').last()[0];
                ClassicEditor
                    .create(newEditor)
                    .catch(error => {
                        console.error(error);
                    });
            });

            // Add More Images Section jquery

            $(document).on("click", ".add_sub_section .append_images", function() {
                var section = $(this).closest(".cms_section, .append_sub_section .sub_sections");
                var profilePictureCount = section.find(".profile_picture").length + 1;
                // console.log(profilePictureCount);
                // Conditional logic to change the name attribute for different sections
                if (section.hasClass('main_header_section')) {
                    inputFileName = "main_header_images";
                } else if (section.hasClass('safety_on_board_section')) {
                    inputFileName = "safety_on_board_images";
                } else if (section.hasClass('boat_specs_section')) {
                    inputFileName = "boat_specs_images";
                } else if (section.hasClass('activity_section')) {
                    inputFileName = "activity_images";
                } else if (section.hasClass('location_section')) {
                    currentlocations = section.data("location");
                    // console.log("current location: " + currentlocations);
                    inputFileName = 'itinerary[' + currentlocations + '][images]'
                } else {
                    inputFileName = "image"
                }
                var inputFileNameWithCount = inputFileName + '[new_' + profilePictureCount + ']';
                section.find(
                    ".append_sub_images").append(
                    '<div class="appended_section profile_picture"><div class="profile_image"><div class="image-input image-input-outline" data-kt-image-input="true"><div class="image-input-wrapper"><img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"></div>' +
                    '<label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i><input type="file" name="' +
                    inputFileNameWithCount +
                    '"  accept=".png, .jpg, .jpeg" class="custom_file_input"/>' +
                    '<input type="hidden" name="avatar_remove" /></label><span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Cancel avatar"><i class="ki-outline ki-cross fs-3"></i></span></div></div></div>'
                );
            });

            //         // Re-initialize the input[type="file"] functionality for newly appended sections
            $(document).on('change', '.appended_section input[type="file"]', function() {
                var reader = new FileReader();
                var $imageInputWrapper = $(this).closest('.appended_section .image-input').find(
                    '.image-input-wrapper');

                reader.onload = function(e) {
                    $imageInputWrapper.html('<img class="custom_img" src="' + e.target.result +
                        '" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"/>'
                    );
                }

                // Load the selected image into the preview
                reader.readAsDataURL(this.files[0]);
            });

            // Add event listener to remove the image when clicking the 'cancel' button
            $(document).on('click', '.appended_section [data-kt-image-input-action="cancel"]', function() {
                var newImg = $(this).closest('.appended_section .image-input').find(
                    '.image-input-wrapper img.custom_img');
                var originalSrc = newImg.attr('data-original-src');
                newImg.attr('src', originalSrc);
            });

            function toggleFields() {
                $(".custom_switch_toggle .form-switch input[type=checkbox]").each(function() {
                    if ($(this).is(":checked")) {
                        $(this).val("pdf");
                        $(this).closest('.custom_image_toggle').find('.client_upload_img').hide();
                        $(this).closest('.custom_image_toggle').find('.upload_pdf').show();
                    } else {
                        $(this).val("image");
                        $(this).closest('.custom_image_toggle').find('.upload_pdf').hide();
                        $(this).closest('.custom_image_toggle').find('.client_upload_img').show();
                    }
                });
            }

            // Run on page load
            toggleFields();

            // Run on change
            $(document).on("change", ".custom_switch_toggle .form-switch input[type=checkbox]", function() {
                toggleFields();
            });

            $(document).on("change", '.upload_pdf .file-input', function() {
                const file = $(this)[0].files[0];

                if (file.type === 'application/pdf') {
                    const reader = new FileReader();
                    const pdfURL = URL.createObjectURL(file);
                    const $parentDiv = $(this).closest(".upload_pdf .append_type_file");
                    $parentDiv.find(".image_structure").remove();
                    $parentDiv.append('<div class="image_structure">' +
                        '<div class="pdf_img"><img src="<?php echo e(asset('website')); ?>/assets/images/pdf_img.png" class="pdf_control"></div>' +
                        '<a href="' + pdfURL +
                        '" target="_blank" class="preview_pdf">View PDF</a></div>');
                } else {
                    $(this).val('');
                    alert('Only PDF File Are Allowed.');
                }
            });
            $(document).on("click", ".remove_btn", function() {
                $(".image_structure").remove();
            });
        })
    </script>
    
    <script>
        let routeCount = $(".route-container").length;
        let markers = []; // To store markers on the map

        function initMap() {
            const map = new google.maps.Map($("#map")[0], {
                zoom: 6,
                center: {
                    lat: 41.85,
                    lng: -87.65
                }, // Default center
            });

            // Initialize autocomplete for start and end fields
            initAutocomplete(map);

            // Event listener for the Submit button
            $("#showlocationsonmap").on("click", () => {
                displayPinsOnMap(map);
            });

            // Event listener for the Add More button
            $("#addMore").on("click", () => addRoute(map));

            $(document).on('change', '.end-point', function() {
                displayPinsOnMap(map);
            });
        }

        function setStartLatLng(input, lat, lng) {
            const idNumber = $(input).data("id");
            $(`.start-lat${idNumber}`).val(lat);
            $(`.start-lng${idNumber}`).val(lng);
        }

        function setEndLatLng(input, lat, lng) {
            const idNumber = $(input).data("id");
            $(`.end-lat${idNumber}`).val(lat);
            $(`.end-lng${idNumber}`).val(lng);
        }

        function initAutocomplete() {
            // Apply Autocomplete to all inputs with start-point or end-point classes
            document.querySelectorAll(".start-point, .end-point").forEach(input => {
                const autocomplete = new google.maps.places.Autocomplete(input);

                // Add event listener for place selection
                autocomplete.addListener("place_changed", () => {
                    const place = autocomplete.getPlace();
                    if (!place.geometry) {
                        console.error("Please select a valid location from suggestions.");
                        input.value = "";
                        return;
                    }

                    // Extract latitude and longitude
                    const lat = place.geometry.location.lat();
                    const lng = place.geometry.location.lng();

                    // Get the corresponding hidden inputs for this input
                    const parentContainer = input.closest(".route-container");
                    if (input.classList.contains("start-point")) {
                        parentContainer.querySelector(".start-lat").value = lat;
                        parentContainer.querySelector(".start-lng").value = lng;
                    } else if (input.classList.contains("end-point")) {
                        parentContainer.querySelector(".end-lat").value = lat;
                        parentContainer.querySelector(".end-lng").value = lng;
                    }
                });

                // If user types manually or clears the field, reset lat/lng
                input.addEventListener("input", () => {
                    const parentContainer = input.closest(".route-container");

                    if (input.classList.contains("start-point")) {
                        parentContainer.querySelector(".start-lat").value = "";
                        parentContainer.querySelector(".start-lng").value = "";
                    } else {
                        parentContainer.querySelector(".end-lat").value = "";
                        parentContainer.querySelector(".end-lng").value = "";
                    }

                    input.dataset.selected = "false"; // Mark as not selected
                });
            });
        }

        // Validate before form submission
        function validateLocations() {
            let isValid = true;

            $(".route-container").each(function(index) {
                let routeNumber = index + 1;
                let startLat = $(this).find(".start-lat").val();
                let startLng = $(this).find(".start-lng").val();
                let endLat = $(this).find(".end-lat").val();
                let endLng = $(this).find(".end-lng").val();

                if (!startLat || !startLng) {
                    Swal.fire({
                        icon: "error",
                        title: "Validation Error",
                        text: `Please select a valid Start Point for Stop Details Section ${routeNumber} from Google Maps suggestions.`,
                    });

                    isValid = false;
                    return false; // Exit loop early
                }

                if (!endLat || !endLng) {
                    Swal.fire({
                        icon: "error",
                        title: "Validation Error",
                        text: `Please select a valid End Point for Stop Details Section ${routeNumber} from Google Maps suggestions.`,
                    });

                    isValid = false;
                    return false; // Exit loop early
                }
            });

            return isValid;
        }


        function addRoute(map) {
            routeCount++;
            const lastRoute = $('.route-container:last');

            const newRouteDiv = $(`
            <div class="sub_sections route-container" id="route${routeCount}_new">
                <h2>Sub Section</h2>
                <div class="cms_section custom_cards_design location_section" data-location="${routeCount}_new">
                    <div class="row custom_row">
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Number</label>
                                <input name="itinerary[${routeCount}_new][number]" class="form-control myinput" type="number" value="${routeCount}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Duration</label>
                                <input name="itinerary[${routeCount}_new][duration]" class="form-control myinput" type="number" placeholder="Day">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Date</label>
                                <input name="itinerary[${routeCount}_new][date]" class="form-control myinput" type="text" placeholder="Monday, 23 Jan 2023">
                            </div>
                        </div>
                        <div class="col-md-6"></div>
                        <div class="col-md-5">
                            <h3>Location</h3>
                            <div class="txt_field custom_time">
                                <label>Start Point:</label>
                                <input name="itinerary[${routeCount}_new][start_point]" required class="form-control myinput start-point" id="start${routeCount}_new" data-id="${routeCount}_new" type="text" placeholder="Enter starting location">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="custom_check_box">
                                <input name="" class="form-check-input" type="checkbox" value="" id="nightStay${routeCount}">
                                <label for="nightStay${routeCount}">Night Stay</label>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="txt_field custom_time custom_margin">
                                <label>End Point:</label>
                                <input name="itinerary[${routeCount}_new][end_point]" required class="form-control myinput end-point" id="end${routeCount}_new" data-id="${routeCount}_new" type="text" placeholder="Enter destination">
                            </div>
                        </div>
                        <input name="itinerary[${routeCount}_new][start_lat]" type="hidden" class="start-lat" id="start-lat${routeCount}_new">
                        <input name="itinerary[${routeCount}_new][start_lng]" type="hidden" class="start-lng" id="start-lng${routeCount}_new">
                        <input name="itinerary[${routeCount}_new][end_lat]" type="hidden" class="end-lat" id="end-lat${routeCount}_new">
                        <input name="itinerary[${routeCount}_new][end_lng]" type="hidden" class="end-lng" id="end-lng${routeCount}_new">
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Time</label>
                                <input name="itinerary[${routeCount}_new][time]" class="form-control myinput" type="text" placeholder="26nm - 2h 15m - Embark at midday.">
                            </div>
                        </div>
                        <div class="col-md-6"></div>
                        <div class="col-md-12">
                            <div class="txt_field txt_description">
                                <label>Description:</label>
                                <textarea name="itinerary[${routeCount}_new][description]" class="form-control myinput itinerary_des_editor" rows="10" cols="30" placeholder="Type here"></textarea>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <h3>Images</h3>
                            <div class="client_upload_img">
                                <div class="dropzone dz-clickable" id="route-${routeCount}_new-images-div">
                                    <div class="dz-default dz-message">
                                        <button class="dz-button" type="button">
                                            <i class="fa-solid fa-image"></i>
                                            <h6>Upload Images</h6>
                                            <p>Drag & drop or click to upload</p>
                                        </button>
                                    </div>
                                </div>
                                <input type="file" name="itinerary[${routeCount}_new][images][]" id="route${routeCount}_images_input" multiple
                                    style="display: none;">
                            </div>

                        </div>
                        <div class="col-md-12">
                            <div class="add_sub_section">
                                <span class="remove-btn" onclick="removeRoute('route${routeCount}_new')"><i class="fa-solid fa-trash-can fa-2xl"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>`);
            const newRouteDivOLD = $(`
            <div class="sub_sections route-container" id="route${routeCount}_new">
                <h2>Sub Section</h2>
                <div class="cms_section custom_cards_design location_section" data-location="${routeCount}_new">
                    <div class="row custom_row">
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Number</label>
                                <input name="itinerary[${routeCount}_new][number]" class="form-control myinput" type="number" value="${routeCount}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Duration</label>
                                <input name="itinerary[${routeCount}_new][duration]" class="form-control myinput" type="number" placeholder="Day">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Date</label>
                                <input name="itinerary[${routeCount}_new][date]" class="form-control myinput" type="text" placeholder="Monday, 23 Jan 2023">
                            </div>
                        </div>
                        <div class="col-md-6"></div>
                        <div class="col-md-5">
                            <h3>Location</h3>
                            <div class="txt_field custom_time">
                                <label>Start Point:</label>
                                <input name="itinerary[${routeCount}_new][start_point]" required class="form-control myinput start-point" id="start${routeCount}_new" data-id="${routeCount}_new" type="text" placeholder="Enter starting location">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="custom_check_box">
                                <input name="" class="form-check-input" type="checkbox" value="" id="nightStay${routeCount}">
                                <label for="nightStay${routeCount}">Night Stay</label>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="txt_field custom_time custom_margin">
                                <label>End Point:</label>
                                <input name="itinerary[${routeCount}_new][end_point]" required class="form-control myinput end-point" id="end${routeCount}_new" data-id="${routeCount}_new" type="text" placeholder="Enter destination">
                            </div>
                        </div>
                        <input name="itinerary[${routeCount}_new][start_lat]" type="hidden" class="start-lat" id="start-lat${routeCount}_new">
                        <input name="itinerary[${routeCount}_new][start_lng]" type="hidden" class="start-lng" id="start-lng${routeCount}_new">
                        <input name="itinerary[${routeCount}_new][end_lat]" type="hidden" class="end-lat" id="end-lat${routeCount}_new">
                        <input name="itinerary[${routeCount}_new][end_lng]" type="hidden" class="end-lng" id="end-lng${routeCount}_new">
                        <div class="col-md-6">
                            <div class="txt_field">
                                <label>Time</label>
                                <input name="itinerary[${routeCount}_new][time]" class="form-control myinput" type="text" placeholder="26nm - 2h 15m - Embark at midday.">
                            </div>
                        </div>
                        <div class="col-md-6"></div>
                        <div class="col-md-12">
                            <div class="txt_field txt_description">
                                <label>Description:</label>
                                <textarea name="itinerary[${routeCount}_new][description]" class="form-control myinput itinerary_des_editor" rows="10" cols="30" placeholder="Type here"></textarea>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <h3>Image</h3>
                            <div class="custom_profile_upload custom_flex append_sub_images">
                                <div class="appended_section profile_picture"><div class="profile_image"><div class="image-input image-input-outline" data-kt-image-input="true"><div class="image-input-wrapper"><img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"></div><label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i><input type="file" name="itinerary[${routeCount}_new][images][new_1]" accept=".png, .jpg, .jpeg" class="custom_file_input"><input type="hidden" name="avatar_remove"></label><span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Cancel avatar"><i class="ki-outline ki-cross fs-3"></i></span></div></div></div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="add_sub_section">
                                <span class="remove-btn" onclick="removeRoute('route${routeCount}_new')"><i class="fa-solid fa-trash-can fa-2xl"></i></span>
                                <button type="button" class="btn btn_grey append_images"><i class="fa-solid fa-plus"></i>Add More Images</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>`);

            $("#routes").append(newRouteDiv);
            const newEditor = $('.itinerary_des_editor').last()[0];
            ClassicEditor
                .create(newEditor)
                .catch(error => {
                    console.error(error);
                });

            // Set the start point of this new route as the end point of the previous route
            $(`#route${routeCount}_new .start-point`).val(lastRoute.find('.end-point').val());
            $(`#route${routeCount}_new .start-lat`).val(lastRoute.find('.end-lat').val());
            $(`#route${routeCount}_new .start-lng`).val(lastRoute.find('.end-lng').val());
            initAutocomplete(map);
            createDropzone(`route-${routeCount}_new-images-div`, `route${routeCount}_images_input`);

        }

        function generatePathCoordinates(routeContainers) {
            const pathCoordinates = [];

            routeContainers.each((index, routeContainer) => {
                const endLat = $(routeContainer).find(".end-lat").val();
                const endLng = $(routeContainer).find(".end-lng").val();

                // For the first route-container, include both start and end points
                if (index === 0) {
                    const startLat = $(routeContainer).find(".start-lat").val();
                    const startLng = $(routeContainer).find(".start-lng").val();

                    // Add start point if it exists
                    if (startLat && startLng) {
                        pathCoordinates.push({
                            lat: parseFloat(startLat),
                            lng: parseFloat(startLng),
                        });
                    }
                }
                // Add end point for all containers
                if (endLat && endLng) {
                    pathCoordinates.push({
                        lat: parseFloat(endLat),
                        lng: parseFloat(endLng),
                    });
                }
            });

            // console.log("Generated pathCoordinates:", pathCoordinates);
            return pathCoordinates;
        }

        function displayPinsOnMap(map) {
            const routeContainers = $(".route-container");

            // Generate path coordinates from route containers
            const pathCoordinates = generatePathCoordinates(routeContainers);

            // Clear existing markers
            markers.forEach(marker => marker.setMap(null));
            markers = [];

            // Add markers for each coordinate
            let newIndex = 1;
            pathCoordinates.forEach((location, index) => {
                const marker = new google.maps.Marker({
                    position: location,
                    map: map,
                    title: `Location ${newIndex}`, // Title based on array order
                });
                markers.push(marker);
                newIndex++;
            });

            // Optionally draw a polyline connecting the points
            // if (pathCoordinates.length > 1) {
            //     const polyline = new google.maps.Polyline({
            //         path: pathCoordinates,
            //         geodesic: true,
            //         strokeColor: "#FF0000",
            //         strokeOpacity: 1.0,
            //         strokeWeight: 2,
            //     });
            //     polyline.setMap(map);
            // }

            // Adjust the map view to fit all markers
            if (pathCoordinates.length > 0) {
                const bounds = new google.maps.LatLngBounds();
                pathCoordinates.forEach(loc => bounds.extend(loc));
                map.fitBounds(bounds);
            }
        }

        // Remove a specific route
        function removeRoute(routeId) {
            $(`#${routeId}`).remove();
            updateRouteStartPoints();
        }

        function updateRouteStartPoints() {
            const routeContainers = $(".route-container");
            let lastEndPoint = null;
            let lastEndLat = null;
            let lastEndLng = null;

            routeContainers.each((_, routeContainer) => {
                const startInput = $(routeContainer).find(".start-point");
                const startLat = $(routeContainer).find(".start-lat");
                const startLng = $(routeContainer).find(".start-lng");
                const endPoint = $(routeContainer).find(".end-point");
                const endLat = $(routeContainer).find(".end-lat");
                const endLng = $(routeContainer).find(".end-lng");

                if (lastEndPoint) {
                    startInput.val(lastEndPoint.val());
                    startLat.val(lastEndLat.val());
                    startLng.val(lastEndLng.val());
                } else {
                    // startInput.prop("disabled", false);
                }
                // Update the last endpoint references
                lastEndPoint = endPoint;
                lastEndLat = endLat;
                lastEndLng = endLng;
            });
        }

        window.initMap = initMap;
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            const checkboxes = document.querySelectorAll('.section-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                        const checkedBoxes = document.querySelectorAll('.section-checkbox:checked');
                        if (checkedBoxes.length > 2) {
                        checkbox.checked = false; // Deselect if more than 2 are selected
                            alert('You can select up to 2 sections only.');
                        }
                });
            });
        });
    </script>
    
    
    <script>
        $(document).ready(function() {
            var currentStep = 1;
            var totalSteps = $(".stepper_step").length;
            $(".stepper_step").hide();
            $("#step-" + currentStep).show();
            $(".stepper_form_previous").hide();

            function updateButtons() {
                $(".stepper_form_previous").toggle(currentStep > 1);
                $(".stepper_form_next").toggle(currentStep < totalSteps);
                $('.tab').removeClass('active');
                $('.tab[data-step="' + currentStep + '"]').addClass('active');
            }

            // Next Button Click
            $(".stepper_form_next").click(function() {
                if (currentStep < totalSteps) {
                    $("#step-" + currentStep).hide();
                    currentStep++;
                    $("#step-" + currentStep).show();
                    updateButtons();
                }
            });

            // Previous Button Click
            $(".stepper_form_previous").click(function() {
                if (currentStep > 1) {
                    $("#step-" + currentStep).hide();
                    currentStep--;
                    $("#step-" + currentStep).show();
                    updateButtons();
                }
            });

            // Tab Click Functionality
            $('.trips_navigation ul li.tab').click(function() {
                let step = $(this).data('step');
                if (step !== currentStep) {
                    $("#step-" + currentStep).hide();
                    currentStep = step;
                    $("#step-" + currentStep).show();
                    updateButtons();
                }
            });

            updateButtons();
        });
        $(document).ready(function () {
            $(document).on('change','.location_section .custom_check_box .form-check-input[type=checkbox]',function() {
                if ($(this).is(':checked')) {
                    $('.cms_section .txt_field input.end-point').prop('disabled', true);
                } else {
                    $('.cms_section .txt_field input.end-point').prop('disabled', false);
                }
            });
        });
    </script>
    
    <?php
        $imageCategories = [
            'mainHeaderImages' => $trip->mainHeaderImages->map(function ($file) {
                $absolutePath = public_path('website/' . $file->image);
                return [
                    'id' => $file->id,
                    'path' => $file->image,
                    'size' => file_exists($absolutePath) ? filesize($absolutePath) : 0,
                ];
            }),
            'activityImages' => $trip->activityImages->map(function ($file) {
                $absolutePath = public_path('website/' . $file->image);
                return [
                    'id' => $file->id,
                    'path' => $file->image,
                    'size' => file_exists($absolutePath) ? filesize($absolutePath) : 0,
                ];
            }),
            'boatSpecImages' => $trip->boatSpecImages->map(function ($file) {
                $absolutePath = public_path('website/' . $file->image);
                return [
                    'id' => $file->id,
                    'path' => $file->image,
                    'size' => file_exists($absolutePath) ? filesize($absolutePath) : 0,
                ];
            }),
            'safetyOnBoardImages' => $trip->safetyOnBoardImages->map(function ($file) {
                $absolutePath = public_path('website/' . $file->image);
                return [
                    'id' => $file->id,
                    'path' => $file->image,
                    'size' => file_exists($absolutePath) ? filesize($absolutePath) : 0,
                ];
            }),
            'menuPlanImages' => $trip->menuPlanImages->map(function ($file) {
                $absolutePath = public_path('website/' . $file->image);
                return [
                    'id' => $file->id,
                    'path' => $file->image,
                    'size' => file_exists($absolutePath) ? filesize($absolutePath) : 0,
                ];
            }),
            'spaMenuImages' => $trip->spaMenuImages->map(function ($file) {
                $absolutePath = public_path('website/' . $file->image);
                return [
                    'id' => $file->id,
                    'path' => $file->image,
                    'size' => file_exists($absolutePath) ? filesize($absolutePath) : 0,
                ];
            }),
        ];
        foreach ($trip->itineraries as $itinerary) {
            $imageCategories['route' . $itinerary->id . 'Images'] = $itinerary->images->map(function ($file) {
                $absolutePath = public_path('website/' . $file->image);
                return [
                    'id' => $file->id,
                    'path' => $file->image,
                    'size' => file_exists($absolutePath) ? filesize($absolutePath) : 0,
                ];
            });
        }
    ?>

    <script>
        let imageFiles = <?php echo json_encode($imageCategories, 15, 512) ?>;
        let tripItineraries = <?php echo json_encode($trip->itineraries, 15, 512) ?>;

        function initializeDropzone(selector, uploadUrl, deleteUrl, category) {
            new Dropzone(selector, {
                url: uploadUrl,
                paramName: "file",
                acceptedFiles: ".jpg,.jpeg,.png,.gif",
                maxFiles: null,
                maxFilesize: 10, // MB
                addRemoveLinks: true,
                thumbnailWidth: 100,
                thumbnailHeight: 100,
                headers: {
                    'X-CSRF-TOKEN': "<?php echo e(csrf_token()); ?>"
                },

                init: function() {
                    let dropzone = this;

                    // Initialize existing files
                    imageFiles[category].forEach(file => {
                        let mockFile = {
                            size: file.size,
                            id: file.id,
                            serverId: file.id
                        };

                        dropzone.emit("addedfile", mockFile);
                        dropzone.emit("thumbnail", mockFile, "<?php echo e(asset('website')); ?>/" + file.path);
                        dropzone.emit("complete", mockFile);

                        // Hide file name and size for existing files
                        if (mockFile.previewElement) {
                            let fileNameElement = mockFile.previewElement.querySelector(
                                "[data-dz-name]");
                            if (fileNameElement) {
                                fileNameElement.style.display = "none";
                            }

                            let fileSizeElement = mockFile.previewElement.querySelector(
                                "[data-dz-size]");
                            if (fileSizeElement) {
                                fileSizeElement.style.display = "none";
                            }
                        }
                    });

                    // this.on("sending", function(file, xhr, formData) {
                    //     formData.append("trip_id", <?php echo e($trip->id); ?>);
                    // });

                    this.on("addedfile", function(file) {
                        // Hide file name
                        if (file.previewElement) {
                            let fileNameElement = file.previewElement.querySelector("[data-dz-name]");
                            if (fileNameElement) {
                                fileNameElement.style.display = "none";
                            }

                            // Hide file size
                            let fileSizeElement = file.previewElement.querySelector("[data-dz-size]");
                            if (fileSizeElement) {
                                fileSizeElement.style.display = "none";
                            }
                        }
                        // Validate file size
                        if (file.size > 10 * 1024 * 1024) { // 20MB limit
                            alert("Some Files size exceeds 10MB limit!");
                            this.removeFile(file);
                            return;
                        }

                        // Validate file type
                        const allowedTypes = ["image/jpeg", "image/png", "image/gif"];
                        if (!allowedTypes.includes(file.type)) {
                            alert("Invalid file type! Only JPG, PNG, and GIF are allowed.");
                            this.removeFile(file);
                            return;
                        }
                    });

                    this.on("success", function(file, response) {
                        file.serverId = response.id;
                    });

                    this.on("removedfile", function(file) {
                        if (!file.serverId) return;

                        fetch(deleteUrl, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': "<?php echo e(csrf_token()); ?>"
                                },
                                body: JSON.stringify({
                                    id: file.serverId,
                                    _method: 'DELETE',
                                    _token: "<?php echo e(csrf_token()); ?>"
                                })
                            })
                            .then(response => response.json())
                            .then(data => console.log(data.message))
                            .catch(error => console.error('Error:', error));
                    });
                }
            });
        }

        initializeDropzone("#main-header-images-div", "<?php echo e(route('trip.image.add', [$trip->id, 'mainHeaderImages'])); ?>",
            "<?php echo e(route('trip.image.delete', [$trip->id, 'mainHeaderImages'])); ?>", "mainHeaderImages");
        initializeDropzone("#activity-images-div", "<?php echo e(route('trip.image.add', [$trip->id, 'activityImages'])); ?>",
            "<?php echo e(route('trip.image.delete', [$trip->id, 'activityImages'])); ?>", "activityImages");
        initializeDropzone("#boat-specs-images-div", "<?php echo e(route('trip.image.add', [$trip->id, 'boatSpecImages'])); ?>",
            "<?php echo e(route('trip.image.delete', [$trip->id, 'boatSpecImages'])); ?>", "boatSpecImages");
        initializeDropzone("#safety-onboard-images-div",
            "<?php echo e(route('trip.image.add', [$trip->id, 'safetyOnBoardImages'])); ?>",
            "<?php echo e(route('trip.image.delete', [$trip->id, 'safetyOnBoardImages'])); ?>", "safetyOnBoardImages");


        tripItineraries.forEach(itinerary => {
            initializeDropzone(`#route-${itinerary.id}-images-div`,
                `<?php echo e(route('iti.image.add', ['__ITINERARY_ID__', '__ITINERARY_IMAGES__'])); ?>`.replace(
                    '__ITINERARY_ID__', `${itinerary.id}`).replace('__ITINERARY_IMAGES__',
                    `route${itinerary.id}Images`),
                `<?php echo e(route('iti.image.delete', ['__ITINERARY_ID__', '__ITINERARY_IMAGES__'])); ?>`.replace(
                    '__ITINERARY_ID__', `${itinerary.id}`).replace(
                    '__ITINERARY_IMAGES__', `route${itinerary.id}Images`),
                `route${itinerary.id}Images`);
        });

        initializeDropzone("#menu-plan-images-div", "<?php echo e(route('trip.image.add', [$trip->id, 'menuPlanImages'])); ?>",
            "<?php echo e(route('trip.image.delete', [$trip->id, 'menuPlanImages'])); ?>", "menuPlanImages");
        initializeDropzone("#spa-menu-images-div", "<?php echo e(route('trip.image.add', [$trip->id, 'spaMenuImages'])); ?>",
            "<?php echo e(route('trip.image.delete', [$trip->id, 'spaMenuImages'])); ?>", "spaMenuImages");



        function createDropzone(dropzoneId, inputId) {
            let myDropzone = new Dropzone(`#${dropzoneId}`, {
                url: "/upload", // Dummy URL, form handles upload
                paramName: "images[]", // Must match input name
                autoProcessQueue: false, // Prevent automatic uploads
                uploadMultiple: true,
                addRemoveLinks: true,
                parallelUploads: 5,
                maxFilesize: 5, // Max 5MB
                acceptedFiles: "image/*",
                dictDefaultMessage: "Drop files here or click to upload"
            });

            let imageInput = document.getElementById(inputId);
            let fileMap = new Map(); // Store file objects

            // Function to update hidden input with Dropzone files
            function updateHiddenInput() {
                let dataTransfer = new DataTransfer();

                fileMap.forEach((file) => {
                    dataTransfer.items.add(file);
                });

                imageInput.files = dataTransfer.files;
            }

            // Event: File added to Dropzone
            myDropzone.on("addedfile", function(file) {
                // Hide file name
                if (file.previewElement) {
                    let fileNameElement = file.previewElement.querySelector("[data-dz-name]");
                    if (fileNameElement) {
                        fileNameElement.style.display = "none";
                    }

                    // Hide file size
                    let fileSizeElement = file.previewElement.querySelector("[data-dz-size]");
                    if (fileSizeElement) {
                        fileSizeElement.style.display = "none";
                    }
                }
                fileMap.set(file.name, file);
                updateHiddenInput();
            });
            // Event: File removed from Dropzone
            myDropzone.on("removedfile", function(file) {
                fileMap.delete(file.name);
                updateHiddenInput();
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\trips\editOLD.blade.php ENDPATH**/ ?>