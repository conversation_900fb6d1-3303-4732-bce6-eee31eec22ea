<?php $__env->startPush('css'); ?>
    <link href="assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />
    <style>
        .image-input-placeholder {
            background-image: url("<?php echo e(asset('website/assets/media/avatars')); ?>/avatar.svg");
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                    <?php echo e(config('app.name')); ?></h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="<?php echo e(url('home')); ?>" class="text-muted text-hover-primary">Home</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-400 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">settings</li>
                </ul>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<div id="" class="app-content flex-column-fluid">
    <div id="kt_app_content_container" class="app-container container-xxl">
        <?php if($errors->any()): ?>
            <div class="alert alert-danger">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php echo e($error); ?> <br>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>
        <div class="card">
            <div class="card-body">
                <form method="post" id="kt_modal_update_role_form" action="<?php echo e(route('settings.update', $setting->id)); ?>" class="form-horizontal"
                    enctype="multipart/form-data">
                    <?php echo e(method_field('PATCH')); ?>

                    <?php echo e(csrf_field()); ?>

                    <div class="row g-7">
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Title</span>
                            </label>
                            <input type="text" name="title" class="form-control form-control-solid" placeholder=""
                                value="<?php echo e($setting->title ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="required">Description</span>
                            </label>
                            <textarea class="form-control form-control-solid" rows="3" name="description" placeholder="Type Target Details"><?php echo e($setting->description ?? ''); ?></textarea>
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Favicon</span>
                            </label>
                            <!--begin::Image input-->
                            <div>
                                <div class="image-input image-input-placeholder image-input-empty"
                                    data-kt-image-input="true">
                                    <!--begin::Image preview wrapper-->
                                    <div class="image-input-wrapper w-125px h-125px"
                                        style="background-image: url('<?php echo e(asset('')); ?><?php echo e($setting->favicon ?? ''); ?>');">
                                    </div>
                                    <!--end::Image preview wrapper-->

                                    <!--begin::Edit button-->
                                    <label
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Change avatar">
                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                class="path2"></span></i>

                                        <!--begin::Inputs-->
                                        <input type="file" name="favicon" value="<?php echo e($setting->favicon ?? ''); ?>"
                                            accept=".png, .jpg, .jpeg" />
                                        <input type="hidden" name="avatar_remove" />
                                        <!--end::Inputs-->
                                    </label>
                                    <!--end::Edit button-->

                                    <!--begin::Cancel button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Cancel avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Cancel button-->

                                    <!--begin::Remove button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Remove avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Remove button-->
                                </div>
                                <!--end::Image input-->
                            </div>
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Logo</span>
                            </label>
                            <!--begin::Image input-->
                            <div>
                                <div class="image-input image-input-placeholder image-input-empty"
                                    data-kt-image-input="true">
                                    <!--begin::Image preview wrapper-->
                                    <div class="image-input-wrapper w-125px h-125px"
                                        style="background-image: url('<?php echo e(asset('')); ?><?php echo e($setting->logo ?? ''); ?>');">
                                    </div>
                                    <!--end::Image preview wrapper-->

                                    <!--begin::Edit button-->
                                    <label
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Change avatar">
                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                                class="path2"></span></i>

                                        <!--begin::Inputs-->
                                        <input type="file" name="logo" value="<?php echo e($setting->logo ?? ''); ?>"
                                            accept=".png, .jpg, .jpeg" />
                                        <input type="hidden" name="avatar_remove" />
                                        <!--end::Inputs-->
                                    </label>
                                    <!--end::Edit button-->

                                    <!--begin::Cancel button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Cancel avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Cancel button-->

                                    <!--begin::Remove button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Remove avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Remove button-->
                                </div>
                                <!--end::Image input-->
                            </div>
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Footer_text</span>
                            </label>
                            <input type="text" name="footer_text" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->footer_text ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Facebook
                                    <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24"
                                            height="24" viewBox="0 0 24 24" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path opacity="0.3"
                                                d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                                                fill="currentColor" />
                                            <path
                                                d="M13.643 9.36206C13.6427 9.05034 13.7663 8.75122 13.9864 8.53052C14.2065 8.30982 14.5053 8.18559 14.817 8.18506H15.992V5.23999H13.643C13.1796 5.24052 12.7209 5.33229 12.293 5.51013C11.8651 5.68798 11.4764 5.94841 11.1491 6.27649C10.8219 6.60457 10.5625 6.99389 10.3857 7.42224C10.209 7.85059 10.1183 8.30956 10.119 8.77295V11.718H7.769V14.663H10.119V21.817C11.2812 22.0479 12.4762 22.0604 13.643 21.854V14.663H15.992L17.167 11.718H13.643V9.36206Z"
                                                fill="currentColor" />
                                        </svg>
                                    </span>
                                    <!--end::Svg Icon--></span>
                            </label>
                            <input type="text" name="facebook" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->facebook ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Twitter
                                    <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24"
                                            height="24" viewBox="0 0 24 24" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path opacity="0.3"
                                                d="M19.0003 4.40002C18.2003 3.50002 17.1003 3 15.8003 3C14.1003 3 12.5003 3.99998 11.8003 5.59998C11.0003 7.39998 11.9004 9.49993 11.2004 11.2999C10.1004 14.2999 7.80034 16.9 4.90034 17.9C4.50034 18 3.80035 18.2 3.10035 18.2C2.60035 18.3 2.40034 19 2.90034 19.2C4.40034 19.8 6.00033 20.2 7.80033 20.2C15.8003 20.2 20.2004 13.5999 20.2004 7.79993C20.2004 7.59993 20.2004 7.39995 20.2004 7.19995C20.8004 6.69995 21.4003 6.09993 21.9003 5.29993C22.2003 4.79993 21.9003 4.19998 21.4003 4.09998C20.5003 4.19998 19.7003 4.20002 19.0003 4.40002Z"
                                                fill="currentColor" />
                                            <path
                                                d="M11.5004 8.29997C8.30036 8.09997 5.60034 6.80004 3.30034 4.50004C2.90034 4.10004 2.30037 4.29997 2.20037 4.79997C1.60037 6.59997 2.40035 8.40002 3.90035 9.60002C3.50035 9.60002 3.10037 9.50007 2.70037 9.40007C2.40037 9.30007 2.00036 9.60004 2.10036 10C2.50036 11.8 3.60035 12.9001 5.40035 13.4001C5.10035 13.5001 4.70034 13.5 4.30034 13.6C3.90034 13.6 3.70035 14.1001 3.90035 14.4001C4.70035 15.7001 5.90036 16.5 7.50036 16.5C8.80036 16.5 10.1004 16.5 11.2004 15.8C12.7004 14.9 13.9003 13.2001 13.8003 11.4001C13.9003 10.0001 13.1004 8.39997 11.5004 8.29997Z"
                                                fill="currentColor" />
                                        </svg>
                                    </span>
                                    <!--end::Svg Icon--></span>
                            </label>
                            <input type="text" name="twitter" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->twitter ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Youtube
                                    <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24"
                                            height="24" viewBox="0 0 24 24" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M21 6.30005C20.5 5.30005 19.9 5.19998 18.7 5.09998C17.5 4.99998 14.5 5 11.9 5C9.29999 5 6.29998 4.99998 5.09998 5.09998C3.89998 5.19998 3.29999 5.30005 2.79999 6.30005C2.19999 7.30005 2 8.90002 2 11.9C2 14.8 2.29999 16.5 2.79999 17.5C3.29999 18.5 3.89998 18.6001 5.09998 18.7001C6.29998 18.8001 9.29999 18.8 11.9 18.8C14.5 18.8 17.5 18.8001 18.7 18.7001C19.9 18.6001 20.5 18.4 21 17.5C21.6 16.5 21.8 14.9 21.8 11.9C21.8 9.00002 21.5 7.30005 21 6.30005ZM9.89999 15.7001V8.20007L14.5 11C15.3 11.5 15.3 12.5 14.5 13L9.89999 15.7001Z"
                                                fill="currentColor" />
                                        </svg>
                                    </span>
                                    <!--end::Svg Icon--></span>
                            </label>
                            <input type="text" name="youtube" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->youtube ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Instagram
                                    <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24"
                                            height="24" viewBox="0 0 24 24" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path opacity="0.3"
                                                d="M22 8.20267V15.7027C22 19.1027 19.2 22.0026 15.7 22.0026H8.2C4.8 22.0026 2 19.2027 2 15.7027V8.20267C2 4.80267 4.8 2.0026 8.2 2.0026H15.7C19.2 1.9026 22 4.70267 22 8.20267ZM12 7.30265C9.5 7.30265 7.39999 9.40262 7.39999 11.9026C7.39999 14.4026 9.5 16.5026 12 16.5026C14.5 16.5026 16.6 14.4026 16.6 11.9026C16.6 9.30262 14.5 7.30265 12 7.30265ZM17.9 5.0026C17.3 5.0026 16.9 5.4026 16.9 6.0026C16.9 6.6026 17.3 7.0026 17.9 7.0026C18.5 7.0026 18.9 6.6026 18.9 6.0026C18.9 5.5026 18.4 5.0026 17.9 5.0026Z"
                                                fill="currentColor" />
                                            <path
                                                d="M12 17.5026C8.9 17.5026 6.39999 15.0026 6.39999 11.9026C6.39999 8.80259 8.9 6.30261 12 6.30261C15.1 6.30261 17.6 8.80259 17.6 11.9026C17.6 15.0026 15.1 17.5026 12 17.5026ZM12 8.30261C10 8.30261 8.39999 9.90259 8.39999 11.9026C8.39999 13.9026 10 15.5026 12 15.5026C14 15.5026 15.6 13.9026 15.6 11.9026C15.6 9.90259 14 8.30261 12 8.30261Z"
                                                fill="currentColor" />
                                        </svg>
                                    </span>
                                    <!--end::Svg Icon--></span>
                            </label>
                            <input type="text" name="instagram" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->instagram ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Whatsapp</span>
                            </label>
                            <input type="text" name="whatsapp" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->whatsapp ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Stripe Publishable Key</span>
                            </label>
                            <input type="text" name="stripe_publishable" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->stripe_publishable ?? ''); ?>" />
                        </div>
                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="">Stripe Secret Key</span>
                            </label>
                            <input type="text" name="stripe_secret" class="form-control form-control-solid"
                                placeholder="" value="<?php echo e($setting->stripe_secret ?? ''); ?>" />
                        </div>

                        <div class="col-md-12 fv-row">
                            <label class="form-label">
                                <span class="required">Time Zone</span></label>
                            <select name="timezone" aria-label="Select a Timezone" data-control="select2"
                                data-placeholder="Select a timezone.."
                                class="form-select form-select-solid form-select-lg">
                                <option value="">Select a Timezone..</option>
                                 <?php $__currentLoopData = $timezones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $timezone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option data-bs-offset="-<?php echo e($timezone); ?>" value="<?php echo e($timezone); ?>" <?php echo e(old('timezone') == $timezone ? 'selected' : ''); ?>>
                                    <?php echo e($timezone); ?>

                                </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="row mb-8">
                                                    <!--begin::Col-->
                                                    <div class="col-md-12 fv-row">
                                                        <div class="fs-6 fw-semibold mt-2 mb-3">Due Date</div>
                                                    </div>
                                                    <!--end::Col-->
                                                    <!--begin::Col-->
                                                    <div class="col-xl-9 fv-row">
                                                        <div class="position-relative d-flex align-items-center">
                                                            <i class="ki-duotone ki-calendar-8 position-absolute ms-4 mb-1 fs-2">
                                                                <span class="path1"></span>
                                                                <span class="path2"></span>
                                                                <span class="path3"></span>
                                                                <span class="path4"></span>
                                                                <span class="path5"></span>
                                                                <span class="path6"></span>
                                                            </i>
                                                            <input class="form-control form-control-solid ps-12" name="date" placeholder="Pick a date" id="kt_datepicker_1" />
                                                        </div>
                                                    </div>
                                                    <!--begin::Col-->
                                                </div>
                                                <!--begin::Card footer-->
                                                <div class="card-footer d-flex justify-content-end py-6 px-9">
                                                    <a href="<?php echo e(route('settings.index')); ?>"class="btn btn-light btn-active-light-primary me-2">Cancel</a>
                                                    <button type="submit" class="btn btn-primary" id="kt_project_settings_submit">Save Changes</button>
                                                </div>
                                                <!--end::Card footer-->
                                            </div>
                                        </div>
                                    </form>
        </div>
    </div>
</div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>var hostUrl = "assets/";</script>
        <!--begin::Global Javascript Bundle(mandatory for all pages)-->
        <script src="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.js"></script>
        <script src="<?php echo e(asset('website')); ?>/assets/js/scripts.bundle.js"></script>
        <!--end::Global Javascript Bundle-->
        <!--begin::Vendors Javascript(used for this page only)-->
        <script src="<?php echo e(asset('website')); ?>/assets/plugins/custom/datatables/datatables.bundle.js"></script>
        <!--end::Vendors Javascript-->
        <!--begin::Custom Javascript(used for this page only)-->
        <script src="<?php echo e(asset('website')); ?>/assets/js/custom/apps/projects/settings/settings.js"></script>
        <script src="<?php echo e(asset('website')); ?>/assets/js/widgets.bundle.js"></script>
        <script src="<?php echo e(asset('website')); ?>/assets/js/custom/widgets.js"></script>
        <script src="<?php echo e(asset('website')); ?>/assets/js/custom/utilities/modals/upgrade-plan.js"></script>
        <script src="<?php echo e(asset('website')); ?>/assets/js/custom/utilities/modals/create-app.js"></script>
        <script src="<?php echo e(asset('website')); ?>/assets/js/custom/utilities/modals/users-search.js"></script>
        <script src="<?php echo e(asset('website')); ?>/assets/js/custom/utilities/modals/new-target.js"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\settings\edit.blade.php ENDPATH**/ ?>