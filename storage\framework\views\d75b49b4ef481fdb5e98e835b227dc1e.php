<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <?php if(auth()->user()->hasRole('admin')): ?>
        <section class="trip_management">
            <div class="container-fluid">
                <div class="row custom_row">
                    <div class="col-md-12">
                        <div class="custom_table custom_cards_design">
                            <h1>Trip Management</h1>
                            <table class="table myTable datatable">
                                <thead>
                                <tr>
                                    <th>Sr#</th>
                                    <th>Trip Name</th>
                                    <th>Company Name</th>
                                    <th>URL</th>
                                    <th>Duration</th>
                                    <th>Package Type</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php for($i=0;$i<12;$i++): ?>
                                    <tr>
                                        <td>01</td>
                                        <td>Vacation</td>
                                        <td>Yacht IQ</td>
                                        <td>https:guesttrips.com/vacation</td>
                                        <td>20 Days</td>
                                        <td>Gold</td>
                                        <td><span class="success">Active</span></td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                    <li><a href="<?php echo e(url('trip_view')); ?>" class="dropdown-item"><i class="fa-solid fa-eye"></i>View</a></li>
                                                    <li><a href="#!" class="dropdown-item"><i class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                    <li><a href="#!" class="dropdown-item"><i class="fa-solid fa-close"></i>Deactivate</a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endfor; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php elseif(auth()->user()->hasRole('owner')): ?>
        <section class="trip_management">
            <div class="container-fluid">
                <div class="row custom_row">
                    <div class="col-md-12">
                        <div class="custom_table custom_cards_design">
                            <div class="create_trip_btn">
                                <h1>Trip Management</h1>
                                <button type="button" data-bs-toggle="modal" data-bs-target="#create_trip_modal" class="btn btn_dark_green">Create Trip</button>
                            </div>
                            <table class="table myTable datatable">
                                <thead>
                                <tr>
                                    <th>Sr#</th>
                                    <th>Trip Name</th>
                                    <th>URL</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Duration</th>
                                    <th>Package Type</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php for($i=0;$i<6;$i++): ?>
                                    <tr>
                                        <td>01</td>
                                        <td>Vacation</td>
                                        <td>https:guesttrips.com/vacation</td>
                                        <td>11/5/2024</td>
                                        <td>20/5/2024</td>
                                        <td>9 Days</td>
                                        <td>Gold</td>
                                        <td><span class="success">Active</span></td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                    <li><a href="<?php echo e(url('trip_view')); ?>" class="dropdown-item"><i class="fa-solid fa-eye"></i> View</a></li>
                                                    <li><a href="#!" class="dropdown-item"><i class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                    <li><a href="#!" class="dropdown-item"><i class="fa-solid fa-close"></i>Deactivate</a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endfor; ?>

                                <?php for($i=0;$i<6;$i++): ?>
                                    <tr>
                                        <td>01</td>
                                        <td>Vacation</td>
                                        <td>https:guesttrips.com/vacation</td>
                                        <td>13/6/2024</td>
                                        <td>24/7/2024</td>
                                        <td>40 Days</td>
                                        <td>Gold</td>
                                        <td><span class="success">Active</span></td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                    <li><a href="<?php echo e(url('trip_view')); ?>" class="dropdown-item"><i class="fa-solid fa-eye"></i> View</a></li>
                                                    <li><a href="#!" class="dropdown-item"><i class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                    <li><a href="#!" class="dropdown-item"><i class="fa-solid fa-close"></i>Deactivate</a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endfor; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        
        <!-- Modal -->
        <div class="modal fade create_trip_modal" id="create_trip_modal" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title" id="createModalLabel">Create Trip</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body ">
                        <form method="post" action="<?php echo e(route('trips.store')); ?>">
                            <?php echo csrf_field(); ?>
                            <div class="row custom_row_modal">
                                <div class="col-md-12">
                                    <div class="txt_field">
                                        <label>Trip name:</label>
                                        <input type="text" name="name" class="form-control" placeholder="Vacation" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="txt_field">
                                        <label>Start Date:</label>
                                        <input type="date" name="start_date" class="form-control" placeholder="13/5/2024" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="txt_field">
                                        <label>End Date:</label>
                                        <input type="date" name="end_date" class="form-control" placeholder="20/5/2024" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="txt_field">
                                        <label>Duration:</label>
                                        <input type="text" name="duration" class="form-control" placeholder="20 Days" required>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="txt_field">
                                        <label>URL:</label>
                                        <input type="link" name="url_slug" class="form-control" placeholder="www.guestrip.com/vacation" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn_dark_green">Add Trip</button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn_transparent" data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>


<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\TripManagement\trip_management_index.blade.php ENDPATH**/ ?>