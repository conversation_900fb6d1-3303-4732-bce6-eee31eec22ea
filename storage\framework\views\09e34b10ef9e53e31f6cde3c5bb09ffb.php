<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="trip_management">
        <div class="container-fluid">
            <div class="row custom_row">
                <div class="col-md-8">
                    <form method="post" action="<?php echo e(route('trips.update.basic', $trip->url_slug)); ?>">
                        <?php echo e(method_field('PATCH')); ?>

                        <?php echo e(csrf_field()); ?>

                        <div class="custom_cards_design">
                            <div class="row custom_row">
                                <div class="col-md-12">
                                    <h1>Trip Management</h1>
                                </div>
                                <div class="col-md-12">
                                    <div class="edit_user_trip custom_justify_between icon_image">
                                        <div class="profile_changes">
                                            <div class="edit_change custom_flex">
                                                <?php if(auth()->user()->hasRole('owner')): ?>
                                                    <a class="btn btn_dark_green edit_profile">Edit</a>
                                                <?php endif; ?>
                                                <a class="btn btn_transparent toggle-status"
                                                    data-slug="<?php echo e($trip->slug); ?>"><?php echo e($trip->status === 'active' ? 'Deactivate' : 'Activate'); ?></a>
                                                <?php if(auth()->user()->hasRole('owner')): ?>
                                                    <a target="_blank" id="trip_url"
                                                        href="<?php echo e(route('trip.itinerary.overview', [$trip->company_slug, $trip->url_slug])); ?>"
                                                        class="btn btn_grey">View Trip Details</a>
                                                    <a href="<?php echo e(route('trips.edit', $trip->slug)); ?>"
                                                        class="btn btn_grey">Edit Trip
                                                        Details</a>
                                                <?php endif; ?>
                                            </div>
                                            <div class="save_cancel_btn custom_flex">
                                                <button type="submit" class="btn btn_dark_green">Save
                                                    changes</button>
                                                <button type="button"
                                                    class="btn btn_transparent cancel_edit">Cancel</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <h2>Trip Information</h2>
                                </div>
                                <div class="col-md-12">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Trip name:</label>
                                        <input type="text" name="name" value="<?php echo e(old('name', $trip->name ?? '')); ?>"
                                            class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> myinput" id=""
                                            required placeholder="Vacation" readonly>
                                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="alert alert-danger mt-2"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="txt_field">
                                        <label for="url_slug" class="form-label">URL:
                                            <?php echo e(url('/')); ?>/<?php echo e($trip->user->company_name ?? ''); ?>/</label>
                                        <div class="input-group">
                                            <input type="link"
                                                class="form-control <?php $__errorArgs = ['url_slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> myinput"
                                                id="url_slug" required name="url_slug"
                                                value="<?php echo e(old('url_slug', $trip->url_slug ?? '')); ?>" readonly>
                                            <span class="" data-bs-toggle="tooltip" data-bs-placement="top"
                                                title="Copy" id="copyButton" style="cursor: pointer;">
                                                <i class="fa-solid fa-copy fa-2xl"
                                                    style="font-size: 18px; color:#03525A;"></i>
                                            </span>
                                        </div>
                                        <?php $__errorArgs = ['url_slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="alert alert-danger mt-2"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label>Start Date:</label>
                                        <input type="date" name="start_date"
                                            value="<?php echo e(old('start_date', $trip->start_date ?? '')); ?>"
                                            class="form-control <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            placeholder="13/5/2024" required id="start_date">
                                        <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="alert alert-danger mt-2"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label>End Date:</label>
                                        <input type="date" name="end_date"
                                            value="<?php echo e(old('end_date', $trip->end_date ?? '')); ?>"
                                            class="form-control <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            placeholder="20/5/2024" required id="end_date">
                                        <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="alert alert-danger mt-2"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Duration:</label>
                                        <input type="text" name="duration"
                                            value="<?php echo e(old('duration', $trip->duration ?? '')); ?>"
                                            class="form-control <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            placeholder="20 Days" required id="duration" readonly>
                                        <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="alert alert-danger mt-2"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Package Type:</label>
                                        <input type="text" class="form-control" id="" required readonly
                                            value="<?php echo e($trip->package->name ?? ''); ?>" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="col-md-4">
                    <div class="user_trip_details">
                        <div class="row custom_row_card custom_row">
                            <div class="col-md-12 custom_column_card">
                                <div class="custom_card">
                                    <h2>Starting Point</h2>
                                    <h1 class="f_30"><?php echo e($trip->itineraries->first()?->start_point ?? '-'); ?></h1>
                                </div>
                            </div>
                            <div class="col-md-12 custom_column_card">
                                <div class="custom_card">
                                    <h2>Ending point</h2>
                                    <h1 class="f_30"><?php echo e($trip->itineraries->last()?->end_point ?? '-'); ?></h1>
                                </div>
                            </div>
                            <div class="col-md-6 custom_column_card">
                                <div class="custom_card">
                                    <h2>Total Stops</h2>
                                    <h1 class="f_30"><?php echo e($trip->itineraries->count()); ?></h1>
                                </div>
                            </div>
                            <div class="col-md-6 custom_column_card">
                                <div class="custom_card">
                                    <h2>Trip Status</h2>
                                    <?php
                                        $isActive =
                                            $trip->status === 'active' &&
                                            now()->between($trip->created_at, $trip->created_at->copy()->addMonth());
                                    ?>
                                    <span id="status-label" class="<?php echo e($isActive ? 'success' : 'danger'); ?>">
                                        <?php echo e($isActive ? 'Active' : 'Inactive'); ?>

                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="custom_table custom_cards_design">
                        <h1>Trip Summary</h1>
                        <div class="custom_scroll_tbl">
                            <table class="table without_pagination_tbl datatable">
                                <thead>
                                    <tr>
                                        <th>Stop No</th>
                                        <th>Pickup Location</th>
                                        <th>Drop off Location</th>
                                        <th>Duration</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $trip->itineraries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $itinerary): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($loop->iteration); ?></td>
                                            <td><?php echo e($itinerary->start_point ?? ''); ?></td>
                                            <td><?php echo e($itinerary->end_point ?? ''); ?></td>
                                            <td><?php echo e($itinerary->duration ?? ''); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            var dataTable = $('.without_pagination_tbl').DataTable({
                "searching": true,
                "bLengthChange": false,
                "paging": false,
                "info": false,
            });
            $(document).on("input", '.custom_search_box', function() {
                var searchValue = $(this).val();
                dataTable.search(searchValue).draw();
            });
            $(".without_pagination_tbl").DataTable();
        })
    </script>
    <script>
        $(document).ready(function() {
            var copyButton = $('#copyButton');
            var tooltip = new bootstrap.Tooltip(copyButton[0]);

            copyButton.click(function() {
                var url = "<?php echo e(route('trip.itinerary.overview', [$trip->company_slug, $trip->url_slug])); ?>";

                // Create a temporary input field to copy the text
                var tempInput = $("<input>");
                $("body").append(tempInput);
                tempInput.val(url).select();
                document.execCommand("copy");
                tempInput.remove();

                // Destroy and reinitialize tooltip with "Copied!"
                copyButton.attr("data-bs-original-title", "Copied!");
                tooltip.dispose();
                tooltip = new bootstrap.Tooltip(copyButton[0]);
                tooltip.show();

                // Reset tooltip back to "Copy" after 1.5 seconds
                setTimeout(() => {
                    copyButton.attr("data-bs-original-title", "Copy");
                    tooltip.dispose();
                    tooltip = new bootstrap.Tooltip(copyButton[0]);
                }, 1500);
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            $('.toggle-status').on('click', function() {
                let button = $(this);
                let tripSlug = button.data('slug');
                let row = $(`#trip-${tripSlug}`);

                $.ajax({
                    url: "<?php echo e(url('trips')); ?>" + `/${tripSlug}/toggle-status`,
                    type: 'GET',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>',
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            // Update the button text and status
                            let newStatus = response.new_status;
                            button.html(newStatus === 'active' ? 'Deactivate' : 'Activate');
                            button.data('status', newStatus);

                            let statusLabel = $('#status-label');
                            statusLabel
                                .text(newStatus.charAt(0).toUpperCase() + newStatus.slice(
                                    1)) // Capitalize the status
                                .removeClass('success danger')
                                .addClass(newStatus === 'active' ? 'success' : 'danger');

                            // Show SweetAlert success message
                            Swal.fire({
                                title: 'Success!',
                                text: `Trip ${newStatus === 'active' ? 'Activated' : 'Deactivated'} successfully!`,
                                icon: 'success',
                                confirmButtonText: 'OK',
                            });
                        } else {
                            // Show SweetAlert error message
                            Swal.fire({
                                title: 'Error!',
                                text: 'Failed to update status. Please try again.',
                                icon: 'error',
                                confirmButtonText: 'OK',
                            });
                        }
                    },
                    error: function() {
                        // Show SweetAlert error message
                        Swal.fire({
                            title: 'Error!',
                            text: 'An error occurred. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK',
                        });
                    },
                });
            });
        });
    </script>
    <script>
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        const durationInput = document.getElementById('duration');

        // Set the minimum date as today's date
        const today = new Date().toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format
        // startDateInput.setAttribute('min', today);
        // endDateInput.setAttribute('min', today);

        function calculateDuration() {
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(endDateInput.value);

            if (startDate && endDate && endDate >= startDate) {
                const timeDiff = endDate - startDate;
                const daysDiff = timeDiff / (1000 * 3600 * 24); // Calculate the difference in days
                durationInput.value = `${daysDiff}`;
            } else if (endDate && endDate < startDate) {
                alert('End Date cannot be earlier than Start Date.');
                endDateInput.value = ''; // Clear the invalid End Date input
                durationInput.value = ''; // Clear the duration
            }
        }

        startDateInput.addEventListener('change', calculateDuration);
        endDateInput.addEventListener('change', calculateDuration);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\trips\show.blade.php ENDPATH**/ ?>