<?php $__env->startPush('css'); ?>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.css" />
    <style>
        .hero_sec {
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.60) 0%, rgba(0, 0, 0, 0.60) 100%), url('<?php echo e(isset($contact->image) ? asset('website/' . $contact->image) : asset('/website/assets/images/company_hero_sec_img.png')); ?>');
            background-repeat: no-repeat;
            background-size: cover;
            background-position: 25% 75%;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    
    <section class="hero_sec services_hero_sec">
        <div class="container custom_container">
            <div class="row">
                <div class="col-md-12">
                    <div class="hero_sec_wrapper">
                        <h1><?php echo e($contact->heading ?? ''); ?></h1>
                        <h6><?php echo e($contact->description ?? ''); ?></h6>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="contact_us_sec">
        <div class="container-fluid custom_container_fluid_service_ph">
            <div class="row column_reverse">
                <div class="col-md-6 custom_col_contact_us">
                    <div class="get_in_touch_heading">
                        <h6><?php echo e($contact->form_kicker ?? ''); ?></h6>
                        <h2><?php echo e($contact->form_heading ?? ''); ?></h2>
                        <h6><?php echo e($contact->form_sub_heading ?? ''); ?></h6>
                    </div>
                    <form id="contact_form" action="<?php echo e(route('contactform.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="form_contact_us">
                            <div class="row form_contact_us_row">
                                <div class="col-md-6">
                                    <div class="field_wrapper">
                                        <label for="fname">First name</label>
                                        <input id="fname" class="form-control <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            type="text" name="first_name" placeholder="First name"
                                            value="<?php echo e(old('first_name')); ?>" autofocus>
                                        <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="field_wrapper">
                                        <label for="lname">Last name</label>
                                        <input id="lname" class="form-control <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            type="text" name="last_name" placeholder="Last name"
                                            value="<?php echo e(old('last_name')); ?>">
                                        <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="field_wrapper">
                                        <label for="email">Email</label>
                                        <input id="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            type="email" name="email" placeholder="Email Address"
                                            value="<?php echo e(old('email')); ?>">
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="field_wrapper">
                                        <label for="phone_number">Phone Number</label>
                                        <input class="form-control phone <?php $__errorArgs = ['phone_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            id="phone_number" type="tel" name="phone_number"
                                            value="<?php echo e(old('phone_number')); ?>" />
                                        <?php $__errorArgs = ['phone_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="field_wrapper">
                                        <label for="message">Message</label>
                                        <textarea rows="5" name="message" id="message" class="form-control <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            placeholder="Type Here ...."><?php echo e(old('message')); ?></textarea>
                                        <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <!-- <div class="col-md-12">
                                            <div class="checkbox_contact_us_wrapper">
                                                <input type="checkbox" id="checkbox_contact_us_form">
                                                <label for="checkbox_contact_us_form">You agree to our friendly privacy policy.</label>
                                            </div>
                                        </div> -->
                                <div class="col-md-12">
                                    <div class="">
                                        <button type="submit" class="btn_global yellow_btn arrow_up_right_btn_img">Submit
                                            <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </form>
                </div>
                <div class="col-md-6 custom_col_contact_us">
                    <div class="image_contact_wrapper">
                        <img src="<?php echo e(asset('website/' . ($contact->form_image ?? 'assets/images/contact_us_img.png'))); ?>">
                    </div>
                    <div class="social_wrapper_contact_us">
                        <div class="phone_number">
                            <i class="fa-solid fa-phone"></i>
                            <div>
                                <h6>Phone Number</h6>
                                <a href="tel:+442086386370">
                                    <h2>+44 2086 386 370</h2>
                                </a>
                            </div>
                        </div>
                        <div class="phone_number">
                            <i class="fa-regular fa-envelope"></i>
                            <div>
                                <h6>Email Address</h6>
                                <a href="mailto:<EMAIL>">
                                    <h2><EMAIL></h2>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"></script>

    <script>
        $(document).ready(function() {
            const phoneInputField = $('.phone');
            const phoneInput = window.intlTelInput(phoneInputField[0], {
                utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js",
                initialCountry: "us",
                separateDialCode: true,
            });

            phoneInputField.on('countrychange', function() {
                const countryCode = phoneInput.getSelectedCountryData().dialCode;
                phoneInputField.val(`+${countryCode}`);
            })

        });
    </script>

    <script>
        $(document).ready(function() {
            jQuery('#contact_form').validate({
                rules: {
                    first_name: {
                        required: true,
                        minlength: 2
                    },
                    last_name: {
                        required: true,
                        minlength: 2
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    phone_number: {
                        required: true,
                        // digits: true,
                        minlength: 6,
                        maxlength: 15
                    },
                    message: {
                        required: true,
                        minlength: 10
                    }
                },
                messages: {
                    first_name: {
                        required: "Please enter your first name.",
                        minlength: "Your first name must be at least 2 characters."
                    },
                    last_name: {
                        required: "Please enter your last name.",
                        minlength: "Your last name must be at least 2 characters."
                    },
                    email: {
                        required: "Please enter your email.",
                        email: "Please enter a valid email address."
                    },
                    phone_number: {
                        required: "Please enter your phone number.",
                        digits: "Only digits are allowed.",
                        minlength: "Phone number must be at least 6 digits.",
                        maxlength: "Phone number must not exceed 15 digits."
                    },
                    message: {
                        required: "Please enter your message.",
                        minlength: "Your message must be at least 10 characters."
                    }
                },
                errorElement: "span",
                errorClass: "invalid-feedback",
                highlight: function(element, errorClass) {
                    $(element).addClass("is-invalid");
                },
                unhighlight: function(element, errorClass) {
                    $(element).removeClass("is-invalid");
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\website\contact_us.blade.php ENDPATH**/ ?>