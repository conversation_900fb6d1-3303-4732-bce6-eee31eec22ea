<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="homepage_section">
        <div class="container-fluid">
            <div class="row custom_row">
                <div class="col-md-12">
                    <div class="homepage_cards owner_home_pg_cards">
                        <div class="row">
                            
                                
                                        
                            
                            <div class="col-md-3">
                                <div class="custom_card">
                                    <h4>active trips</h4>
                                    <h1><?php echo e($activeTrips ?? 0); ?></h1>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="custom_card">
                                    <h4>Total trips</h4>
                                    <h1><?php echo e($totalTrips ?? 0); ?></h1>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="custom_card">
                                    <h4>available trips</h4>
                                    <h1><?php echo e($availableTrips ?? 0); ?></h1>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="custom_card">
                                    <h4>My Subscription</h4>
                                    <h1><?php echo e($mySubscription ?? 'No Subscription Found'); ?></h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="card card-bordered custom_cards_design graph_chart">
                        <h2>Trips</h2>
                        <canvas id="line-chart_owner" style="height: 250px"></canvas>
                    </div>
                </div>
                
                
                
                <div class="col-md-12">
                    <div class="custom_table custom_cards_design ">
                        <h2>My Trips</h2>
                        <div class="custom_scroll_tbl">
                            <table class="table without_pagination_tbl datatable">
                                <thead>
                                    <tr>
                                        <th>SR#</th>
                                        <th>Trip Name</th>
                                        <th>URL</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Duration</th>
                                        
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentTrips; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr id="trip-<?php echo e($item->slug); ?>">
                                            <td><?php echo e($loop->iteration); ?></td>
                                            <td><?php echo e($item->name ?? ''); ?></td>
                                            <td><?php echo e(route('trip.itinerary.overview', [$item->company_slug ?? '', $item->url_slug ?? ''])); ?>

                                            </td>
                                            <td><?php echo e($item->start_date ?? ''); ?></td>
                                            <td><?php echo e($item->end_date ?? ''); ?></td>
                                            <td><?php echo e($item->duration ?? ''); ?></td>
                                            
                                            <td>
                                                <span
                                                    class="status-label <?php echo e($item->status === 'active' ? 'success' : 'danger'); ?>">
                                                    <?php echo e(ucfirst($item->status)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="dropdown-toggle" type="button" id="dropdownMenuButton11"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                        <li><a href="<?php echo e(route('trips.show', $item->slug)); ?>"
                                                                class="dropdown-item"><i class="fa-solid fa-eye"></i>
                                                                View</a></li>
                                                        <li><a href="<?php echo e(route('trips.edit', $item->slug)); ?>"
                                                                class="dropdown-item"><i
                                                                    class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                        
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        $(document).ready(function() {
            var dataTable = $('.without_pagination_tbl').DataTable({
                "searching": true,
                "bLengthChange": false,
                "paging": false,
                "info": false,
            });
            $(document).on("input", '.custom_search_box', function() {
                var searchValue = $(this).val();
                dataTable.search(searchValue).draw();
            });
            $(".without_pagination_tbl").DataTable();
        })
    </script>
    
    <script>
        var data = <?php echo json_encode($data, 15, 512) ?>; // Subscription amounts for each month
        var labels = <?php echo json_encode($monthsYear, 15, 512) ?>; // Month-Year labels (e.g., "Jan 2025", "Dec 2024", ...)
        var lineChart = document.getElementById("line-chart_owner").getContext('2d');
        var gradientOne = lineChart.createLinearGradient(0, 0, 0, 600);
        gradientOne.addColorStop(0, 'rgba(34, 128, 194, 0.5)');
        gradientOne.addColorStop(1, 'rgba(255, 255, 255, 0)');

        var datasets = [{
            label: 'Trips',
            data: data,
            borderColor: '#027F8A',
            fill: 'start',
            backgroundColor: gradientOne,
            tension: 0.4,
            pointBackgroundColor: '#1B1732',
            pointHoverBackgroundColor: "#1B1732",
            borderWidth: 2,
            pointRadius: 0,
            pointHoverRadius: 5,

        }];
        var options = {
            borderWidth: 1,
            cubicInterpolationMode: 'monotone',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderWidth: 4,

        };

        new Chart(lineChart, {
            type: 'line',
            data: {
                labels: labels, // Use the dynamic labels (e.g., "Jan 2025", "Dec 2024", ...)
                datasets: datasets
            },
            options: {
                plugins: {
                    legend: {
                        display: false,
                        position: 'bottom',
                        labels: {
                            fontColor: "#4A4A4A",
                            fontSize: 14,
                        },

                        padding: 10,
                        textAlign: 'left',
                    },
                    tooltip: {
                        backgroundColor: '#FAF9F6',
                        usePointStyle: false,
                        intersect: false,
                        mode: 'index',
                        padding: 10,
                        titleColor: 'black', // Set title color to black
                        bodyColor: 'black', // Set body color to black
                        callbacks: {
                            title: function(tooltipItems) {
                                const index = tooltipItems[0].dataIndex;
                                return `${labels[index]}`; // Display Month Year in tooltip
                            },
                            label: function(tooltipItem) {
                                return `Value: ${tooltipItem.raw}`; // Show value in tooltip
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: true
                        },
                    },
                    y: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            display: false
                        }
                    }
                }
            },
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\dashboard_owner_index.blade.php ENDPATH**/ ?>