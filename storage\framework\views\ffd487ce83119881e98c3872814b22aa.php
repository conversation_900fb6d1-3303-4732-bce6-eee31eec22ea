<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <?php if(auth()->user()->hasRole('admin')): ?>
        <section class="homepage_section">
            <div class="container-fluid">
                <div class="row custom_row">
                    <div class="col-md-12">
                        <div class="homepage_cards">
                            <div class="row">
                                <div class="col-md-12">
                                    <h2>Hey Mariana -<span>here’s what’s happening with your store today</span></h2>
                                </div>
                                <div class="col-md-12">
                                    <div class="cards_wrapper">
                                        <div class="custom_card">
                                            <h4>Total Revenue</h4>
                                            <h1>$12,426</h1>
                                        </div>
                                        <div class="custom_card">
                                            <h4>Total Boat Managers</h4>
                                            <h1>500+</h1>
                                        </div>
                                        <div class="custom_card">
                                            <h4>Active Boat Managers</h4>
                                            <h1>84,382</h1>
                                        </div>
                                        <div class="custom_card">
                                            <h4>Total Boat Managers</h4>
                                            <h1>2,426</h1>
                                        </div>
                                        <div class="custom_card">
                                            <h4>Total Subscriptions Purchase</h4>
                                            <h1>$12,426</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="card card-bordered custom_cards_design graph_chart">
                            <h2>Monthly Earnings</h2>
                            <canvas id="line-chart" style="height: 250px"></canvas>
                        </div>

                    </div>
                    
                    
                    
                    <div class="col-md-4">
                        <div class="custom_trip_stats custom_cards_design">
                            <div class="custom_select custom_justify_between">
                                <h2>Stats</h2>
                                <select class="form-select" aria-label="Default select example">
                                    <option selected disabled>Select to Check Stats</option>
                                    <option value="1">Last 7 Days</option>
                                    <option value="2">Last 14 Days</option>
                                    <option value="3">Last 21 Days</option>
                                </select>
                            </div>
                            <div class="stats_progress">
                                <div class="custom_progress">
                                    <div class="stats_detail custom_justify_between">
                                        <h3>Boat Managers</h3>
                                        <h3>1,43,382</h3>
                                    </div>
                                    <div class="progress" role="progressbar" aria-label="Success example" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">
                                        <div class="progress-bar"></div>
                                    </div>
                                </div>
                                <div class="custom_progress">
                                    <div class="stats_detail custom_justify_between">
                                        <h3>Active Boat Managers</h3>
                                        <h3>87,974</h3>
                                    </div>
                                    <div class="progress" role="progressbar" aria-label="Success example" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100">
                                        <div class="progress-bar"></div>
                                    </div>
                                </div>
                                <div class="custom_progress">
                                    <div class="stats_detail custom_justify_between">
                                        <h3>Total Boat Managers</h3>
                                        <h3>45,211</h3>
                                    </div>
                                    <div class="progress" role="progressbar" aria-label="Success example" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100">
                                        <div class="progress-bar"></div>
                                    </div>
                                </div>
                                <div class="custom_progress">
                                    <div class="stats_detail custom_justify_between">
                                        <h3>Total Subscriptions</h3>
                                        <h3>21,893</h3>
                                    </div>
                                    <div class="progress" role="progressbar" aria-label="Success example" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">
                                        <div class="progress-bar"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="custom_table custom_cards_design">
                            <h2>Users</h2>
                            <div class="custom_scroll_tbl">
                                <table class="table without_pagination_tbl datatable">
                                    <thead>
                                    <tr>
                                        <th>Sr#</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Address</th>
                                        <th>Action</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php for($i=0;$i<3;$i++): ?>
                                        <tr>
                                            <td>01</td>
                                            <td>John Doe</td>
                                            <td><EMAIL></td>
                                            <td>123456789</td>
                                            <td>Address Here</td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                        <li><a href="<?php echo e(url('users/3')); ?>" class="dropdown-item"><i class="fa-solid fa-eye"></i>View</a></li>
                                                        <li><a href="#!" class="dropdown-item"><i class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                        <li><a href="#!" class="dropdown-item"><i class="fa-solid fa-close"></i>Deactivate</a></li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endfor; ?>

                                    <?php for($i=0;$i<3;$i++): ?>
                                        <tr>
                                            <td>01</td>
                                            <td>David</td>
                                            <td><EMAIL></td>
                                            <td>123456789</td>
                                            <td>Address Here</td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="dropdown-toggle" type="button" id="dropdownMenuButton11" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                        <li><a href="<?php echo e(url('users/3')); ?>" class="dropdown-item"><i class="fa-solid fa-eye"></i>View</a></li>
                                                        <li><a href="#!" class="dropdown-item"><i class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                        <li><a href="#!" class="dropdown-item"><i class="fa-solid fa-close"></i>Deactivate</a></li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endfor; ?>


                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="homepage_notification custom_cards_design">
                            <div class="navbar_notification_header">
                                <h2>Notifications</h2>
                            </div>
                            <div class="nav_linking">
                                <ul>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/jenny_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Jenny Wilson</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/lane_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Devon Lane</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/russell_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Dianne Russell</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/jenny_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Jenny Wilson</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/lane_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Devon Lane</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/russell_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Dianne Russell</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/russell_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Dianne Russell</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

    <?php elseif(auth()->user()->hasRole('owner')): ?>
        <section class="homepage_section">
            <div class="container-fluid">
                <div class="row custom_row">
                    <div class="col-md-12">
                        <div class="homepage_cards owner_home_pg_cards">
                            <div class="row">
                                <div class="col-md-12 owner_home_pg_cards_col">
                                    <h2>Hey Mariana -<span>here’s what’s happening with your store today</span></h2>
                                </div>
                                <div class="col-md-3">
                                    <div class="custom_card">
                                        <h4>active trips</h4>
                                        <h1><?php echo e($activeTrips??0); ?></h1>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="custom_card">
                                        <h4>Total trips</h4>
                                        <h1><?php echo e($totalTrips??0); ?></h1>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="custom_card">
                                        <h4>available trips</h4>
                                        <h1><?php echo e($availableTrips??0); ?></h1>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="custom_card">
                                        <h4>My Subscription</h4>
                                        <h1><?php echo e($mySubscription ?? 'No Subscription Found'); ?></h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="card card-bordered custom_cards_design graph_chart">
                            <h2>Active Trips</h2>
                            <canvas id="line-chart_owner" style="height: 250px"></canvas>
                        </div>
                    </div>
                    
                    
                    
                    <div class="col-md-8">
                        <div class="custom_table custom_cards_design ">
                            <h2>My Trips</h2>
                            <div class="custom_scroll_tbl">
                                <table class="table without_pagination_tbl datatable">
                                    <thead>
                                        <tr>
                                        <th>SR#</th>
                                            <th>Trip Name</th>
                                            <th>URL</th>
                                            <th>Start Date</th>
                                            <th>End Date</th>
                                            <th>Duration</th>
                                            
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $trips; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr id="trip-<?php echo e($item->slug); ?>">
                                                <td><?php echo e($loop->iteration); ?></td>
                                                <td><?php echo e($item->name ?? ''); ?></td>
                                                <td><?php echo e(route('trip.itinerary.overview', [$item->company_slug ?? '', $item->url_slug ?? ''])); ?>

                                                </td>
                                                <td><?php echo e($item->start_date ?? ''); ?></td>
                                                <td><?php echo e($item->end_date ?? ''); ?></td>
                                                <td><?php echo e($item->duration ?? ''); ?></td>
                                                
                                                <td>
                                                    <span
                                                        class="status-label <?php echo e($item->status === 'active' ? 'success' : 'danger'); ?>">
                                                        <?php echo e(ucfirst($item->status)); ?>

                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="dropdown-toggle" type="button" id="dropdownMenuButton11"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="fa-solid fa-ellipsis"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                            <li><a href="<?php echo e(route('trips.show', $item->slug)); ?>"
                                                                    class="dropdown-item"><i class="fa-solid fa-eye"></i>
                                                                    View</a></li>
                                                            <li><a href="<?php echo e(route('trips.edit', $item->slug)); ?>"
                                                                    class="dropdown-item"><i
                                                                        class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                            <li><a class="dropdown-item toggle-status"
                                                                    data-slug="<?php echo e($item->slug); ?>">
                                                                    <i
                                                                        class="fa-solid <?php echo e($item->status === 'active' ? 'fa-close' : 'fa-check'); ?>"></i>
                                                                    <?php echo e($item->status === 'active' ? 'Deactivate' : 'Activate'); ?></a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="homepage_notification custom_cards_design">
                            <div class="navbar_notification_header">
                                <h2>Notifications</h2>
                            </div>
                            <div class="nav_linking">
                                <ul>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/jenny_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Jenny Wilson</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/lane_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Devon Lane</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/russell_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Dianne Russell</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/jenny_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Jenny Wilson</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/lane_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Devon Lane</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/russell_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Dianne Russell</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="<?php echo e(url('notifications')); ?>" class="navbar_notification" >
                                            <div class="user_profile custom_flex">
                                                <div class="user_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/russell_user.png">
                                                </div>
                                                <div class="user_name">
                                                    <h3>Dianne Russell</h3>
                                                    <h3><EMAIL></h3>
                                                </div>
                                            </div>
                                            <div class="status_time">
                                                <h4>12:00 pm</h4>
                                            </div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        $(document).ready(function() {
            var dataTable = $('.without_pagination_tbl'). DataTable({
                "searching": true,
                "bLengthChange": false,
                "paging": false,
                "info": false,
            });
            $(document).on("input", '.custom_search_box', function () {
                var searchValue = $(this).val();
                dataTable.search(searchValue).draw();
            });
            $(".without_pagination_tbl").DataTable();
        })

    </script>


    <script>
        var lineChart = document.getElementById("line-chart_owner").getContext('2d');
        var gradientOne = lineChart.createLinearGradient(0, 0, 0, 600);
        gradientOne.addColorStop(0, 'rgba(34, 128, 194, 0.5)');
        gradientOne.addColorStop(1, 'rgba(255, 255, 255, 0)');

        var datasets = [
            {
                label:'Admin Commission',
                data: [60, 65, 70, 60, 70, 65, 75, 70, 80, 85, 90, 100],
                borderColor: '#027F8A',
                fill: 'start',
                backgroundColor: gradientOne,
                tension:0.4,
                pointBackgroundColor: '#1B1732',
                pointHoverBackgroundColor: "#1B1732",
                borderWidth:2,
                pointRadius: 0,
                pointHoverRadius: 5,

            }
        ];
        var options = {
            borderWidth: 1,
            cubicInterpolationMode: 'monotone',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderWidth: 4,

        };

        new Chart(lineChart, {
            type: 'line',
            data: {
                labels: [ 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'July', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec','Jan'],
                datasets: datasets
            },
            options: {
                plugins: {
                    legend: {
                        display: false,
                        position: 'bottom',
                        labels: {
                            fontColor: "#4A4A4A",
                            fontSize: 14,
                        },

                        padding:10,
                        textAlign: 'left',
                    },
                    tooltip: {
                        backgroundColor: '#FAF9F6',
                        usePointStyle: false,
                        intersect:false,
                        mode: 'index',
                        padding:10,
                        titleColor: 'black', // Set title color to black
                        bodyColor: 'black',   // Set body color to black
                        callbacks: {
                            title: function(tooltipItems) {
                                const index = tooltipItems[0].dataIndex;
                                const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                                return `${months[index]} 2024`;
                            },
                            label: function(tooltipItem) {
                                return `Value: ${tooltipItem.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: true
                        },
                    },
                    y: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            display: false
                        }
                    }
                }
            },
        });
    </script>

    <script>
        var lineChart = document.getElementById("line-chart").getContext('2d');
        var gradientOne = lineChart.createLinearGradient(0, 0, 0, 600);
        gradientOne.addColorStop(0, 'rgba(34, 128, 194, 0.5)');
        gradientOne.addColorStop(1, 'rgba(255, 255, 255, 0)');

        var datasets = [
            {
                label:'Admin Commission',
                data: [50, 70, 60, 70, 60, 70, 60, 70, 60, 70, 90, 80],
                borderColor: '#027F8A',
                fill: 'start',
                backgroundColor: gradientOne,
                tension:0.4,
                pointBackgroundColor: '#1B1732',
                pointHoverBackgroundColor: "#1B1732",
                borderWidth:2,
                pointRadius: 0,
                pointHoverRadius: 5,

            }
        ];
        var options = {
            borderWidth: 1,
            cubicInterpolationMode: 'monotone',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderWidth: 4,

        };

        new Chart(lineChart, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'July', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec'],
                datasets: datasets
            },
            options: {
                plugins: {
                    legend: {
                        display: false,
                        position: 'bottom',
                        labels: {
                            fontColor: "#4A4A4A",
                            fontSize: 14,
                        },

                        padding:10,
                        textAlign: 'left',
                    },
                    tooltip: {
                        backgroundColor: '#FAF9F6',
                        usePointStyle: false,
                        intersect:false,
                        mode: 'index',
                        padding:10,
                        titleColor: 'black', // Set title color to black
                        bodyColor: 'black',   // Set body color to black
                        callbacks: {
                            title: function(tooltipItems) {
                                const index = tooltipItems[0].dataIndex;
                                const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                                return `${months[index]} 2024`;
                            },
                            label: function(tooltipItem) {
                                return `Value: ${tooltipItem.raw}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: true
                        },
                    },
                    y: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            display: false
                        }
                    }
                }
            },
        });
    </script>

    
    
    
    

    
    
    
    
    

    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    

    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    

    
    
    
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\dashboard_indexOLD.blade.php ENDPATH**/ ?>