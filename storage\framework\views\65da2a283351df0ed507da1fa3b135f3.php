
<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>

    <section class="packages_activities custom_padding">
        <div class="container custom_container">
            <div class="row custom_align_center">
                <div class="col-md-6">
                    <div class="activities_content">
                        <h6>Kicker Heading</h6>
                        <h2>Activities</h2>
                        <h6>We’re more connected than ever.  More powerful, more creative and more aware with how information is shared and distributed.</h6>
                        <div class="activity_listing">
                            <ul>
                                <li><i class="fa-solid fa-arrow-circle-right"></i>Full Trip Planning & Custom Itineraries</li>
                                <li><i class="fa-solid fa-arrow-circle-right"></i>Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)</li>
                                <li><i class="fa-solid fa-arrow-circle-right"></i>Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)</li>
                                <li><i class="fa-solid fa-arrow-circle-right"></i>Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)</li>
                                <li><i class="fa-solid fa-arrow-circle-right"></i>Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)</li>
                                <li><i class="fa-solid fa-arrow-circle-right"></i>Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)</li>
                                <li><i class="fa-solid fa-arrow-circle-right"></i>Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)</li>
                                <li><i class="fa-solid fa-arrow-circle-right"></i>Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)Luxury Add-Ons (Gourmet Catering, Water Sports, etc.)</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="multi_images_section">
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <div class="swiper itenarySwiper">
                                    <div class="swiper-wrapper">
                                        <?php for($i=0;$i<3;$i++): ?>
                                            <div class="swiper-slide">
                                                <div class="slider_images">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/scenery_image.png">
                                                </div>
                                            </div>
                                        <?php endfor; ?>
                                    </div>
                                    <div class="swiper-pagination"></div>
                                </div>
                            </div>
                            <div class="col-md-7">
                                <div class="custom_images">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/activity_image.png">
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div class="custom_images">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/boat_river_img.png">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>


<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\website\TripPackages\VariationTwo\packages_activities_two.blade.php ENDPATH**/ ?>