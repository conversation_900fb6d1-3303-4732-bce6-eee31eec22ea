<?php $__env->startPush('css'); ?>
    <!-- <PERSON> Swiper's CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.css" />
    <style>
        .navbar_header_sec {
            position: fixed;
            width: 100%;
            z-index: 99;
        }

        .navbar_header_sec {
            background: rgba(42, 42, 42, 0.45);
            backdrop-filter: blur(7.5px);
        }

        .hero_sec.services_hero_sec.home_pg_hero_sec {
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.60) 0%, rgba(0, 0, 0, 0.60) 100%), url('<?php echo e(isset($home->image) ? asset('website/' . $home->image) : asset('website/assets/images/hero_image_home_pg.png')); ?>');
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    
    <section class="hero_sec services_hero_sec home_pg_hero_sec">
        <div class="container custom_container_fluid_service_ph">
            <div class="row">
                <div class="col-md-12">
                    <div class="hero_sec_wrapper">
                        <h3><?php echo e($home->kicker ?? ''); ?></h3>
                        <h1><?php echo e($home->heading ?? ''); ?></h1>
                        <h6><?php echo e($home->description ?? ''); ?></h6>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <main class="custom_main_page">
        <section class="our_objective_sec services_objective_sec home_objective_sec">
            <div class="container custom_container_fluid_service_ph">
                <div class="row">
                    <div class="col-md-12 custom_col_home_pg">
                        <div class="custom_flex">
                            <h6><?php echo e($services->service_kicker ?? ''); ?></h6>
                            <h2><?php echo e($services->service_heading ?? ''); ?></h2>
                            <p><?php echo e($services->service_description ?? ''); ?></p>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="swiper mySwiper home_page_slider_swiper">
                            <div class="swiper-wrapper">
                                <?php $__currentLoopData = $services->services_detail; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="swiper-slide">
                                        <div class="slider_cards_wrapper">
                                            <div class="slider_logo_img">
                                                <img
                                                    src="<?php echo e(asset('website/' . ($detail->card_icon ?? 'assets/images/board_logo.svg'))); ?>">
                                            </div>
                                            <p><?php echo e($detail->heading ?? ''); ?></p>
                                            <h6><?php echo e($detail->home_card_description ?? ''); ?></h6>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <div class="swiper-pagination">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>



        
        
        <section class="our_objective_sec services_objective_sec home_objective_sec">
            <div class="container custom_container_fluid_service_ph">
                <div class="row">
                    <div class="col-md-12">
                        <div class="custom_flex">
                            <h6><?php echo e($home->home_kicker ?? ''); ?></h6>
                            <h2><?php echo e($home->home_heading ?? ''); ?></h2>
                            
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="yacht_reg_sec yacht_reg_sec_home">
            <div class="container-fluid custom_container_fluid_service_ph">
                <div class="row custom_row_yacht_reg">
                    <div class="col-md-12 col-xl-8">
                        <div class="images_boards_sec">
                            <div class="row">
                                <div class="col-md-6 col-xl-7 col-sm-6">
                                    <div class="boards_img_wrapper">
                                        <img
                                            src="<?php echo e(asset('website/' . ($home->home_image_one ?? 'assets/images/borders_img_ser.png'))); ?>">
                                    </div>
                                    <div class="boards_inner_img_text">
                                        <h2>10</h2>
                                        <img src="<?php echo e(asset('website')); ?>/assets/images/boads_plus.svg">
                                        <h4>Years Of Experience</h4>
                                    </div>
                                </div>
                                <div class="col-md-6 col-xl-5 col-sm-6 custom_col_image_rotation">
                                    <div class="boards_img_wrapper boards_img_wrapper2">
                                        <img
                                            src="<?php echo e(asset('website/' . ($home->home_image_two ?? 'assets/images/image_web2.png'))); ?>">

                                        <div class="circle_animation_txt">
                                            <a href="#" class="circle_animation_logo">
                                                <div class="circle_animation_logo">
                                                    <i class="fa-solid fa-play"></i>
                                                </div>
                                                <div class="circle_animation_text">
                                                    <p>
                                                        Our Story
                                                    </p>
                                                    <p>
                                                        Watch Video
                                                    </p>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 col-xl-4">
                        <div class="yacht_reg_wrapper">
                            <h3><?php echo e($home->home_sub_heading ?? ''); ?></h3>
                            <p><?php echo e($home->home_description ?? ''); ?></p>
                            <?php if(auth()->guard()->guest()): ?>
                                <a href="<?php echo e(route('register')); ?>" type="button"
                                    class="btn_global yellow_btn arrow_up_right_btn_img"><?php echo e($home->home_button_text ?? ''); ?>

                                    <img
                                        src="https://guesttrip.thebackendprojects.com/website/assets/images/arrow-up-right.svg "></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

            </div>
        </section>

        <section class="footer_sec video_home_sec">
            <div class="container-fluid custom_container_footer">
                <div class="row custom_footer_first_row">
                    <div class="col-md-12 custom_footer_parent_col">
                        <div class="footer_video">
                            <video autoplay muted loop>
                                <source
                                    src="<?php echo e(asset('website/' . ($home->call_video ?? 'assets/images/footer_video.mp4'))); ?>"
                                    type="video/mp4">
                            </video>
                            <div class="footer_wrapper">
                                <div class="background_content">
                                    <div class="row custom_row_footer">
                                        <div class="col-md-12">

                                            <h6><?php echo e($home->call_kicker ?? ''); ?></h6>
                                            <h2><?php echo e($home->call_heading ?? ''); ?></h2>
                                            <p><?php echo e($home->call_description ?? ''); ?></p>
                                            <?php if(auth()->guard()->guest()): ?>
                                                <a href="<?php echo e(route('register')); ?>" type="button"
                                                    class="btn_global yellow_btn arrow_up_right_btn_img"><?php echo e($home->call_button_text ?? ''); ?><img
                                                        src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.png "></a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <section class="contact_us_sec">
            <div class="container-fluid custom_container_fluid_service_ph">
                <div class="row column_reverse">
                    <div class="col-md-6 custom_col_contact_us">
                        <div class="get_in_touch_heading">
                            <h6><?php echo e($contact->form_kicker ?? ''); ?></h6>
                            <h2><?php echo e($contact->form_heading ?? ''); ?></h2>
                            <h6><?php echo e($contact->form_sub_heading ?? ''); ?></h6>
                        </div>
                        <form id="contact_form" action="<?php echo e(route('contactform.store')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="form_contact_us">
                                <div class="row form_contact_us_row">
                                    <div class="col-md-6">
                                        <div class="field_wrapper">
                                            <label for="fname">First name</label>
                                            <input id="fname"
                                                class="form-control <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                type="text" name="first_name" placeholder="First name"
                                                value="<?php echo e(old('first_name')); ?>" <?php if($errors->any()): ?> autofocus <?php endif; ?>>
                                            <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <span class="invalid-feedback"><?php echo e($message); ?></span>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="field_wrapper">
                                            <label for="lname">Last name</label>
                                            <input id="lname"
                                                class="form-control <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" type="text"
                                                name="last_name" placeholder="Last name" value="<?php echo e(old('last_name')); ?>">
                                            <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <span class="invalid-feedback"><?php echo e($message); ?></span>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="field_wrapper">
                                            <label for="email">Email</label>
                                            <input id="email"
                                                class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" type="email"
                                                name="email" placeholder="Email Address" value="<?php echo e(old('email')); ?>">
                                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <span class="invalid-feedback"><?php echo e($message); ?></span>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="field_wrapper">
                                            <label for="phone_number">Phone Number</label>
                                            <input class="form-control phone <?php $__errorArgs = ['phone_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                id="phone_number" type="tel" name="phone_number"
                                                value="<?php echo e(old('phone_number')); ?>" />
                                            <?php $__errorArgs = ['phone_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <span class="invalid-feedback"><?php echo e($message); ?></span>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="field_wrapper">
                                            <label for="message">Message</label>
                                            <textarea rows="5" name="message" id="message" class="form-control <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                placeholder="Type Here ...."><?php echo e(old('message')); ?></textarea>
                                            <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <span class="invalid-feedback"><?php echo e($message); ?></span>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <!-- <div class="col-md-12">
                                                        <div class="checkbox_contact_us_wrapper">
                                                            <input type="checkbox" id="checkbox_contact_us_form">
                                                            <label for="checkbox_contact_us_form">You agree to our friendly privacy policy.</label>
                                                        </div>
                                                    </div> -->
                                    <div class="col-md-12">
                                        <div class="">
                                            <button type="submit"
                                                class="btn_global yellow_btn arrow_up_right_btn_img">Submit
                                                <img
                                                    src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </form>
                    </div>
                    <div class="col-md-6 custom_col_contact_us">
                        <div class="image_contact_wrapper">
                            <img
                                src="<?php echo e(asset('website/' . ($contact->form_image ?? 'assets/images/contact_us_img.png'))); ?>">
                        </div>
                        <div class="social_wrapper_contact_us">
                            <div class="phone_number">
                                <i class="fa-solid fa-phone"></i>
                                <div>
                                    <h6>Phone Number</h6>
                                    <a href="tel:+442086386370">
                                        <h2>+44 2086 386 370</h2>
                                    </a>
                                </div>
                            </div>
                            <div class="phone_number">
                                <i class="fa-regular fa-envelope"></i>
                                <div>
                                    <h6>Email Address</h6>
                                    <a href="mailto:<EMAIL>">
                                        <h2><EMAIL></h2>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <!--begin::How It Works Section-->
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"></script>

    <script>
        $(document).ready(function() {
            const phoneInputField = $('.phone');
            const phoneInput = window.intlTelInput(phoneInputField[0], {
                utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js",
                initialCountry: "us",
                separateDialCode: true,
            });

            phoneInputField.on('countrychange', function() {
                const countryCode = phoneInput.getSelectedCountryData().dialCode;
                phoneInputField.val(`+${countryCode}`);
            })

        });
    </script>

    <script>
        $(document).ready(function() {
            jQuery('#contact_form').validate({
                rules: {
                    first_name: {
                        required: true,
                        minlength: 2
                    },
                    last_name: {
                        required: true,
                        minlength: 2
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    phone_number: {
                        required: true,
                        // digits: true,
                        minlength: 6,
                        maxlength: 15
                    },
                    message: {
                        required: true,
                        minlength: 10
                    }
                },
                messages: {
                    first_name: {
                        required: "Please enter your first name.",
                        minlength: "Your first name must be at least 2 characters."
                    },
                    last_name: {
                        required: "Please enter your last name.",
                        minlength: "Your last name must be at least 2 characters."
                    },
                    email: {
                        required: "Please enter your email.",
                        email: "Please enter a valid email address."
                    },
                    phone_number: {
                        required: "Please enter your phone number.",
                        digits: "Only digits are allowed.",
                        minlength: "Phone number must be at least 6 digits.",
                        maxlength: "Phone number must not exceed 15 digits."
                    },
                    message: {
                        required: "Please enter your message.",
                        minlength: "Your message must be at least 10 characters."
                    }
                },
                errorElement: "span",
                errorClass: "invalid-feedback",
                highlight: function(element, errorClass) {
                    $(element).addClass("is-invalid");
                },
                unhighlight: function(element, errorClass) {
                    $(element).removeClass("is-invalid");
                }
            });
        });
    </script>
    <script>
        // const text = document.querySelector(".text");
        // text.innerHTML = text.innerText
        //     .split("")
        //     .map(
        //         (char, i) => `<span style="transform:rotate(${i * 10.3}deg)">${char}</span>`
        //     )
        //     .join("");


        const texts = document.querySelectorAll(".circle_animation_text");

        texts.forEach((text) => {
            text.innerHTML = text.innerText
                .split("")
                .map(
                    (char, i) => `<span style="transform:rotate(${i * 10.3}deg)">${char}</span>`
                )
                .join("");
        });
    </script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <!-- Initialize Swiper -->
    <script>
        var swiper = new Swiper(".mySwiper", {
            slidesPerView: 4,
            spaceBetween: 30,
            // autoplay: {
            //     delay: 2000,
            // },
            loop: true,
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
            },
            breakpoints: {
                1920: {
                    slidesPerView: 4,
                },
                1600: {
                    slidesPerView: 4,
                },
                1440: {
                    slidesPerView: 4,
                },
                1336: {
                    slidesPerView: 4,
                },
                1200: {
                    slidesPerView: 3,
                },
                1024: {
                    slidesPerView: 3,
                },
                991: {
                    slidesPerView: 3,
                },
                800: {
                    slidesPerView: 3,
                },
                767: {
                    slidesPerView: 2,
                },
                600: {
                    slidesPerView: 2,
                },
                515: {
                    slidesPerView: 2,
                },
                480: {
                    slidesPerView: 2,
                },
                425: {
                    slidesPerView: 1,
                },
                375: {
                    slidesPerView: 1,
                },
                320: {
                    slidesPerView: 1,
                },
            },
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views/website/index.blade.php ENDPATH**/ ?>