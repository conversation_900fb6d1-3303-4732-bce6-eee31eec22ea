

<script>
    $(document).ready(function() {
        $('[data-fancybox="gallery"]').fancybox({
            protect: false,
            clickOutside: false,
            closeExisting: false,
        });

        // read more functionality
        $(".pre_line_description").each(function() {
            var $this = $(this);
            var readMore = $this.next(".read_more");

            // Check if text is overflowing
            if (this.scrollHeight > $this.outerHeight()) {
                readMore.show(); // Only show Read More if text is overflowing
            } else {
                readMore.remove(); // Remove Read More if text fits within 5 lines
            }

            readMore.click(function() {
                if ($this.hasClass("expanded")) {
                    $this.removeClass("expanded").css({
                        display: "-webkit-box",
                        overflow: "hidden",
                        maxHeight: "8em"
                    });
                    $(this).text("Read More");
                } else {
                    $this.addClass("expanded").css({
                        display: "block",
                        overflow: "visible",
                        maxHeight: "none"
                    });
                    $(this).text("Read Less");
                }
            });
        });
    });
</script>
<script>
    var swiper = new Swiper(".itineryCardSwiper", {
        slidesPerView: "<?php echo e($trip->itineraries->count() < 4 ? $trip->itineraries->count() : 4); ?>",
        
        spaceBetween: 30,
        loop: false,
        navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
        },
        breakpoints: {
            1920: {
                slidesPerView: 4,
            },
            1600: {
                slidesPerView: 4,
            },
            1440: {
                slidesPerView: 4,
            },
            1336: {
                slidesPerView: 4,
            },
            1280: {
                slidesPerView: 3,
            },
            1024: {
                slidesPerView: 3,
            },
            991: {
                slidesPerView: 3,
            },
            800: {
                slidesPerView: 3,
            },
            768: {
                slidesPerView: 2,
            },
            767: {
                slidesPerView: 2,
            },
            600: {
                slidesPerView: 2,
            },
            515: {
                slidesPerView: 2,
            },
            474: {
                slidesPerView: 1,
            },
            424: {
                slidesPerView: 1,
            },
            374: {
                slidesPerView: 1,
            },
            320: {
                slidesPerView: 1,
            },
        },
    });

    //        var itenarySwiper = new Swiper(".itenarySwiper", {
    //            slidesPerView: 1,
    //            spaceBetween: 10,
    //            loop: true,
    //            pagination: {
    //                el: ".swiper-pagination",
    //                clickable: true,
    //            },
    //        });
</script>
<script>
    function generatePathCoordinates(itineraries) {
        const pathCoordinates = [];

        if (itineraries && itineraries.length > 0) {
            // Add the start point from the first itinerary
            const firstItinerary = itineraries[0];
            pathCoordinates.push({
                lat: parseFloat(firstItinerary.start_lat),
                lng: parseFloat(firstItinerary.start_lng),
            });

            // Add the end points from all itineraries
            itineraries.forEach(itinerary => {
                pathCoordinates.push({
                    lat: parseFloat(itinerary.end_lat),
                    lng: parseFloat(itinerary.end_lng),
                });
            });
        } else {
            console.error("No itineraries found or data is invalid.");
        }

        return pathCoordinates;
    }

    const itineraries = <?php echo json_encode($trip->itineraries, 15, 512) ?>; // Assuming this is coming from Laravel
    const pathCoordinates = generatePathCoordinates(itineraries);

    function initMap() {
        const map = new google.maps.Map(document.getElementById("map"), {
            center: {
                lat: 24.8607343,
                lng: 67.0011364,
            }, // Center map around Karachi
            zoom: 6,
        });

        // const polyline = new google.maps.Polyline({
        //     path: pathCoordinates,
        //     geodesic: true,
        //     strokeColor: "#FF0000",
        //     strokeOpacity: 1.0,
        //     strokeWeight: 2,
        //     icons: [{
        //         icon: {
        //             path: google.maps.SymbolPath
        //                 .FORWARD_CLOSED_ARROW, // Using built-in arrow symbol
        //             scale: 4, // Adjust the size of the arrow
        //             strokeColor: '#FF0000', // Optional: color of the arrows
        //         },
        //         offset: '0%',
        //         repeat: '10%' // Adjust this value to control how frequently the arrows appear along the polyline
        //     }]
        // });
        // polyline.setMap(map);

        const apiKey = "02d522f701e289110d83898599ed9487"; // Replace with your OpenWeatherMap API key
        const weatherInfoSpan = document.getElementById("weather-info");

        // Function to fetch weather data
        function fetchWeather(location) {
            const url =
                `https://api.openweathermap.org/data/2.5/weather?lat=${location.lat}&lon=${location.lng}&appid=${apiKey}&units=metric`;

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.cod === 200) {
                        const temp = Math.round(data.main.temp); // Round temperature
                        // const temp = data.main.temp.toFixed(1); // Round temperature
                        weatherInfoSpan.textContent = `${temp} ℃`; // Display in <h3>
                    } else {
                        console.error("Error fetching weather data:", data.message);
                        weatherInfoSpan.textContent = "unavailable.";
                    }
                })
                .catch(error => {
                    console.error("Failed to fetch weather data:", error);
                    weatherInfoSpan.textContent = "unavailable.";
                });
        }

        // Show weather for the first coordinate initially
        if (pathCoordinates.length > 0) {
            fetchWeather(pathCoordinates[0]);
        }

        // Add markers for each point
        pathCoordinates.forEach((location, index) => {
            const marker = new google.maps.Marker({
                position: location,
                map: map,
                title: `Location ${index + 1}`,
            });

            // On marker click, fetch and show weather for that location
            marker.addListener("click", () => {
                fetchWeather(location);
            });
        });

        // Adjust map bounds to fit all markers
        const bounds = new google.maps.LatLngBounds();
        pathCoordinates.forEach(loc => bounds.extend(loc));
        map.fitBounds(bounds);
    }
</script>
<!-- Include Google Maps API -->
<script
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAbi9IUF4TBu58oC9iGZexb045rMaQr2AQ&libraries=places&callback=initMap"
    async defer></script>
<?php /**PATH D:\guesttrip\resources\views\website\packages_templates\script\iti.blade.php ENDPATH**/ ?>