<?php $__env->startPush('css'); ?>
    
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <?php if(auth()->user()->hasRole('owner')): ?>
        <section class="subscription_sec">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="subscription_wrapper">
                            <div class="row">













































                                <div class="col-md-12">
                                    <div class="active_subscription custom_cards_design">
                            <h1>My Subscription</h1>
                                        <div class="subscription_detail">
                                            <?php if($activeSubcription): ?>
                                                <h1><?php echo e($activeSubcription->package->name ?? ''); ?></h1>
                                                <h1><?php echo e($activeSubcription->stripe_subscription_id == null ? 'Trial' : ($activeSubcription->package->type == 'monthly' ? 'Monthly' : 'Annuall')); ?> Subscription</h1>
                                            <?php endif; ?>
                                        </div>
                                        <div class="cancel_btn">
                                            <button type="button" class="btn btn_green modify_subs">Modify Subscription</button>
                                            <button type="button" class="btn btn_transparent">Cancel Subscription</button>
                                        </div>
                                        <div class="select_subscription_plan">
                                            <label>Modify Subscription Plan</label>
                                            <select class="form-select" id="package" aria-label="Default select example">
                                                <option selected disabled>Open this select menu</option>
                                                
                                                 <?php if($remainingPackages): ?>
                                                    <?php $__currentLoopData = $remainingPackages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($item->slug); ?>"
                                                            data-amount="<?php echo e($item->amount ?? 0); ?>"
                                                            data-package-id="<?php echo e($item->id); ?>">
                                                            <?php echo e(explode(' ', $item->name)[0] ." ".($item->type == 'monthly' ? 'Monthly' : 'Annually')); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php endif; ?>
                                            </select>
                                            <button type="button" id="subscription-btn" class="btn btn_dark_green">Pay</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="owner_subscription">
                            <div class="row">
                                            <div class="col-md-4">
                                                <div class="basic_packg_sec">
                                                    <div class="numbering"><span>1</span></div>
                                                    <span class="monthly">
                                                        <div class="susbcription_name">
                                                            <h1>BRONZE</h1>
                                                        </div>
                                                        <div class="subscription_listing">
                                                            <ul>
                                                                <li>AI-powered trip creation & customisable itineraries</li>
                                                                <li>2 design templates to choose from</li>
                                                                <li>Itinerary overview and trip detail modules</li>
                                                                <li>Crew profile and management module</li>
                                                                <li>3 itineraries per month</li>
                                                                <li>Auto generated, interactive routes through Google maps api</li>
                                                                <li>Auto generated URL’s, customisable to your trip name</li>
                                                                <li>Free online training</li>
                                                                <li>Worldwide remote support</li>
                                                            </ul>
                                                        </div>
                                                    </span>
                                                    <span class="yearly">
                                                        <div class="susbcription_name">
                                                            <h1>BRONZE</h1>
                                                </div>
                                                        <div class="subscription_listing">
                                                            <ul>
                                                                <li>AI-powered trip creation & customisable itineraries</li>
                                                                <li>2 design templates to choose from</li>
                                                                <li>Itinerary overview and trip detail modules</li>
                                                                <li>Crew profile and management module</li>
                                                                <li>3 itineraries per month</li>
                                                                <li>Auto generated, interactive routes through Google maps api</li>
                                                                <li>Auto generated URL’s, customisable to your trip name</li>
                                                                <li>Free online training</li>
                                                                <li>Worldwide remote support</li>
                                                            </ul>
                                            </div>
                                                    </span>
                                                    <div class="subscription_plans">
                                                        <span class="monthly">
                                                            <div class="package_pricing">
                                                                <h1>£69</h1>
                                                            </div>
                                                        </span>
                                                        <span class="yearly">
                                                            <div class="package_pricing">
                                                                <h1>£749</h1>
                                                            </div>
                                                        </span>
                                                        <div class="custom_switch_toggle">
                                                            <div>
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/file_img_icon.svg">
                                                            </div>
                                                            <div class="form-check form-switch">
                                                                <label class="form-check-label" for="">Monthly</label>
                                                                <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault">
                                                                <label class="form-check-label" for="">Annually</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="basic_packg_sec silver_packg">
                                                    <div class="numbering"><span>2</span></div>
                                                    <span class="monthly">
                                                        <div class="susbcription_name">
                                                            <h1>SILVER</h1>
                                                        </div>
                                                        <div class="subscription_listing">
                                                            <ul>
                                                                <li>AI-powered trip creation & customisable itineraries</li>
                                                                <li>3 design templates to choose from</li>
                                                                <li>Crew profile and management add-on</li>
                                                                <li>Choice of 2 additional modules, choose from:
                                                                    <ol>
                                                                        <li>Menu plan - Daily planning or room service menu</li>
                                                                        <li>Spa menu - Spa treatments offered onboard</li>
                                                                        <li>Trip activities - Daily activities or activities available overview</li>
                                                                        <li>Boat specifications & general arrangement map</li>
                                                                        <li>Safety onboard - Escape plan and safety video</li>
                                                                    </ol>
                                                                </li>
                                                                <li>5 itineraries per month</li>
                                                                <li>Auto generated, interactive routes through Google maps api</li>
                                                                <li>Auto generated URL’s, customisable to your trip name</li>
                                                                <li>Free online training</li>
                                                                <li>Worldwide remote support</li>
                                                            </ul>
                                                        </div>
                                                    </span>
                                                    <span class="yearly">
                                                        <div class="susbcription_name">
                                                            <h1>SILVER</h1>
                                                        </div>
                                                        <div class="subscription_listing">
                                                            <ul>
                                                                <li>AI-powered trip creation & customisable itineraries</li>
                                                                <li>3 design templates to choose from</li>
                                                                <li>Crew profile and management add-on</li>
                                                                <li>Choice of 2 additional modules, choose from:
                                                                    <ol>
                                                                        <li>Menu plan - Daily planning or room service menu</li>
                                                                        <li>Spa menu - Spa treatments offered onboard</li>
                                                                        <li>Trip activities - Daily activities or activities available overview</li>
                                                                        <li>Boat specifications & general arrangement map</li>
                                                                        <li>Safety onboard - Escape plan and safety video</li>
                                                                    </ol>
                                                                </li>
                                                                <li>5 itineraries per month</li>
                                                                <li>Auto generated, interactive routes through Google maps api</li>
                                                                <li>Auto generated URL’s, customisable to your trip name</li>
                                                                <li>Free online training</li>
                                                                <li>Worldwide remote support</li>
                                                            </ul>
                                                        </div>
                                                    </span>
                                                    <div class="subscription_plans">
                                                        <span class="monthly">
                                                            <div class="package_pricing">
                                                                <h1>£99</h1>
                                                            </div>
                                                        </span>
                                                        <span class="yearly">
                                                            <div class="package_pricing">
                                                                <h1>£1069</h1>
                                                            </div>
                                                        </span>
                                                        <div class="custom_switch_toggle">
                                                            <div>
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/file_img_icon.svg">
                                                            </div>
                                                            <div class="form-check form-switch">
                                                                <label class="form-check-label" for="">Monthly</label>
                                                                <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault">
                                                                <label class="form-check-label" for="">Annually</label>
                                        </div>
                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="basic_packg_sec premium_packg">
                                                    <div class="numbering"><span>3</span></div>
                                                    <span class="monthly">
                                                        <div class="susbcription_name">
                                                            <h1>GOLD</h1>
                                                        </div>
                                                        <div class="subscription_listing">
                                                            <ul>
                                                                <li>AI-powered trip creation & customisable itineraries</li>
                                                                <li>5 design templates to choose from</li>
                                                                <li>Crew profile and management add-on</li>
                                                                <li>All additional modules, including:
                                                                    <ol>
                                                                        <li>Menu plan - Daily planning or room service menu</li>
                                                                        <li>Spa menu - Spa treatments offered onboard</li>
                                                                        <li>Trip activities - Daily activities or activities available overview</li>
                                                                        <li>Boat specifications & general arrangement map</li>
                                                                        <li>Safety onboard - Escape plan and safety video</li>
                                                                    </ol>
                                                                </li>
                                                                <li>Unlimited itineraries per month</li>
                                                                <li>Auto generated, interactive routes through Google maps api</li>
                                                                <li>Auto generated URL’s, customisable to your trip name</li>
                                                                <li>Priority support</li>
                                                                <li>Free online training</li>
                                                                <li>Worldwide remote support</li>
                                                            </ul>
                                                        </div>
                                                    </span>
                                                    <span class="yearly">
                                                        <div class="susbcription_name">
                                                            <h1>GOLD</h1>
                                                        </div>
                                                        <div class="subscription_listing">
                                                            <ul>
                                                                <li>AI-powered trip creation & customisable itineraries</li>
                                                                <li>5 design templates to choose from</li>
                                                                <li>Crew profile and management add-on</li>
                                                                <li>All additional modules, including:
                                                                    <ol>
                                                                        <li>Menu plan - Daily planning or room service menu</li>
                                                                        <li>Spa menu - Spa treatments offered onboard</li>
                                                                        <li>Trip activities - Daily activities or activities available overview</li>
                                                                        <li>Boat specifications & general arrangement map</li>
                                                                        <li>Safety onboard - Escape plan and safety video</li>
                                                                    </ol>
                                                                </li>
                                                                <li>Unlimited itineraries per month</li>
                                                                <li>Auto generated, interactive routes through Google maps api</li>
                                                                <li>Auto generated URL’s, customisable to your trip name</li>
                                                                <li>Priority support</li>
                                                                <li>Free online training</li>
                                                                <li>Worldwide remote support</li>
                                                            </ul>
                                                        </div>
                                                    </span>
                                                    <div class="subscription_plans">
                                                        <span class="monthly">
                                                            <div class="package_pricing">
                                                                <h1>£129</h1>
                                                            </div>
                                                        </span>
                                                        <span class="yearly">
                                                            <div class="package_pricing">
                                                                <h1>£1389</h1>
                                                            </div>
                                                        </span>
                                                        <div class="custom_switch_toggle">
                                                            <div>
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/file_img_icon.svg">
                                                            </div>
                                                            <div class="form-check form-switch">
                                                                <label class="form-check-label" for="">Monthly</label>
                                                                <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault">
                                                                <label class="form-check-label" for="">Annually</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        

        <div class="modal fade custom_modal" id="update_subscription" tabindex="-1" aria-labelledby="createModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h1 class="modal-title" id="createModalLabel">Buy a Subscription</h1>
                    </div>
                    <div class="modal-body ">
                        <form id="updateForm">
                            <?php echo csrf_field(); ?>
                            <div class="row custom_row">
                                <?php if($activeSubcription): ?>
                                    <div class="col-md-12">
                                        <div class="txt_field">
                                            <label>Current Subscription Name:</label>
                                            <input type="text" class="form-control"
                                                placeholder="<?php echo e($activeSubcription->package->name ?? ''); ?>" readonly
                                                required>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label for="" class="form-label">Select Package:</label>
                                        
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="txt_field">
                                        <label>Amount:</label>
                                        <input id="amount" type="number" class="form-control" readonly required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn_transparent"
                                        data-bs-dismiss="modal">Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            $('#amount').val($('#package option:selected').data('amount'));
            $('#package').on('change', function() {
                var amount = $('#package option:selected').data('amount');
                $('#amount').val(amount);
                console.log("Selected Package Amount: " + amount);
            });

            $(".owner_subscription .basic_packg_sec .yearly").hide();
            $(".active_subscription .select_subscription_plan").hide();
            $(document).on("change", ".custom_switch_toggle .form-switch input[type=checkbox]", function() {
                if ($(this).is(":checked")) {
                    $(this).closest('.owner_subscription .basic_packg_sec').find('.monthly').hide();
                    $(this).closest('.owner_subscription .basic_packg_sec').find('.yearly').show();
                } else {
                    $(this).closest('.owner_subscription .basic_packg_sec').find('.yearly').hide();
                    $(this).closest('.owner_subscription .basic_packg_sec').find('.monthly').show();
                }
            });

            $(document).on("click",".active_subscription .modify_subs",function (){
                $(".active_subscription .select_subscription_plan").toggle();
            })

            // Enabled Disabled Active Subscription

            // $('.premium_packg .active_btn button')
            //     .prop('disabled', true)
            //     .text('Disabled Gold Subscription');
            //
            // $('.active_btn button').click(function() {
            //     $('.active_btn button')
            //         .prop('disabled', false)
            //         .text(function() {
            //             const plan = $(this).closest('.basic_packg_sec').hasClass('premium_packg') ? 'Gold' :
            //                 $(this).closest('.basic_packg_sec').hasClass('silver_packg') ? 'Silver' : 'Bronze';
            //             return `Enabled ${plan} Subscription`;
            //         });
            //
            //     $(this)
            //         .prop('disabled', true)
            //         .text('Disabled ' + $(this).text().replace('Enabled ', ''));
            // });
        });
    </script>
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        $(document).ready(function() {
            $('#subscription-btn').on('click', function() {
                const packageId = $('#package option:selected').data('package-id');
                gettingSubscription(packageId);
            });
            $('#renew-subscription-btn').on('click', function() {
                const packageId = $(this).data('package-id');
                gettingSubscription(packageId);
            });

            function gettingSubscription(packageId) {
                $.ajax({
                    url: "<?php echo e(route('checkout.session')); ?>",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    contentType: 'application/json',
                    data: JSON.stringify({
                        package_id: packageId,
                        success_url: "<?php echo e(route('packages.index')); ?>",
                        // success_url: "<?php echo e(route('checkout.success')); ?>",
                        cancel_url: "<?php echo e(route('packages.index')); ?>"
                    }),
                    success: function(data) {
                        if (data.url) {
                            window.location.href = data.url; // Redirect to Stripe Checkout
                        } else {
                            alert(data.error || 'Something went wrong.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        alert('An error occurred. Please try again.');
                    }
                });
            }

            // Toggle Subscription on button click
            $("#toggleSubscription").click(function() {
                let button = $(this); // Store reference to button
                let buttonText = button.text().trim(); // Get the current button text

                // Show confirmation dialog
                Swal.fire({
                    title: "Are you sure?",
                    text: `Do you want to ${buttonText.toLowerCase()} your subscription?`,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yes, proceed!",
                    cancelButtonText: "Cancel"
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Proceed with AJAX request
                        $.ajax({
                            url: "<?php echo e(route('toggle.subscription')); ?>",
                            type: "POST",
                            headers: {
                                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                            },
                            success: function(response) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Success!',
                                    text: response.message,
                                    confirmButtonText: 'OK'
                                });

                                // Update button text dynamically
                                if (response.status === "canceling") {
                                    button.text("Resume");
                                } else {
                                    button.text("Cancel");
                                }
                            },
                            error: function(xhr) {
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Oops...',
                                    text: 'An error occurred. Please try again.',
                                    confirmButtonText: 'OK'
                                });
                            }
                        });
                    }
                });
            });

        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\packages\index_owner.blade.php ENDPATH**/ ?>