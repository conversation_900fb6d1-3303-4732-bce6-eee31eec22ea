<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <?php if(auth()->user()->hasRole('admin')): ?>
        <section class="trip_management">
            <div class="container-fluid">
                <div class="row custom_row">
                    <div class="col-md-12">
                        <div class="custom_table custom_cards_design">
                            <h1>Trip Management</h1>
                            <table class="table myTable datatable custom_table_class_edit ">
                                <thead>
                                    <tr>
                                        <th>Sr#</th>
                                        <th>Trip Name</th>
                                        <th>Company Name</th>
                                        <th>URL</th>
                                        <th>Duration</th>
                                        <th>Package Type</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $trips; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr id="trip-<?php echo e($item->slug); ?>">
                                            <td><?php echo e($loop->iteration); ?></td>
                                            <td><?php echo e($item->name ?? ''); ?></td>
                                            <td><?php echo e($item->user->company_name ?? ''); ?></td>
                                            <td><?php echo e(route('trip.itinerary.overview', [$item->company_slug ?? '', $item->url_slug ?? ''])); ?>

                                            </td>
                                            <td><?php echo e($item->duration ?? ''); ?></td>
                                            <td><?php echo e($item->package->name ?? ''); ?></td>
                                            <td>
                                                <span
                                                    class="status-label <?php echo e($item->status === 'active' ? 'success' : 'danger'); ?>">
                                                    <?php echo e(ucfirst($item->status)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="dropdown-toggle" type="button" id="dropdownMenuButton11"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                        <li><a href="<?php echo e(route('trips.show', $item->slug)); ?>"
                                                                class="dropdown-item"><i
                                                                    class="fa-solid fa-eye"></i>View</a></li>
                                                        
                                                        <li><a class="dropdown-item toggle-status"
                                                                data-slug="<?php echo e($item->slug); ?>">
                                                                <i
                                                                    class="fa-solid <?php echo e($item->status === 'active' ? 'fa-close' : 'fa-check'); ?>"></i>
                                                                <?php echo e($item->status === 'active' ? 'Deactivate' : 'Activate'); ?></a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php elseif(auth()->user()->hasRole('owner')): ?>
        <section class="trip_management">
            <div class="container-fluid">
                <div class="row custom_row">
                    <div class="col-md-12">
                        <div class="custom_table custom_cards_design">
                            <div class="create_trip_btn">
                                <h1>Trip Management</h1>
                                <?php if($activeSub && $activeSub->no_of_trips > 0): ?>
                                    <button type="button" data-bs-toggle="modal" data-bs-target="#create_trip_modal"
                                        class="btn btn_dark_green">Create Trip</button>
                                <?php else: ?>
                                    <a href="<?php echo e(route('packages.index')); ?>">Please Upgrade your package</a>
                                <?php endif; ?>
                            </div>
                            <table class="table myTable datatable extra_table_class_trips">
                                <thead>
                                    <tr>
                                        <th>Sr#</th>
                                        <th>Trip Name</th>
                                        <th>URL</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Duration</th>
                                        <th>Package Type</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $trips; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr id="trip-<?php echo e($item->slug); ?>">
                                            <td><?php echo e($loop->iteration); ?></td>
                                            <td><?php echo e($item->name ?? ''); ?></td>
                                            <td><?php echo e(route('trip.itinerary.overview', [$item->company_slug ?? '', $item->url_slug ?? ''])); ?>

                                            </td>
                                            <td><?php echo e($item->start_date ?? ''); ?></td>
                                            <td><?php echo e($item->end_date ?? ''); ?></td>
                                            <td><?php echo e($item->duration ?? ''); ?></td>
                                            <td><?php echo e($item->package->name ?? ''); ?></td>
                                            <td>
                                                <span
                                                    class="status-label <?php echo e($item->status === 'active' ? 'success' : 'danger'); ?>">
                                                    <?php echo e(ucfirst($item->status)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="dropdown-toggle" type="button" id="dropdownMenuButton11"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="fa-solid fa-ellipsis"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                                        <li><a href="<?php echo e(route('trips.show', $item->slug)); ?>"
                                                                class="dropdown-item"><i class="fa-solid fa-eye"></i>
                                                                View</a></li>
                                                        <li><a href="<?php echo e(route('trips.edit', $item->slug)); ?>"
                                                                class="dropdown-item"><i
                                                                    class="fa-solid fa-pen-to-square"></i>Edit</a></li>
                                                        <li><a class="dropdown-item toggle-status"
                                                                data-slug="<?php echo e($item->slug); ?>">
                                                                <i
                                                                    class="fa-solid <?php echo e($item->status === 'active' ? 'fa-close' : 'fa-check'); ?>"></i>
                                                                <?php echo e($item->status === 'active' ? 'Deactivate' : 'Activate'); ?></a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
    <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\trips\index.blade.php ENDPATH**/ ?>