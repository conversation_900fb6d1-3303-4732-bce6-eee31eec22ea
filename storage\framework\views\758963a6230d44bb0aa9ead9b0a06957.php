<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    
    <section class=" menu_plan_sec ">
        <div class="container custom_container">
            <div class="row">
                <div class="col-md-12">
                    <div class="crew_members_wrapper">
                        <?php if(isset($trip->menu)): ?>
                            <h6><?php echo e($trip->menu->kicker ?? ''); ?></h6>
                            <h2><?php echo e($trip->menu->heading ?? ''); ?></h2>
                            <h4><?php echo e($trip->menu->sub_heading ?? ''); ?></h4>
                        <?php endif; ?>
                        <ul class="nav nav-tabs" id="myTab" role="tablist">
                            
                            <?php if(isset($trip->menuPlans)): ?>
                                <?php $__currentLoopData = $trip->menuPlans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menuPlan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link <?php echo e($loop->first ? 'active' : ''); ?>"
                                            id="breakfast-tab<?php echo e($menuPlan->id); ?>" data-bs-toggle="tab"
                                            data-bs-target="#breakfast<?php echo e($menuPlan->id); ?>" type="button" role="tab"
                                            aria-controls="breakfast<?php echo e($menuPlan->id); ?>"
                                            aria-selected="true"><?php echo e($menuPlan->name ?? ''); ?></button>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                            
                        </ul>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="menu_plan_tabs_cont-wrap">
                        <div class="tab-content" id="myTabContent">
                            <?php if(isset($trip->menuPlans)): ?>
                                <?php $__currentLoopData = $trip->menuPlans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menuPlan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="tab-pane fade show <?php echo e($loop->first ? 'active' : ''); ?> break_fast_wrapper"
                                        id="breakfast<?php echo e($menuPlan->id); ?>" role="tabpanel" aria-labelledby="home-tab">
                                        <?php if(isset($menuPlan->details)): ?>
                                            <?php $__currentLoopData = $menuPlan->details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="tabs_content_wrapper">
                                                    <div class="img_tabs_wrap">
                                                        <?php if(isset($detail->image)): ?>
                                                            <img src="<?php echo e(asset('website/' . $detail->image)); ?>" alt="Image">
                                                        <?php else: ?>
                                                            <img src="<?php echo e(asset('website')); ?>/assets/images/menus_img.png">
                                                        <?php endif; ?>

                                                    </div>
                                                    <div class="menu_plan_tabs_content">
                                                        <h2><?php echo e($detail->title??''); ?></h2>
                                                        <h4><?php echo e($detail->text??''); ?></h4>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master_trip', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\website\packages_templates\VariationOne\menu_plan.blade.php ENDPATH**/ ?>