<script>
    function initAutocomplete() {
        const startInput = document.querySelector('.input_start_location');
        const endInput = document.querySelector('.input_end_location');
        const stopInputs = document.querySelectorAll('.trip_stop_input');

        if (startInput && !startInput.dataset.gmapsAttached) {
            new google.maps.places.Autocomplete(startInput);
            startInput.dataset.gmapsAttached = true;
        }

        if (endInput && !endInput.dataset.gmapsAttached) {
            new google.maps.places.Autocomplete(endInput);
            endInput.dataset.gmapsAttached = true;
        }

        stopInputs.forEach(function(input) {
            if (!input.dataset.gmapsAttached) {
                new google.maps.places.Autocomplete(input);
                input.dataset.gmapsAttached = true;
            }
        });
    }
    $('#create_trip_ai').on('shown.bs.modal', function () {
        initAutocomplete();
    });


    $(document).ready(function() {
        $(".close").click(function() {
            $(this).closest(".modal").hide(); // Find the closest modal and hide it
            location.reload();
        });

        // Button 1: Submit form and reload page
        $('#submitButton1').on('click', function() {
            showLoader();
            var $button = $(this);
            $button.prop('disabled', true);
            var formData = $('#trip-create-form').serialize();

            $.ajax({
                url: "<?php echo e(route('trips.store')); ?>", // Your route for submission
                method: 'POST',
                data: formData,
                success: function(response) {
                    Swal.fire({
                        title: 'Success!',
                        text: response.message,
                        icon: 'success',
                    });
                    location.reload(); // Reload the page after successful submission
                },
                error: function(xhr) {
                    console.log(xhr);

                    // Handle validation errors specifically
                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        let errorMessages = '';
                        $.each(xhr.responseJSON.errors, function(key, value) {
                            errorMessages += value[0] + '<br>';
                        });

                        // Show SweetAlert error with multiple validation errors
                        Swal.fire({
                            title: 'Validation Error!',
                            html: errorMessages,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        let errorMessage = "An error occurred. Please try again.";
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        // Show SweetAlert error
                        Swal.fire({
                            title: 'Error!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                complete: function() {
                    hideLoader();
                    $button.prop('disabled', false);
                }
            });
        });

        // Button 2: Submit form via AJAX and open modal
        $('#submitButton2').on('click', function() {
            showLoader();
            var $button = $(this);
            $button.prop('disabled', true);
            var formData = $('#trip-create-form').serialize();
            $.ajax({
                url: "<?php echo e(route('trips.store')); ?>", // Your route for submission
                method: 'POST',
                data: formData,
                success: function(response) {
                    if (response.status == 'success') {
                        Swal.fire({
                            title: 'Success!',
                            text: response.message,
                            icon: 'success',
                        });
                        $('#trip_slug').val(response.trip.slug);
                        $('#trip_duration').val(response.trip.duration);
                        $('#create_trip_modal').modal('hide'); // Show the modal
                        $('#trip-create-form')[0].reset();
                        $('#create_trip_ai').modal('show'); // Show the modal
                    }
                },
                error: function(xhr) {
                    console.log(xhr);

                    // Handle validation errors specifically
                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        let errorMessages = '';
                        $.each(xhr.responseJSON.errors, function(key, value) {
                            errorMessages += value[0] + '<br>';
                        });

                        // Show SweetAlert error with multiple validation errors
                        Swal.fire({
                            title: 'Validation Error!',
                            html: errorMessages,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        let errorMessage = "An error occurred. Please try again.";
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        // Show SweetAlert error
                        Swal.fire({
                            title: 'Error!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                complete: function() {
                    hideLoader();
                    $button.prop('disabled', false);
                }
            });
        });

        $('#create_trip_ai').on('hidden.bs.modal', function() {
            $('#trip-itinerarie-generate')[0].reset();
        });
        $('#create_trip_modal').on('hidden.bs.modal', function() {
            $('#trip-create-form')[0].reset();
        });

        $('#generate-itineraries').on('click', function() {
            showLoader();
            var $button = $(this);
            $button.prop('disabled', true);
            var formData = $('#trip-itinerarie-generate').serialize();

            $.ajax({
                url: "<?php echo e(route('trips.itineraries.generate')); ?>", // Your route for submission
                method: 'POST',
                data: formData,
                success: function(response) {
                    if (response.status) {
                        let itineraries = response.data;
                        let slug = response.slug;
                        let itineraryHtml = '';
                        let overviewHtml = `
                    <div class="itinerary-item">
                        <p><strong>Trip Overview</strong></p>
                        <p>${response.tripOverview}</p>
                    </div>
                `;

                        // Loop through and render all itineraries
                        itineraries.forEach((itinerary, index) => {
                            itineraryHtml += `
                        <div class="itinerary-item" data-itinerary-number="${itinerary.itinerary_number}">
                            <p><strong>Start:</strong> ${itinerary.start_point} → <strong>End:</strong> ${itinerary.end_point}</p>
                            <p><strong>Description:</strong> ${itinerary.description}</p>
                            <button class="btn btn_transparent regenerate-itinerary" data-itinerary-slug="${slug}" data-itinerary-number="${itinerary.itinerary_number}">Regenerate</button>
                        </div>
                    `;
                        });

                        $('#itineraries-modal .modal-body .itinerary-overview').html(
                            overviewHtml);
                        $('#itineraries-modal .modal-body .itineraries-description').html(
                            itineraryHtml);
                        $('#create_trip_ai').modal('hide');
                        $('#itineraries-modal').modal('show');
                        $('#proceed-itineraries').data('itineraries', itineraries).data(
                            'slug', slug);
                        Swal.fire('Success!', 'Itinerary generated successfully.',
                            'success');
                    }
                },
                error: function(xhr) {
                    let errorMessage = "An error occurred. Please try again.";
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    Swal.fire({
                        title: 'Error!',
                        text: errorMessage,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                },
                complete: function() {
                    hideLoader();
                    $button.prop('disabled', false);
                }
            });
        });

        $(document).on('click', '.regenerate-itinerary', function() {
            showLoader();
            var itineraryNumber = $(this).data('itinerary-number');
            var slug = $(this).data('itinerary-slug');

            // Get the previously generated itinerary data (from the modal or global data storage)
            var itineraries = $('#proceed-itineraries').data('itineraries');
            var previousResponse = ''; // Generate the full previous itinerary response

            // Loop through itineraries to construct the full previousResponse text
            itineraries.forEach(function(itinerary, index) {
                previousResponse += `**Itinerary ${index + 1}**\n`;
                previousResponse += `- **Start Location:** ${itinerary.start_point}\n`;
                previousResponse += `- **End Location:** ${itinerary.end_point}\n`;
                previousResponse += `- **Description:** ${itinerary.description}\n\n`;
            });

            $.ajax({
                url: "<?php echo e(route('trips.itineraries.regenerate')); ?>", // Your route for regenerating a single itinerary
                method: 'POST',
                data: {
                    _token: "<?php echo e(csrf_token()); ?>",
                    slug: slug,
                    itinerary_number: itineraryNumber,
                    previousResponse: previousResponse, // Send previous itinerary data
                },
                success: function(response) {
                    if (response.status) {
                        let regeneratedItinerary = response.data[itineraryNumber -
                            1]; // Assuming only one itinerary is returned
                        // Loop through itineraries and update the description for the specific itinerary
                        itineraries.forEach(function(itinerary) {
                            console.log('Checking itinerary:',
                                itinerary
                            ); // Log each itinerary to see if it matches
                            if (itinerary.itinerary_number == itineraryNumber) {
                                itinerary.description = regeneratedItinerary
                                    .description; // Update the description
                                // console.log('Updated Itinerary:', itinerary); // Log the updated itinerary
                            }
                        });
                        let itineraryHtml = `
                                <div class="itinerary-item" data-itinerary-number="${regeneratedItinerary.itinerary_number}">
                                    <p><strong>Start:</strong> ${regeneratedItinerary.start_point} → <strong>End:</strong> ${regeneratedItinerary.end_point}</p>
                                    <p><strong>Description:</strong> ${regeneratedItinerary.description}</p>
                                    <button class="btn btn_transparent regenerate-itinerary" data-itinerary-slug="${slug}" data-itinerary-number="${regeneratedItinerary.itinerary_number}">Regenerate</button>
                                </div>
                            `;

                        // Update the specific itinerary in the modal
                        $('#itineraries-modal .modal-body .itineraries-description .itinerary-item[data-itinerary-number="' +
                                itineraryNumber + '"]')
                            .replaceWith(itineraryHtml);

                        // Update the data for the proceed button
                        $('#proceed-itineraries').data('itineraries', itineraries);

                        Swal.fire('Success!', 'Itinerary regenerated successfully.',
                            'success');
                    }
                },
                error: function(xhr) {
                    let errorMessage = "An error occurred. Please try again.";
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    Swal.fire({
                        title: 'Error!',
                        text: errorMessage,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                },
                complete: function() {
                    hideLoader();
                }
            });
        });


        $('#proceed-itineraries').on('click', function() {
            showLoader();
            var itineraries = $(this).data('itineraries');
            var slug = $(this).data('slug');

            $.ajax({
                url: "<?php echo e(route('trips.itineraries.save')); ?>",
                method: 'POST',
                data: {
                    _token: "<?php echo e(csrf_token()); ?>",
                    slug: slug,
                    itineraries: itineraries
                },
                success: function(response) {
                    if (response.status) {
                        Swal.fire('Success!', response.message, 'success');
                        window.location.href = response.url;
                    } else {
                        alert(response.message);
                    }
                },
                complete: hideLoader,
            });
        });

        function showLoader() {
            document.getElementById("loading").style.visibility = "visible";
        }

        function hideLoader() {
            document.getElementById("loading").style.visibility = "hidden";
        }


        // <?php if($errors->any()): ?>
        //     $('#create_trip_modal').modal('show');
        // <?php endif; ?>

        $('.toggle-status').on('click', function() {
            let button = $(this);
            let tripSlug = button.data('slug');
            let row = $(`#trip-${tripSlug}`);

            $.ajax({
                url: "<?php echo e(url('trips')); ?>" + `/${tripSlug}/toggle-status`,
                type: 'GET',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                },
                success: function(response) {
                    if (response.status === 'success') {
                        // Update the button text and status
                        let newStatus = response.new_status;
                        button.html(
                            `<i class="fa-solid ${
                    newStatus === 'active' ? 'fa-close' : 'fa-check'
                }"></i> ${
                    newStatus === 'active' ? 'Deactivate' : 'Activate'
                }`
                        );
                        button.data('status', newStatus);

                        // Update the status label in the row
                        let statusLabel = row.find('.status-label');
                        statusLabel
                            .text(newStatus.charAt(0).toUpperCase() + newStatus.slice(
                                1)) // Capitalize the status
                            .removeClass('success danger')
                            .addClass(newStatus === 'active' ? 'success' : 'danger');

                        // Show SweetAlert success message
                        Swal.fire({
                            title: 'Success!',
                            text: `Trip ${newStatus === 'active' ? 'Activated' : 'Deactivated'} successfully!`,
                            icon: 'success',
                            confirmButtonText: 'OK',
                        });
                    } else {
                        // Show SweetAlert error message
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to update status. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK',
                        });
                    }
                },
                error: function() {
                    // Show SweetAlert error message
                    Swal.fire({
                        title: 'Error!',
                        text: 'An error occurred. Please try again.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                    });
                },
            });
        });

    });
</script>
<script>
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const durationInput = document.getElementById('duration');

    // Set the minimum date as today's date
    const today = new Date().toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format
    startDateInput.setAttribute('min', today);
    endDateInput.setAttribute('min', today);

    function calculateDuration() {
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);

        if (startDate && endDate && endDate >= startDate) {
            const timeDiff = endDate - startDate;
            const daysDiff = timeDiff / (1000 * 3600 * 24); // Calculate the difference in days
            durationInput.value = `${daysDiff}`;
        } else if (endDate && endDate < startDate) {
            alert('End Date cannot be earlier than Start Date.');
            endDateInput.value = ''; // Clear the invalid End Date input
            durationInput.value = ''; // Clear the duration
        }
    }

    startDateInput.addEventListener('change', calculateDuration);
    endDateInput.addEventListener('change', calculateDuration);
</script>

<script>
    $(document).ready(function() {
        // Adding new trip stop
        $(document).on('click', '.ai_trip_stop_append_btn', function() {
            // Find the current number of trip stops
            var tripStopCount = $(this).closest('.stop_whole_wrap').find('.ai_trip_stop_append_wrapper')
                .length + 1;

            // Append a new stop block with the correct number
                var newStopHtml = `
                    <div class="ai_trip_stop_append_wrapper">
                        <div class="txt_field">
                            <label>Trip Stop ${tripStopCount}:</label>
                    <input type="text" name="locations[]" value="" class="form-control trip_stop_input" placeholder="">
                        </div>
                        <button type="button" class="ai_trip_stop_delete delete_stop_all_styling"><i class="fa-solid fa-trash"></i></button>
                    </div>
    `;

                var $newField = $(newStopHtml).appendTo($(this).closest('.stop_whole_wrap'));
                var input = $newField.find('.trip_stop_input')[0];
                if (input && !input.dataset.gmapsAttached) {
                    new google.maps.places.Autocomplete(input);
                    input.dataset.gmapsAttached = true;
                }
        });

        // Event delegation for deleting trip stops
        $(document).on('click', '.ai_trip_stop_delete', function() {
            $(this).closest('.ai_trip_stop_append_wrapper').remove();
            updateTripValues(); // Update values after deletion
        });

        function updateTripValues() {
            var inputStartLocation = $('.input_start_location').val().trim();
            var inputEndLocation = $('.input_end_location').val().trim();

            // Check if Start Location, End Location, and at least one Trip Stop is filled
            if (inputStartLocation && inputEndLocation) {
                var tripStopOffValues = [];
                $('.ai_trip_stop_append_wrapper .trip_stop_input').each(function() {
                    var tripStopValue = $(this).val().trim();
                    if (tripStopValue) {
                        tripStopOffValues.push(tripStopValue);
                    }
                });

                if (tripStopOffValues.length === 0) {
                    alert("Please add at least one trip stop.");
                    return;
                }

                // Show the trip details column
                $('.my_trip_custom_column').show();

                // Update start and end locations
                $('.my_trip_box.start h2').text(inputStartLocation);
                $('.my_trip_box.end h2').text(inputEndLocation);

                // Clear previous stops before adding new ones
                $('.my_trip_box.stop_location').remove();

                var lastInsertedElement = $('.my_trip_box.start'); // Start inserting stops after this element

                // Append stops in the correct order
                tripStopOffValues.forEach(function(stop) {
                    var newStopDiv = $(`
                <div class="my_trip_box stop_location">
                    <h2>${stop}</h2>
                </div>
            `);
                    lastInsertedElement = newStopDiv.insertAfter(lastInsertedElement);
                });
            }
        } // ✅ Fixed: Closing bracket was missing here

        // On button click to finalize the trip and show the values
        $('.finish_trip').on('click', function() {
            updateTripValues(); // Update trip values when button is clicked
        });
    });
</script>
<?php /**PATH D:\guesttrip\resources\views\trips\trip_scripts.blade.php ENDPATH**/ ?>