
<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    
    <section class="hero_section">
        <div class="container custom_container">
            <div class="row">
                <div class="col-md-6">
                    <div class="hero_content">
                        <h6>One Week Itinerary for our Our VIP Client</h6>
                        <h1>Discover Thailand</h1>
                        <h4>From <span>23 Jan 2023</span> to <span>30 Jan 2023</span> on board <span>SUPER YACHT</span></h4>
                        <h5>We’re more connected than ever. More powerful, more creative and more aware with how information is shared and distributed. Yacht IQ builds on years of inventive solutions to bring you a simple yet sophisticated set of applications to help keep you informed and connected while onboard. With over 13 years of experience delivering cutting-edge IT solutions and delivering custom software solutions to UHNW clients, Yacht-IQ is what you need</h5>
                    </div>

                </div>
                <div class="col-md-6">
                    <div class="multi_images_section">
                        <div class="row custom_row">
                            <div class="col-md-7">
                                <div class="custom_images">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/custom_ship_img.png">
                                </div>
                            </div>
                            <div class="col-md-5">
                                <div class="custom_images">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/ship_sky_img.png">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="swiper itenarySwiper">
                                    <div class="swiper-wrapper">
                                        <?php for($i=0;$i<3;$i++): ?>
                                            <div class="swiper-slide">
                                                <div class="slider_images">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/mountain_img.png">
                                                </div>
                                            </div>
                                        <?php endfor; ?>
                                    </div>
                                    <div class="swiper-pagination"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="itinerary_summary custom_padding margin_top_bottom_image">
        <div class="container custom_container">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_image_card_content">
                        <h6>Kicker Heading</h6>
                        <h2>Itinerary Summary</h2>
                        <h6>We’re more connected than ever. More powerful, more creative and more aware with how information is shared and distributed.</h6>
                        <div class="swiper crewCardSwiper">
                            <div class="swiper-wrapper">
                                <?php for($i=0;$i<5;$i++): ?>
                                    <div class="swiper-slide">
                                        <div class="custom_img_cards">
                                            <div class="custom_card">
                                                <div class="card_img">
                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/mountain_img.png">
                                                </div>
                                                <div class="card_content">
                                                    <h3>Day 01.</h3>
                                                    <h4>Phuket - Phang Nga Bay</h4>
                                                    <h6>After embarking M/Y Super Yacht in Phuket, we set sail at 1pm for the Malacca Strait which is home to one of Thailand’s most famous tourist spots - Phang Nga Bay.</h6>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="google_map_sec">
        <div class="section_inner_wrapper">
            <div class="container custom_container">
                <div class="row custom_row">
                    <div class="col-md-12">
                        <div class="custom_image_card_content">
                            <h6>Kicker Heading</h6>
                            <h2>Itinerary Map</h2>
                            <h6>We’re more connected than ever.  More powerful, more creative and more aware with how information is shared and distributed.</h6>
                        </div>
                    </div>
                    <div class="col-md-10">
                        <div class="google_map_wrapper">
                            <div style="" class="map_class" id = "map"></div>
                            <div class="google_map_modal" id = "modals"></div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="dist_time_whea_wrapper">
                            <div class="google_distance_wrapper">
                                <i class="fa-solid fa-location-arrow"></i>
                                <div class="">
                                    <h5>Distance</h5>
                                    <h3>300 KM</h3>
                                </div>
                            </div>
                            <div class="google_distance_wrapper">
                                <i class="fa-regular fa-clock"></i>
                                <div class="">
                                    <h5>Time</h5>
                                    <h3>2h 15m</h3>
                                </div>
                            </div>
                            <div class="google_distance_wrapper">
                                <i class="fa-solid fa-cloud"></i>
                                <div class="">
                                    <h5>Weather</h5>
                                    <h3>32 ℃</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="image_fixed_sec" style="">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="image_fixed_wrapper" style="">
                        <img src="https://guesttrip.thebackendprojects.com/website/assets/images/section_img.png" style="">
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>

<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyD9MOeWzhcw1wh6TclwLR4ASVhkSdmAQ-k&libraries=geometry,places"></script>
<script>
    function mainFunc(infoArray, boundary){
        const locationsArray = [];
        let htmlString = "";

        infoArray.forEach((business, index) => {
            htmlString += modalGenerator(business, index);
        const lat = business.Lat !== "" ? business.Lat : 53.756439;
        const lng = business.Lng !== "" ? business.Lng : -2.383170;
        locationsArray.push(["businessModal" + index, { lng, lat }]);
    });

        document.getElementById("modals").innerHTML = htmlString;
        renderMap(locationsArray, boundary);
    }
    function modalGenerator(business, index) {
        let status2019 = business.Sponsor2019 === "Yes" ? "Sponsor" : "Finalist";
        if (business.Finalist2019 === "Yes") {
            status2019 += " & Finalist";
        }
        status2019 += " 2019";

        let modalString = `
    <div class='modal fade' id='businessModal${index}' tabindex='-1' role='dialog' aria-labelledby='exampleModalLabel' aria-hidden='true'>
        <div class='modal-dialog' role='document'>
            <div class='modal-content'>
                <div class='modal-header'>
                    <h3 class='modal-title'>${business.Name} - ${status2019}</h3>
                </div>
                <div class='modal-body'>
                    <img src='${business.Image}' alt='${business.Name} Image' style='width: 100%; height: auto; margin-bottom: 15px;' />
                    <p><b>What They Do</b><br>${business.WhatTheyDo}</p>
                    <hr><p>
                        ${business.Postcode && business.Postcode !== "N/A" ? `<b>Address</b>: ${business.Address}, ${business.Town}, ${business.Postcode}<br>` : ""}
                        ${business.Website && business.Website !== "N/A" ? `<b>Website</b>: <a href='${business.Website}'>${business.Website}</a><br>` : ""}
                        ${business.Contact && business.Contact !== "N/A" ? `<b>Contact</b>: ${business.Contact}<br>` : ""}
                    </p>
                </div>
                <div class='modal-footer'>
                    <div class='mr-auto'>
                        ${business.Facebook !== "N/A" ? `<a href='https://www.facebook.com/${business.Facebook}' class='fa fa-facebook'></a>` : ""}
                        ${business.Twitter !== "N/A" ? `<a href='https://twitter.com/${business.Twitter}' class='fa fa-twitter'></a>` : ""}
                        ${business.Email !== "N/A" ? `<a href='mailto:${business.Email}' class='fa fa-envelope'></a>` : ""}
                    </div>
                    <button type='button' class='btn btn-secondary' data-dismiss='modal'>Close</button>
                </div>
            </div>
        </div>
    </div>`;
        return modalString;
    }
    function renderMap(businessLocations, boundary){
        const mapCenter = { lng: -2.383170, lat: 53.756439 };
        const map = new google.maps.Map(document.getElementById('map'), {
            center: new google.maps.LatLng(mapCenter.lat, mapCenter.lng),
            zoom: 12,
            mapTypeId: 'terrain'
        });

        new google.maps.Polygon({
            paths: boundary,
            strokeColor: '#69207f',
            strokeOpacity: 1,
            strokeWeight: 3,
            fillColor: '#69207f',
            fillOpacity: 0.35,
            map: map
        });

        const image = "https://i.imgur.com/C7gyw7N.png";

        businessLocations.forEach(([modalId, position]) => {
            const marker = new google.maps.Marker({
                position,
                map,
                icon: image
            });
        marker.addListener("click", () => {
            document.getElementById(modalId).modal('show');
    });
    });
    }



    const businessInfo = [
        {
            "Name": "Sundown Solutions",
            "Address": "Moorside House",
            "Town": "Altham",
            "Postcode": "BB5 5TZ",
            "Lat": 53.77868,
            "Lng": -2.36916,
            "Website": "www.sundownsolutions.co.uk",
            "Facebook": "sundownsolutions",
            "Twitter": "sundownsolutionsltd",
            "Sponsor2019": "Yes",
            "Finalist2019": "Yes",
            "Contact": "aaa",
            "Email": "a.a@a.a",
            "Phone": "0000 000 0000",
            "WhatTheyDo": "Innovative digital solutions that transform the service efficiency of public and private sector organisations",
            "Image": "https://yourwebsite.com/images/sundown-solutions.jpg" // Replace with an actual image URL
        },
        {
            "Name": "aaa",
            "Address": "aaa",
            "Town": "aaa",
            "Postcode": "aa1 1aa",
            "Lat": 53.762,
            "Lng": -2.35,
            "Website": "",
            "Facebook": "",
            "Twitter": "",
            "Sponsor2019": "No",
            "Finalist2019": "Yes",
            "Contact": "aaa",
            "Email": "a.a@a.a",
            "Phone": "0000 000 0000",
            "WhatTheyDo": "Placeholder",
            "Image": "https://yourwebsite.com/images/placeholder.jpg" // Add the image URL
        }
    ];

    const boundaryJson = [
        { lng: -2.355285, lat: 53.722155 },
        { lng: -2.358582, lat: 53.718515 },
        //... (rest of boundary points)
    ];

    mainFunc(businessInfo, boundaryJson);

</script>


<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\website\TripPackages\VariationFour\itinerary_overview_four.blade.php ENDPATH**/ ?>