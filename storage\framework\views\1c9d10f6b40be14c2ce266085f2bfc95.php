<?php $__env->startPush('css'); ?>
    <style>
        .hero_sec {
            background: linear-gradient(0deg, rgba(0, 0, 0, 0.60) 0%, rgba(0, 0, 0, 0.60) 100%), url('<?php echo e(isset($pricing->image) ? asset('website/' . $pricing->image) : asset('/website/assets/images/company_hero_sec_img.png')); ?>');
            background-repeat: no-repeat;
            background-size: cover;
            background-position: 25% 75%;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    
    <section class="hero_sec services_hero_sec">
        <div class="container custom_container">
            <div class="row">
                <div class="col-md-12">
                    <div class="hero_sec_wrapper">
                        <h1><?php echo e($pricing->heading ?? ''); ?></h1>
                        <h6><?php echo e($pricing->description ?? ''); ?></h6>
                    </div>
                </div>
            </div>
        </div>
    </section>

    
    <section class="our_objective_sec services_objective_sec pricing_details">
        <div class="container custom_container">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_flex flexible_partner_pricing_pg">
                        <h6><?php echo e($pricing->pricing_kicker ?? ''); ?></h6>
                        <h2><?php echo e($pricing->pricing_heading ?? ''); ?></h2>
                        <p><?php echo e($pricing->pricing_description ?? ''); ?></p>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="pricing_box_card">
                        <?php if(auth()->guard()->guest()): ?>
                            <a href="<?php echo e(route('register')); ?>" class="btn_global yellow_btn arrow_up_right_btn_img">7
                                Days Free
                                Trial<img
                                    src="https://guesttrip.thebackendprojects.com/website/assets/images/arrow-up-right.svg "></a>
                        <?php else: ?>
                            <?php if(!$userSubscription): ?>
                                <form method="POST" action="<?php echo e(route('trial.subscription')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="btn_global yellow_btn arrow_up_right_btn_img">7 Days Free
                                        Trial<img
                                            src="https://guesttrip.thebackendprojects.com/website/assets/images/arrow-up-right.svg "></button>
                                </form>
                            <?php endif; ?>
                        <?php endif; ?>
                        
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="basic_packg_sec">
                        <div class="custom_switch_toggle">
                            <div>
                                <img src="<?php echo e(asset('website')); ?>/assets/images/file_img_icon.svg">
                            </div>
                            <div class="form-check form-switch">
                                <label class="form-check-label" for="">Monthly</label>
                                <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault">
                                <label class="form-check-label" for="">Yearly</label>
                            </div>
                        </div>
                        <span class="monthly">
                            <div>
                                <a>
                                    <h6><?php echo e($bronzeMonthly->name ?? ''); ?></h6>
                                </a>

                            </div>
                            <div class="package_pricing">
                                <h3>£<?php echo e($bronzeMonthly->amount ?? ''); ?></h3>
                            </div>
                            <div><?php echo $bronzeMonthly->description ?? ''; ?></div>
                            <?php if(auth()->guard()->check()): ?>
                                <button type="button" class="btn yellow_btn arrow_up_right_btn_img subscription-btn"
                                    data-package-id="<?php echo e($bronzeMonthly->id); ?>">Click Here To Get Started
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></button>
                            <?php else: ?>
                                <a href="<?php echo e(route('register')); ?>" class="btn yellow_btn arrow_up_right_btn_img">Click Here To
                                    Get
                                    Started
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></a>
                            <?php endif; ?>
                        </span>
                        <span class="yearly">
                            <div>
                                <a>
                                    <h6><?php echo e($bronzeYearly->name ?? ''); ?></h6>
                                </a>

                            </div>
                            <div class="package_pricing">
                                <h3>£<?php echo e($bronzeYearly->amount ?? ''); ?></h3>
                            </div>
                            <div><?php echo $bronzeYearly->description ?? ''; ?></div>
                            <?php if(auth()->guard()->check()): ?>
                                <button type="button" class="btn yellow_btn arrow_up_right_btn_img subscription-btn"
                                    data-package-id="<?php echo e($bronzeYearly->id); ?>">Click Here To Get Started
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></button>
                            <?php else: ?>
                                <a href="<?php echo e(route('register')); ?>" class="btn yellow_btn arrow_up_right_btn_img">Click Here To
                                    Get
                                    Started
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></a>
                            <?php endif; ?>
                        </span>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="basic_packg_sec premium_pakg">
                        <div class="custom_switch_toggle">
                            <div>
                                <img src="<?php echo e(asset('website')); ?>/assets/images/file_img_icon.svg">
                            </div>
                            <div class="form-check form-switch">
                                <label class="form-check-label" for="">Monthly</label>
                                <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault1">
                                <label class="form-check-label" for="">Yearly</label>
                            </div>
                        </div>
                        <span class="monthly">
                            <div>
                                <a>
                                    <h6><?php echo e($goldMonthly->name ?? ''); ?></h6>
                                </a>

                            </div>
                            <div class="package_pricing">
                                <h3>£<?php echo e($goldMonthly->amount ?? ''); ?></h3>
                            </div>
                            <div><?php echo $goldMonthly->description ?? ''; ?></div>
                            <?php if(auth()->guard()->check()): ?>
                                <button type="button" class="btn yellow_btn arrow_up_right_btn_img subscription-btn"
                                    data-package-id="<?php echo e($goldMonthly->id); ?>">Click Here To Get Started
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow_green_clr.svg"></button>
                            <?php else: ?>
                                <a href="<?php echo e(route('register')); ?>" class="btn yellow_btn arrow_up_right_btn_img">Click Here To
                                    Get
                                    Started
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow_green_clr.svg"></a>
                            <?php endif; ?>
                        </span>
                        <span class="yearly">
                            <div>
                                <a>
                                    <h6><?php echo e($goldYearly->name ?? ''); ?></h6>
                                </a>

                            </div>
                            <div class="package_pricing">
                                <h3>£<?php echo e($goldYearly->amount ?? ''); ?></h3>
                            </div>
                            <div><?php echo $goldYearly->description ?? ''; ?></div>
                            <?php if(auth()->guard()->check()): ?>
                                <button type="button" class="btn yellow_btn arrow_up_right_btn_img subscription-btn"
                                    data-package-id="<?php echo e($goldYearly->id); ?>">Click Here To Get Started
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow_green_clr.svg"></button>
                            <?php else: ?>
                                <a href="<?php echo e(route('register')); ?>" class="btn yellow_btn arrow_up_right_btn_img">Click Here To
                                    Get
                                    Started
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow_green_clr.svg"></a>
                            <?php endif; ?>
                        </span>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="basic_packg_sec">
                        <div class="custom_switch_toggle">
                            <div>
                                <img src="<?php echo e(asset('website')); ?>/assets/images/file_img_icon.svg">
                            </div>
                            <div class="form-check form-switch">
                                <label class="form-check-label" for="">Monthly</label>
                                <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault2">
                                <label class="form-check-label" for="">Yearly</label>
                            </div>
                        </div>
                        <span class="monthly">
                            <div>
                                <a>
                                    <h6><?php echo e($silverMonthly->name ?? ''); ?></h6>
                                </a>

                            </div>
                            <div class="package_pricing">
                                <h3>£<?php echo e($silverMonthly->amount ?? ''); ?></h3>
                            </div>
                            <div><?php echo $silverMonthly->description ?? ''; ?></div>
                            <?php if(auth()->guard()->check()): ?>
                                <button type="button" class="btn yellow_btn arrow_up_right_btn_img subscription-btn"
                                    data-package-id="<?php echo e($silverMonthly->id); ?>">Click Here To Get Started
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></button>
                            <?php else: ?>
                                <a href="<?php echo e(route('register')); ?>" class="btn yellow_btn arrow_up_right_btn_img">Click Here To
                                    Get
                                    Started
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></a>
                            <?php endif; ?>
                        </span>
                        <span class="yearly">
                            <div>
                                <a>
                                    <h6><?php echo e($silverYearly->name ?? ''); ?></h6>
                                </a>

                            </div>
                            <div class="package_pricing">
                                <h3>£<?php echo e($silverYearly->amount ?? ''); ?></h3>
                            </div>
                            <div><?php echo $silverYearly->description ?? ''; ?></div>
                            <?php if(auth()->guard()->check()): ?>
                                <button type="button" class="btn yellow_btn arrow_up_right_btn_img subscription-btn"
                                    data-package-id="<?php echo e($silverYearly->id); ?>">Click Here To Get Started
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></button>
                            <?php else: ?>
                                <a href="<?php echo e(route('register')); ?>" class="btn yellow_btn arrow_up_right_btn_img">Click Here To
                                    Get Started
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right.svg "></a>
                            <?php endif; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://js.stripe.com/v3/"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.yearly ul li, .monthly ul li').prepend('<i class="fa-solid fa-circle-arrow-right"></i> ');
            $(".pricing_details .basic_packg_sec .yearly").hide();
            $(document).on("change", ".custom_switch_toggle .form-switch input[type=checkbox]", function() {
                if ($(this).is(":checked")) {
                    $(this).closest('.pricing_details .basic_packg_sec').find('.monthly').hide();
                    $(this).closest('.pricing_details .basic_packg_sec').find('.yearly').show();
                } else {
                    $(this).closest('.pricing_details .basic_packg_sec').find('.yearly').hide();
                    $(this).closest('.pricing_details .basic_packg_sec').find('.monthly').show();
                }
            });

            // Attach click event to all subscription buttons
            $('.subscription-btn').on('click', function() {
                const packageId = $(this).data('package-id');

                $.ajax({
                    url: "<?php echo e(route('checkout.session')); ?>",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    contentType: 'application/json',
                    data: JSON.stringify({
                        package_id: packageId,
                        success_url: "<?php echo e(route('checkout.success')); ?>", // Replace with your success URL
                        cancel_url: "<?php echo e(route('checkout.cancel')); ?>"
                    }),
                    success: function(data) {
                        if (data.url) {
                            window.location.href = data.url; // Redirect to Stripe Checkout
                        } else {
                            alert(data.error || 'Something went wrong.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', error);
                        alert('An error occurred. Please try again.');
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\website\pricing.blade.php ENDPATH**/ ?>