<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="payment_management">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_table custom_cards_design">
                        <h1>Payment Management</h1>
                        <table class="table myTable datatable">
                            <thead>
                                <tr>
                                    <th>Sr#</th>
                                    <th>Company Name</th>
                                    <th>Email</th>
                                    <th>Subscription Type</th>
                                    <th>Amount</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $subscriptions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($loop->iteration); ?></td>
                                        <td><?php echo e($subscription->user->company_name ?? ''); ?></td>
                                        <td><?php echo e($subscription->user->email ?? ''); ?></td>
                                        <td><?php echo e($subscription->package->name ?? ''); ?></td>
                                        <td>$ <?php echo e($subscription->package->amount ?? ''); ?></td>
                                        <td><?php echo e($subscription->created_at->format('d/m/Y')); ?> </td>
                                        <td> <span
                                                class="<?php echo e($subscription->status == 'active' ? 'success' : 'danger'); ?>"><?php echo e($subscription->status ?? '-'); ?></span>
                                        </td>
                                        
                                        
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\subscriptions\index.blade.php ENDPATH**/ ?>