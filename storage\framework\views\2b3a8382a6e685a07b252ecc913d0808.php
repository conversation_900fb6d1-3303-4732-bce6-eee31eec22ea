<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Waypoints in Directions</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA0z3VFl1wO12-FWBFTC1_fdfOMJgaL9R4&libraries=places&callback=initMap"
        defer></script>
    <style>
        #map {
            height: 400px;
            width: 100%;
        }

        #routes {
            margin-top: 20px;
        }

        .route-container {
            margin-bottom: 10px;
            position: relative;
        }

        .remove-btn {
            color: red;
            cursor: pointer;
            font-size: 20px;
            position: absolute;
            top: 5px;
            right: 10px;
        }
    </style>
</head>

<body>

    <div id="map"></div>
    <div id="routes">
        <div class="route-container" id="route1">
            <b>Start:</b>
            <input type="text" class="start-point" id="start1" placeholder="Enter starting location">
            <br />
            <b>End:</b>
            <input type="text" class="end-point" id="end1" placeholder="Enter destination">
            <input type="hidden" class="start-lat" id="start-lat1">
            <input type="hidden" class="start-lng" id="start-lng1">
            <input type="hidden" class="end-lat" id="end-lat1">
            <input type="hidden" class="end-lng" id="end-lng1">
            <span class="remove-btn" onclick="removeRoute('route1')">×</span>
        </div>
    </div>
    <input type="button" value="Add More" id="addMore" />
    <input type="submit" id="showlocationsonmap" value="Submit" />
    <div id="directions-panel"></div>

    <script>
        let routeCount = $(".route-container").length;
        let markers = []; // To store markers on the map

        function initMap() {
            const map = new google.maps.Map($("#map")[0], {
                zoom: 6,
                center: {
                    lat: 41.85,
                    lng: -87.65
                }, // Default center
            });

            // Initialize autocomplete for start and end fields
            initAutocomplete(map);

            // Event listener for the Submit button
            $("#showlocationsonmap").on("click", () => {
                displayPinsOnMap(map);
            });

            // Event listener for the Add More button
            $("#addMore").on("click", addRoute);
            $(".end-point").on("focusout", function() {
                displayPinsOnMap(map);
            });
        }

        // Initialize Autocomplete for start and end fields
        function initAutocomplete(map) {
            $("input.start-point, input.end-point").each(function() {
                const input = this;
                const autocomplete = new google.maps.places.Autocomplete(input);

                // Set the event listener when a place is selected
                autocomplete.addListener("place_changed", function() {
                    const place = autocomplete.getPlace();
                    if (!place.geometry) {
                        console.error("No geometry found for the place");
                        return;
                    }

                    // Fill the latitude and longitude fields
                    const lat = place.geometry.location.lat();
                    const lng = place.geometry.location.lng();
                    const latField = $(input).hasClass('start-point') ? '.start-lat' : '.end-lat';
                    const lngField = $(input).hasClass('start-point') ? '.start-lng' : '.end-lng';

                    $(latField).val(lat);
                    $(lngField).val(lng);

                    // Optionally place a marker on the map
                    new google.maps.Marker({
                        position: place.geometry.location,
                        map: map,
                    });

                    map.setCenter(place.geometry.location); // Center the map on the selected location
                });
            });
        }

        // Function to display pins (markers) for start, end, and waypoints
        function displayPinsOnMap(map) {
            const routeContainers = $(".route-container");

            // Clear previous markers
            markers.forEach(marker => marker.setMap(null));
            markers = [];

            // Loop through each route container
            routeContainers.each((index, routeContainer) => {
                const startPoint = $(routeContainer).find(".start-point").val();
                const endPoint = $(routeContainer).find(".end-point").val();

                if (startPoint && index === 0) {
                    geocodeAddress(startPoint, 'Start', '.start-lat', '.start-lng', map);
                }

                if (endPoint) {
                    geocodeAddress(endPoint, 'End', '.end-lat', '.end-lng', map);
                }
            });
        }

        // Geocode the address to get the LatLng and place a marker
        function geocodeAddress(address, title, latField, lngField, map) {
            const geocoder = new google.maps.Geocoder();
            geocoder.geocode({
                address: address
            }, (results, status) => {
                if (status === google.maps.GeocoderStatus.OK) {
                    const location = results[0].geometry.location;
                    $(latField).val(location.lat());
                    $(lngField).val(location.lng());

                    new google.maps.Marker({
                        position: location,
                        map: map,
                        title: title,
                    });

                    map.setCenter(location); // Optionally, center the map on the first marker
                } else {
                    console.error("Geocode was not successful: " + status);
                }
            });
        }

        // Add a new route (with dynamic start/end points)
        function addRoute() {
            routeCount++;
            const lastRouteEndPoint = $(`#route${routeCount - 1} .end-point`).val();

            const newRouteDiv = $(`
        <div class="route-container" id="route${routeCount}">
            <b>Start:</b>
            <input type="text" class="start-point" id="start${routeCount}" placeholder="Enter starting location">
            <br />
            <b>End:</b>
            <input type="text" class="end-point" id="end${routeCount}" placeholder="Enter destination">
            <input type="hidden" class="start-lat" id="start-lat${routeCount}">
            <input type="hidden" class="start-lng" id="start-lng${routeCount}">
            <input type="hidden" class="end-lat" id="end-lat${routeCount}">
            <input type="hidden" class="end-lng" id="end-lng${routeCount}">
            <span class="remove-btn" onclick="removeRoute('route${routeCount}')">×</span>
        </div>
    `);

            $("#routes").append(newRouteDiv);

            // Set the start point of this new route as the end point of the previous route
            newRouteDiv.find(".start-point").val(lastRouteEndPoint);

            initAutocomplete(map); // Reinitialize autocomplete after adding a new route
        }

        // Remove a specific route
        function removeRoute(routeId) {
            $(`#${routeId}`).remove();
            updateRouteStartPoints();
        }

        // Update the start points of routes after one is removed
        function updateRouteStartPoints() {
            const routeContainers = $(".route-container");
            routeContainers.each((index, routeContainer) => {
                const startInput = $(routeContainer).find(".start-point");
                if (index === 0) {
                    startInput.prop("disabled", false); // First route: allow user to choose start point
                } else {
                    const prevEndPoint = $(routeContainers[index - 1]).find(".end-point");
                    startInput.val(prevEndPoint.val()); // Set start point to the previous route's end point
                    startInput.prop("disabled", true); // Disable to prevent editing
                }
            });
        }

        window.initMap = initMap;
    </script>

</body>

</html>
<?php /**PATH D:\guesttrip\resources\views\website\google_map.blade.php ENDPATH**/ ?>