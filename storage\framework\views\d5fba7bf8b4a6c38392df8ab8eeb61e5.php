

<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>

    <section class="notifications">
        <div class="container-fluid custom_container">
            <div class="row">
                <div class="col-md-12">
                    <div class="notification_section custom_cards_design">
                        <div class="row custom_row">
                            <div class="col-md-12">
                                <div class="notify_wrapper custom_justify_between">
                                    <h1>Notifications</h1>
                                    <a href="#!" class="btn btn_dark_green">Mark All As Read</a>
                                </div>
                            </div>
                            <?php for($i=0;$i<10;$i++): ?>
                            <div class="col-md-12 custom_notification_column">
                                <div class="navbar_notification custom_justify_between" >
                                    <div class="user_notification custom_flex">
                                        <div class="user_image">
                                            <img src="<?php echo e(asset('website')); ?>/assets/images/cmsicon.png">
                                        </div>
                                        <div class="user_profile">
                                            <h1>William Manager</h1>
                                            <h3>It is a long established fact that a reader will be distracted</h3>
                                        </div>
                                    </div>
                                    <div class="status_time custom_flex">
                                        <i class="fa-solid fa-circle"></i>
                                        <h3>00 min ago</h3>
                                    </div>
                                </div>
                            </div>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make("theme.layout.master", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\notifications.blade.php ENDPATH**/ ?>