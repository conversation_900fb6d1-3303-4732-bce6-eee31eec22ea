:root {
    --white_clr: #fff;
    --bg_color: #F5FBFF;
    --grey_clr: #71717A;
    --black_clr: #18181B;
    --green_clr: rgba(3, 172, 0, 0.20);
    --dark_green_clr: #03525A;
    --dark_grey_clr: #6C6C6C;
    --dashboard_txt_clr: #737373;
    --placeholder: #A1A1AA;
}

@font-face {
    font-family: 'PlusJakartaSans-Regular';
    src: url('/website/assets/fonts/Plus_Jakarta_Sans/static/PlusJakartaSans-Regular.ttf');
}

@font-face {
    font-family: 'PlusJakartaSans-SemiBold';
    src: url('/website/assets/fonts/Plus_Jakarta_Sans/static/PlusJakartaSans-SemiBold.ttf');
}

@font-face {
    font-family: 'PlusJakartaSans-Bold';
    src: url('/website/assets/fonts/Plus_Jakarta_Sans/static/PlusJakartaSans-Bold.ttf');
}

@font-face {
    font-family: 'PlusJakartaSans-Light';
    src: url('/website/assets/fonts/Plus_Jakarta_Sans/static/PlusJakartaSans-Light.ttf');
}

@font-face {
    font-family: 'PlusJakartaSans-Medium';
    src: url('/website/assets/fonts/Plus_Jakarta_Sans/static/PlusJakartaSans-Medium.ttf');
}

@font-face {
    font-family: 'PlusJakartaSans-ExtraLight';
    src: url('/website/assets/fonts/Plus_Jakarta_Sans/static/PlusJakartaSans-ExtraLight.ttf');
}

@font-face {
    font-family: 'PlusJakartaSans-Light';
    src: url('/website/assets/fonts/Plus_Jakarta_Sans/static/PlusJakartaSans-Light.ttf');
}

/*poppins*/
/*500*/
@font-face {
    font-family: 'Poppins-Medium';
    src: url('/website/assets/fonts/poppins/Poppins-Medium.ttf');
}

/**/
/*DM Sans*/
/*700*/
@font-face {
    font-family: 'DMSans-Bold';
    src: url('/website/assets/fonts/dm_sans/DMSans-Bold.ttf');
}

/*500*/
@font-face {
    font-family: 'DMSans-Medium';
    src: url('/website/assets/fonts/dm_sans/DMSans-Medium.ttf');
}

/*400*/
@font-face {
    font-family: 'DMSans-Regular';
    src: url('/website/assets/fonts/dm_sans/DMSans-Regular.ttf');
}

/**/
h1 {
    color: var(--black_clr);
    font-family: 'PlusJakartaSans-Bold';
    font-size: 20px;
    line-height: 24px;
    margin: 0
}

h2 {
    color: var(--black_clr);
    font-family: 'PlusJakartaSans-Bold';
    font-size: 16px;
    line-height: 24px;
    margin: 0
}

h3 {
    color: var(--black_clr);
    font-family: 'PlusJakartaSans-Bold';
    font-size: 13px;
    line-height: 24px;
    margin: 0
}

h4 {
    color: var(--grey_clr);
    font-family: 'PlusJakartaSans-Medium';
    font-size: 11px;
    line-height: 18px;
    margin: 0;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.txt_field input::-webkit-outer-spin-button,
.txt_field input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

body.app-default {
    background: #F5FBFF;
}

.f_30 {
    font-size: 30px;
    font-family: 'Poppins-Medium';
    font-weight: 500;
}

#kt_app_wrapper .app-main .btn {
    padding: 10px 30px;
    border-radius: 8px;
    font-family: 'PlusJakartaSans-SemiBold';
    line-height: 24px;
    font-size: 14px;
}

#kt_app_wrapper .app-main .btn.btn_dark_green {
    background: var(--dark_green_clr);
    color: var(--white_clr);
    border: 1px solid var(--dark_green_clr);
}

#kt_app_wrapper .app-main .btn.btn_green {
    background: var(--green_clr);
    color: #4a4a4a;
    border: 1px solid var(--green_clr);
}

#kt_app_wrapper .app-main .btn.btn_grey {
    background: #6C6C6C;
    color: #FFF;
    border: 1px solid #6C6C6C;
}

#kt_app_wrapper .app-main .btn.btn_transparent {
    background: transparent;
    color: var(--dark_green_clr);
    border: 1px solid var(--dark_green_clr);
    box-shadow: 0px 15px 30px 0px rgba(58, 153, 74, 0.18);
}

.txt_field {
    display: flex;
    padding: 10px 15px;
    border-radius: 7px;
    border: 1px solid #C5C5C5;
    background: #FFF;
    align-items: center;
}

.txt_field.custom_search {
    border-radius: 10px;
    border: 1px solid #E4E4E7;
    background: #FFF;
    padding: 15px;
    flex-wrap: unset;
    min-width: 800px;
}

.txt_field.custom_search input.custom_search_box {
    width: 100%;
    box-shadow: none;
}

.txt_field input:focus-visible {
    outline: none;
}

.txt_field input[type="text"],
.txt_field input[type="tel"],
.txt_field input[type="email"],
.txt_field input[type="date"],
.txt_field input[type="number"],
.txt_field input[type="url"],
.txt_field select,
.txt_field textarea,
.txt_field input[type="file"],
.txt_field input[type="link"] {
    border: 0;
    padding: 0 10px;
    background: #FFF;
    color: var(--dark_grey_clr);
    font-size: 14px;
    font-family: "PlusJakartaSans-Medium";
}

.txt_field input[type="text"]::placeholder,
.txt_field input[type="tel"]::placeholder,
.txt_field input[type="email"]::placeholder,
.txt_field input[type="date"]::placeholder,
.txt_field input[type="number"]::placeholder,
.txt_field input[type="url"]::placeholder,
.txt_field input[type="link"]::placeholder {
    color: var(--placeholder);
    font-size: 14px;
    font-family: "PlusJakartaSans-Medium";
    font-size: 12px;
}

.txt_field label {
    font-size: 14px;
    font-family: "PlusJakartaSans-SemiBold";
    white-space: nowrap;
    margin: 0;
    color: #4A4A4A;
}

.txt_field label.error {
    color: red;
    font-family: 'PlusJakartaSans-Regular';
}

.custom_card {
    border-radius: 10px;
    border: 1px solid #E4E4E7;
    background: #FFF;
    padding: 15px;
    height: 100%;
}

.cards_wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 1%;
    row-gap: 10px;
    margin-top: 15px;
}

.custom_card h4 {
    margin-bottom: 15px;
}

.custom_cards_design {
    border-radius: 10px;
    border: 1px solid #E4E4E7;
    background: #FFF;
    padding: 20px;
    height: 100%;
}

.custom_flex {
    display: flex;
    align-items: center;
    gap: 10px;
}

.custom_row {
    row-gap: 20px;
}

.app-main {
    padding: 30px 20px;
}

.custom_card.trip_status .danger {
    width: fit-content;
}

.custom_justify_between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar_wrapper .custom_sidebar_menu .menu-item .menu-content a.active {
    color: #03525A;
    font-family: 'PlusJakartaSans-Medium';
}

.sidebar_wrapper .custom_sidebar_menu .menu-item .menu-content a.active .sidebar_icon i {
    color: #03525A;
}

span.success {
    border-radius: 5px;
    background: rgba(3, 172, 0, 0.30);
    padding: 8px 20px;
    color: #4A4A4A;
    font-size: 13px;
    font-family: 'PlusJakartaSans-Medium';
    width: fit-content;
}

span.danger {
    border-radius: 5px;
    background: rgba(239, 68, 68, 0.80);
    padding: 8px 20px;
    color: #fff;
    font-size: 13px;
    font-family: 'PlusJakartaSans-Medium';
}

span.warning {
    border-radius: 5px;
    background: rgba(255, 248, 40, 0.50);
    padding: 8px 20px;
    color: #4A4A4A;
    font-size: 13px;
    font-family: 'PlusJakartaSans-Medium';
}

#kt_app_wrapper .app-main .btn.btn_dark_green:hover {
    background: transparent;
    border: 1px solid var(--dark_green_clr);
    color: var(--dark_green_clr);
}

#kt_app_wrapper .app-main .btn.btn_transparent:hover {
    background: var(--dark_green_clr);
    color: white;
}

#kt_app_wrapper .app-main .btn.btn_grey:hover {
    background: transparent;
    border: 1px solid #6C6C6C;
    color: #6C6C6C;
}

#kt_app_wrapper .app-main .btn.btn_dark_green:hover i {
    color: var(--dark_green_clr);
}

#kt_app_wrapper .app-main .btn.btn_grey:hover i {
    color: #6C6C6C;
}

.txt_field textarea {
    resize: none;
    MIN-HEIGHT: UNSET;
}

#kt_app_wrapper .app-main button.btn:has(i) i {
    margin-right: 10px;
    color: white;
}

.txt_field select.form-select {
    background-image: url("/website/assets/images/chevron-down.png");
    background-repeat: no-repeat;
    background-position: right;
}

.dropdown li a:has(i) i {
    margin-right: 10px;
    color: #4A4A4A;
}

[data-kt-app-layout=dark-sidebar] .app-sidebar .menu>.menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-title,
[data-kt-app-layout=dark-sidebar] .app-sidebar .menu>.menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-icon i {
    color: #71717A;
}

[data-kt-app-layout=dark-sidebar] .app-sidebar .menu>.menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-arrow:after,
[data-kt-app-layout=dark-sidebar] .app-sidebar .menu>.menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-bullet .bullet {
    background: #71717A
}

.custom_scroll_tbl {
    max-height: 420px;
    overflow-y: auto;
    padding-right: 10px;
    scrollbar-color: #027F8A transparent;
}

.app-sidebar.sidebar_wrapper .scroll-y:hover {
    overflow-y: hidden;
}

/*Sidebar*/
.sidebar_wrapper .app-sidebar-toggle {
    display: none;
}

.app-sidebar.sidebar_wrapper {
    background: var(--white_clr);
    padding: 30px 20px;
    z-index: 0;
}

.sidebar_wrapper .custom_sidebar_menu .menu-item .menu-content a {
    padding: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--dashboard_txt_clr);
    font-size: 13px;
    font-family: PlusJakartaSans-Light;
}

.sidebar_wrapper .custom_sidebar_menu .menu-item .menu-content {
    padding: 0;
}

.sidebar_wrapper .custom_sidebar_menu .menu-item {
    padding: 0;
}

.sidebar_wrapper .app-sidebar-menu .app-sidebar-wrapper {
    width: unset;
}

.sidebar_wrapper .custom_sidebar_menu {
    gap: 10px;
    margin-top: 50px;
}

.app-sidebar.sidebar_wrapper .app-sidebar-logo {
    border: 0;
    display: block;
    height: 30px;
}

.sidebar_wrapper .app-sidebar-logo img {
    height: 100%;
    width: 100%;
    object-fit: cover;
}

.sidebar_wrapper .custom_sidebar_menu .menu-item .logout {
    position: absolute;
    bottom: 20px;
    width: 100%;
}

.sidebar_wrapper .custom_sidebar_menu .menu-item .sidebar_icon {
    width: 18px;
    height: 18px;
}


/* Navbar */
.app-header.custom_header {
    background: none;
    box-shadow: none;
    position: absolute;
    z-index: unset;
}

.app-container {
    padding-right: 0 !important;
}

.app-header-menu {
    align-items: center;
}

.main_header {
    border-radius: 0px 0px 10px 10px;
    border: 1px solid var(--Stroke, #E4E4E7);
    background: #FFF;
    padding: 0 20px;
}

.app-navbar.custom_navbar {
    gap: 25px;
}

.custom_navbar .custom_notification .custom_icon i {
    font-size: 20px;
}

.user_img {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 1px solid #1E1E1E;
    overflow: hidden;
    border: none
}

.user_img img {
    height: 100%;
    width: 100%;
    object-fit: cover;
}

.custom_profile_menu .custom_admin_detail {
    justify-content: space-evenly;
    padding: 0;
}

.custom_profile_menu a:has(i) i {
    margin-right: 10px;
}

.custom_profile_menu .menu-content.custom_logout a {
    display: flex;
    align-items: center;
    color: #252f4a;
}

.app-navbar-item .btn.btn_dark_green {
    background: var(--dark_green_clr);
    color: var(--white_clr);
    border: 1px solid var(--dark_green_clr);
}

/* Table Css */
.table-responsive:has(.datatable tbody tr td .dropdown-menu.show) {
    min-height: 200px;
}

.custom_table>h2 {
    margin-bottom: 5px;
}

#kt_app_body table thead tr th {
    padding: 10px 15px;
    text-align: center;
    color: #4A4A4A;
    font-family: 'PlusJakartaSans-Bold';
    white-space: nowrap;
}

#kt_app_body table thead tr,
#kt_app_body table tbody tr {
    border-bottom: 1px solid #E4E4E7;
}

#kt_app_body table tbody tr td,
#kt_app_body .table:not(.table-bordered) tbody tr td:first-child {
    padding: 20px 15px;
    text-align: center;
    color: #71717A;
    font-family: 'PlusJakartaSans-Medium';
}

#kt_app_body table.table:not(.table-bordered) td:first-child,
.custom_table table.table:not(.table-bordered) th:first-child,
#kt_app_body table.table:not(.table-bordered) tr:first-child {
    padding: 0;
}

#kt_app_body table.table:not(.table-bordered) th:last-child,
#kt_app_body table.table:not(.table-bordered) tr:last-child {
    padding: 0;
}

#kt_app_body table.dataTable>thead>tr>td:not(.sorting_disabled),
#kt_app_body table.dataTable>thead>tr>th:not(.sorting_disabled) {
    padding: 0;
}

#kt_app_body table tbody tr td .dropdown button {
    padding: 0;
    border: 0;
    background: none;
}

#kt_app_body table tbody tr td .dropdown button:after {
    display: none;
}

#kt_app_body table tbody tr td .dropdown button i {
    font-size: 20px;
    color: #A1A1AA;
}

#kt_app_body table {
    margin: 0;
}

.custom_table h1 {
    margin-bottom: 20px;
}

.custom_table span.success,
.custom_table span.danger,
.custom_table span.warning {
    border-radius: 50px;
}

#kt_app_body .dataTables_paginate .pagination li.active>.page-link {
    background: #03525A;
}

#kt_app_body table tbody tr td a {
    color: #71717A;
}

/* Table Dropdown */
#kt_app_body table tbody ul.dropdown-menu.show {
    border-radius: 10px;
    background: #FFF;
    box-shadow: 0px 4px 25px 0px rgba(0, 0, 0, 0.10);
    padding: 15px 30px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

#kt_app_body table tbody a.dropdown-item {
    font-size: 12px;
    padding: 5px 0px;
    color: #4A4A4A;
}

#kt_app_body table tbody tr td.text-center .menu-sub-dropdown.show {
    border-radius: 10px;
    background: #FFF;
    box-shadow: 0px 4px 25px 0px rgba(0, 0, 0, 0.10);
    padding: 15px 30px;
}

#kt_app_body table tbody tr td.text-center .menu-sub-dropdown.show .menu-item a.menu-link {
    font-size: 12px;
    padding: 10px 0;
    color: #4A4A4A;
}

#kt_app_body table tbody tr td.text-center .menu-sub-dropdown.show .menu-item {
    padding: 0;
}

#kt_app_body table tbody a.dropdown-item.download_invoice {color: var(--dark_green_clr);font-family: 'PlusJakartaSans-Bold';}

.custom_cards_design .custom_card .dropdown button {background: none;border: 0;}
.custom_cards_design .custom_card .dropdown button i {color: black!important;margin: 0;}
.custom_cards_design .custom_card .dropdown button::after {
    display: none;
}

.custom_cards_design .custom_card .dropdown ul.dropdown-menu.show {
    border-radius: 10px;
    background: #FFF;
    box-shadow: 0px 4px 25px 0px rgba(0, 0, 0, 0.10);
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.custom_cards_design .custom_card .dropdown ul li a.dropdown-item {
    font-size: 12px;
    padding: 5px;
    color: #4A4A4A;
}

form:has(.custom_cards_design) {
    height: 100%
}

/* Home Page Css */
.homepage_cards .cards_wrapper {
    height: 100%;
}

.homepage_cards .cards_wrapper .custom_card {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.homepage_cards .custom_card {
    width: 19.2%;
}

.homepage_cards h2:has(span) span {
    color: #71717A;
    font-family: 'PlusJakartaSans-Regular';
    margin-left: 6px;
}

.homepage_notification .nav_linking ul {
    padding: 0 10px 0 0;
    list-style: none;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: 410px;
    overflow-y: scroll;
}

.homepage_notification .nav_linking ul li .navbar_notification {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.homepage_notification .nav_linking .user_name h3:last-child {
    color: #71717A;
    font-family: 'PlusJakartaSans-Regular';
}

.homepage_notification .nav_linking .status_time h4 {
    font-family: 'PlusJakartaSans-Regular';
    letter-spacing: unset;
}

.homepage_notification .navbar_notification_header {
    margin-bottom: 15px;
}

.custom_select select.form-select {
    border: 0;
    width: unset;
    padding: 0 40px 0 16px;
}

.progress {
    height: 5px;
}

.progress-bar {
    background: #027F8A;
}

.custom_trip_stats .stats_progress {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-top: 50px;
}

.stats_progress .stats_detail {
    margin-bottom: 10px;
}

.custom_progress:first-child .progress-bar {
    width: 75%;
}

.custom_progress:nth-child(2) .progress-bar {
    width: 60%;
}

.custom_progress:nth-child(3) .progress-bar {
    width: 40%;
}

.custom_progress:last-child .progress-bar {
    width: 25%;
}

.homepage_section .graph_chart h2 {
    margin-bottom: 30px
}

.homepage_notification .nav_linking ul::-webkit-scrollbar,
.table-responsive::-webkit-scrollbar {
    width: 5px;
}

.homepage_notification .nav_linking ul::-webkit-scrollbar-track,
.table-responsive::-webkit-scrollbar-track {
    background: #027F8A;
    border-radius: 20px;
}

.homepage_notification .nav_linking ul::-webkit-scrollbar-thumb,
.table-responsive::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 20px;
}

.homepage_notification .nav_linking ul::-webkit-scrollbar-thumb:hover {
    background: #aeecf1;
}

.homepage_notification .nav_linking ul,
.table-responsive:hover,
.table-responsive {
    scrollbar-width: auto;
    scrollbar-color: auto;
}

.table-responsive:hover {
    scrollbar-color: #027F8A transparent;
}

/* Notifications */
.custom_navbar .notification_wrapper ul.nav li .navbar_notification {
    border-bottom: 0.5px solid #BDBDBD;
    background: #FFF;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.02);
    padding: 15px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.custom_navbar .navbar_notification .status_time {
    display: flex;
    gap: 6px;
    align-items: center;
}

.custom_navbar .navbar_notification .status_time i {
    font-size: 12px;
    color: #027F8A;
}

.custom_navbar .navbar_notification .user_profile h3 {
    color: #4A4A4A;
}

.custom_navbar .navbar_notification .user_profile h3:nth-child(1) {
    font-family: 'PlusJakartaSans-Medium';
    margin-bottom: 5px;
    font-weight: 500;
}

.custom_navbar .notification_wrapper ul.nav {
    display: flex;
    flex-direction: column;
}

.custom_navbar .notification_wrapper {
    border-radius: 10px;
    background: #FFF;
    box-shadow: 0px 4px 35px 0px rgba(0, 0, 0, 0.10);
    padding: 10px;
}

.custom_navbar .navbar_notification_header {
    display: flex;
    justify-content: space-between;
    padding: 15px 10px;
}

.custom_navbar .navbar_notification_header a {
    color: #4A4A4A;
    font-size: 14px;
    text-decoration: underline;
}

.custom_navbar .menu-sub-dropdown.menu.notification_wrapper {
    position: absolute !important;
    width: 60%;
    left: 40% !important;
    right: 0;
}

.custom_navbar .notification_wrapper ul li .navbar_notification:hover {
    background: #027f8a3b;
}

.custom_navbar .notification_wrapper ul li:last-child .navbar_notification {
    border: 0;
}

.user_image {
    border-radius: 50%;
    border: 1px solid var(--Background, #6C6C6C);
    background: rgba(74, 74, 74, 0.10);
    width: 70px;
    height: 70px;
    overflow: hidden;
}

.user_image img {
    height: 100%;
    width: 100%;
    object-fit: cover;
}

.user_profile h1 {
    margin-bottom: 10px;
}

/* Profile Page */
.profile_picture .profile_image {
    border-radius: 20px;
    background: var(--dark_green_clr);
    display: flex;
    align-items: center;
    justify-content: center;
    height: 160px;
    width: 160px;
}

/*.profile_image:has(.image-input-changed) .image-input-wrapper img {display: none;}*/
.image-input .image-input-wrapper {
    height: 160px;
    border-radius: 20px;
    overflow: hidden;
    width: 100%;
}

#kt_app_wrapper .app-main .profile_image .btn.edit_icon {
    background: rgba(197, 197, 197, 0.72);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    padding: 0;
}

#kt_app_wrapper .app-main .profile_image .btn.edit_icon i {
    margin: 0;
    color: #E4E4E7;
    font-size: 15px !important;
    font-family: FontAwesome ! Important;
}

.txt_field .custom_eye_icon {
    position: absolute;
    bottom: 25px;
    right: 18px;
}

.txt_field .fa-eye {
    color: var(--dark_green_clr);
}

.profile_image:has(.image-input-changed) {
    background: unset;
}

.profile_picture .profile_image .image-input{width: 100%;}
.image-input .image-input-wrapper img{height: 100%; width: 100%; object-fit: cover; object-position: bottom;}
.profile_picture .profile_image .image-input.image-input-outline .image-input-wrapper{border: 1px solid #6C6C6C; box-shadow: none;background-image: none!important;}
.icon_image .profile_image{background: #EDEDED;}
.profile_picture .profile_image .edit_icon {display: flex;}
.profile_image label.edit_icon i>span:before{content:"\f044";}
.profile_image span.edit_icon i:before{content:"\f1f8";}
.profile_picture .profile_image label.edit_icon {top: 15%; left: unset; right: 20%;}
.profile_picture .profile_image span.edit_icon {top: 15%; right: 0; left: unset;}
.profile_picture .profile_image .image-input.image-input-empty [data-kt-image-input-action=cancel]{display: flex;}
.profile_picture .profile_image .image-input .btn.btn-active-color-primary:hover:not(.btn-active){color: white;}
.icon_image .profile_image .image-input span.edit_icon {top: 90%;}

.profile_page .profile_image {
    border-radius: 50%;
    background: var(--dark_green_clr);
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile_page .profile_image:has(.image-input-changed) .image-input-wrapper:before {
    content: "";
}

.profile_page .image-input .image-input-wrapper {
    width: 120px;
    height: 120px
}

/* Modal Css */
.modal-dialog.modal-dialog-centered {
    max-width: 35%;
}

.create_trip_modal .modal-dialog.modal-dialog-centered {
    max-width: 45%;
}

.create_trip_modal.custom_trip_modal .modal-dialog.modal-dialog-centered {
    max-width: 63%;
}

.modal_wrapper .txt_field {
    position: relative;
}

.modal-content {
    border-radius: 10px;
    background: #FFF;
    padding: 20px 30px;
}

.modal-header {
    padding: 0;
    border: 0;
    align-items: center;
}

.modal-header .btn-close {
    opacity: 1;
    margin: 0;
    padding: 0;
}

.modal-header .btn-close i {
    color: #002768;
    font-size: 16px;
}

.modal-body {
    margin-top: 25px;
    padding: 0;
}

.modal_wrapper .txt_field {
    border: 0;
    padding: 0;
    display: block;
}

.modal_wrapper .txt_field label {
    width: 100%;
    font-family: "PlusJakartaSans-Regular";
    font-size: 17px;
    font-weight: 400;
    margin-bottom: 10px;
}

.modal_wrapper .txt_field input {
    border-radius: 8px;
    background: #FFF;
    box-shadow: 0px 8px 30px 0px rgba(0, 0, 0, 0.08);
    padding: 20px 10px;
    width: 100%;
}

.modal-content .modal-body button {
    width: 100%;
}

.modal-content .custom_row_modal {
    row-gap: 20px;
}

.modal-header h1 {
    margin: auto;
}

.custom_modal .txt_field select {
    width: 100%;
}

/* Modal Checkbox */
.custom_check_box label {
    position: relative;
    cursor: pointer;
    font-family: 'PlusJakartaSans-SemiBold';
    color: #4A4A4A;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.custom_check_box label:before,
.custom_radio_wrapper label:before {
    content: '';
    -webkit-appearance: none;
    background-color: transparent;
    border: 2px solid #027F8A;
    border-radius: 4px;
    height: 15px;
    width: 15px;
    display: inline-block;
    position: relative;
    vertical-align: middle;
    cursor: pointer;
}

.custom_check_box input:checked+label:after {
    content: '\f00c';
    display: block;
    position: absolute;
    top: 8px;
    font-family: "FontAwesome";
    left: 5px;
    width: 4px;
    height: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.custom_check_box input[type="checkbox"]:checked+label:before {
    background: #002768;
    border: 0;
}

.custom_check_box label:before {
    border: 2px solid #027F8A;
}

.custom_check_box input,
.custom_radio_wrapper input {
    display: none;
}

/* Custom Radio Button */
.custom_radio_wrapper label:before {
    height: 18px;
    width: 18px;
    border: 2px solid #4A4A4A;
}

.custom_radio_wrapper label {
    position: relative;
    cursor: pointer;
}

.custom_radio_wrapper input:checked+label:after {
    content: '\f00c';
    display: block;
    position: absolute;
    top: 8px;
    font-family: "FontAwesome";
    left: 7px;
    width: 5px;
    height: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4A4A4A;
    font-size: 12px;
    background: unset;
    border: unset;
}

.custom_radio_wrapper input[type="radio"]:checked+label:before {
    background: white;
    border: 2px solid #4A4A4A;
}

.custom_radio_wrapper {
    margin: 20px 0;
}

/* Trip Management */
.trip_management .user_trip_details {
    height: 100%;
}

.user_trip_details .row.custom_row_card.custom_row {
    height: 100%;
}

.trip_management .user_trip_details .custom_card {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.trip_management .edit_user_trip.custom_justify_between {
    align-items: end;
}

.edit_user_trip .profile_image {
    border-radius: 50%;
    background: var(--dark_green_clr);
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.edit_user_trip .profile_image:has(.image-input-changed) .image-input-wrapper:before {
    content: "";
}

.edit_user_trip .image-input .image-input-wrapper {
    width: 120px;
    height: 120px
}

/* Payment Management Css */
.payment_management .custom_justify_between {
    margin-bottom: 10px;
}

/*subscription_management*/
.blur_points {
    text-decoration-line: line-through;
}

.select2-container--bootstrap5 .select2-dropdown {
    width: 200px !important
}

span.select2-search.select2-search--dropdown {
    display: none
}

.select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--selected:after {
    display: none
}

.select2-container--bootstrap5 .select2-dropdown .select2-results__option {
    font-size: 12px;
}

span.select2-selection.select2-selection--single.form-select.form-select-transparent {
    border: 0.5px solid #E4E4E7;
    width: 200px;
}

.subscription_management .custom_cards_design h2 {
    margin-bottom: 25px
}

.subscription_management .custom_cards_design .subs_card_clr h1 {
    color: var(--dark_green_clr)
}

.subscription_management .custom_cards_design .subs_card_clr {
    margin-bottom: 10px
}

.subscription_management .custom_cards_design .subs_card_clr i {
    color: var(--dark_green_clr);
    font-size: 15px
}

.subscription_management .custom_cards_design h3 {
    font-family: 'PlusJakartaSans-Medium';
}

.subscription_management .custom_cards_design .custom_justify_between .txt_field {
    width: 25%;
    margin-top: 10px
}

.subscription_management .custom_cards_design .custom_justify_between .txt_field input.total_tips_count {
    width: 100%;
    text-align: center;
}

/*USer Management*/
.app-content.developers_user_management .card .scroll_X {
    overflow-x: scroll;
    padding: 0
}

.app-content.developers_user_management .card .user_mng_delete_btn {
    border: none;
    background: none;
    width: 100%
}

.app-content.developers_user_management .card {
    border: none;
    box-shadow: none
}

.app-content.developers_user_management .app-container {
    border-radius: 10px
}

.user_managment_show .custom_col .row_box {
    height: 100%
}

.user_managment_show .simple_cards .card_footer h1 {
    font-size: 30px
}

.user_managment_show .simple_cards .card_header h2 {
    color: var(--dark_green_clr)
}

/* CMS */
.cms_tabs ul.nav.nav-pills li a.nav-link {
    border-radius: 8px;
    border: 1px solid #C5C5C5;
    background: #E4E4E7;
    box-shadow: -4.573px 4.573px 12.804px 0px rgba(0, 0, 0, 0.05);
    padding: 15px 20px;
    color: #6C6C6C;
    font-family: 'PlusJakartaSans-Regular';
    font-size: 14px;
}

.cms_tabs ul.nav.nav-pills li a.nav-link.active {
    background: #03525A;
    color: #FFF;
    border: 0;
}

.cms_tabs ul.nav.nav-pills {
    gap: 15px;
}

.cms_tabs ul.nav.nav-pills .nav-item {
    margin: 0;
}

.cms_section h1 {
    margin-bottom: 30px;
}

.cms_section .txt_field {
    display: block;
    border: 0;
    padding: 0;
}

.cms_section .txt_field label {
    width: 100%;
    font-size: 16px;
    margin-bottom: 10px;
}

.cms_section .txt_field input,
.cms_section .txt_field textarea {
    border-radius: 7px;
    border: 1px solid #C5C5C5;
    background: #FFF;
    padding: 10px 15px;
    width: 100%;
}

.cms_section .txt_field textarea {
    white-space: pre-line;
}

.profile_picture h3,
.cms_section h3 {
    margin-bottom: 10px;
}

.sub_sections h2 {
    margin: 20px 0;
}

.cms_edit_btn,
.add_sub_section {
    text-align: right;
}

.add_sub_section {
    margin-top: 30px;
}

.add_sub_section button:has(i) i {
    margin-right: 10px;
}

.cms_section h3 {
    font-family: 'PlusJakartaSans-SemiBold';
    font-size: 16px;
}

/*owner dashboad css*/
.homepage_cards.owner_home_pg_cards .custom_card {
    width: unset;
}

.owner_home_pg_cards .owner_home_pg_cards_col {
    margin-bottom: 15px;
}

.create_trip_btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
}

.create_trip_btn h1 {
    margin-bottom: unset;
}

/*subscription page*/
.subscription_sec .subscription_wrapper>h1 {
    margin-bottom: 30px;
}

.subscription_sec .subscription_wrapper .basic_package_wrapper {
    border-radius: 15px;
    border: 1px solid #C5C5C5;
    background: #FFF;
    padding: 27px 34px;
}

.subscription_sec .subscription_wrapper .basic_package_wrapper span {
    font-size: 16px;
    font-style: italic;
    line-height: 150%;
    font-family: 'DMSans-Medium';
    border-radius: 64px;
    background: #E6E6E6;
    padding: 9px 27px;
}

.subscription_sec .subscription_wrapper .basic_package_wrapper h1 {
    font-size: 27px;
    font-family: 'DMSans-Bold';
    line-height: 150%;
    margin-top: 18px;
    margin-bottom: 9px;
}

.subscription_sec .subscription_wrapper .basic_package_wrapper .basic_pkg_pts {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    padding-left: 0px;
}

.subscription_sec .subscription_wrapper .basic_package_wrapper .basic_pkg_pts h3 {
    font-family: 'DMSans-Regular';
    font-size: 15px;
    line-height: 24px;
    font-weight: 400;
}

.subscription_sec .subscription_wrapper .basic_package_wrapper h2 {
    font-family: 'DMSans-Regular';
    font-size: 15px;
    line-height: 24px;
    font-weight: 400;
    margin-bottom: 12px;
}

.subscription_sec .subscription_wrapper .basic_package_wrapper h4 {
    font-family: 'DMSans-Regular';
    font-size: 12px;
    line-height: 24px;
    font-weight: 400;
    color: #4A4A4A;
}

.subscription_sec .subscription_wrapper .basic_package_wrapper button {
    width: 100%;
    margin-top: 19px;
}

.subscription_sec .subscription_wrapper .basic_package_wrapper button img {
    padding-left: 18px;
}

.subscription_sec .subscription_wrapper .basic_package_wrapper .basic_pkg_pts h3:before {}

.subscription_sec .subscription_wrapper .basic_package_wrapper .basic_pkg_pts h3 {
    position: relative;
}

.subscription_sec .subscription_wrapper .basic_package_wrapper .basic_pkg_pts li {
    list-style: none;
    display: flex;
    column-gap: 14px;
}

.subscription_sec .subscription_wrapper .basic_package_wrapper .basic_pkg_pts li i {
    color: #027F8A;
    font-size: 16px;
    font-weight: 900;
    line-height: 20px;
}

/*profile page */
.credit_card_sec.custom_cards_design.icon_image {
    margin-top: 20px;
}

.profile_page .debit_card_sec {
    background-color: #03525A;
    border-radius: 20px;
    padding: 23px 35px 34px 35px;
    color: #D6D6D6;
}

.profile_page .debit_card_sec .bank_name h1,
.profile_page .debit_card_sec .bank_name h2,
.profile_page .debit_card_sec .card_number h1,
.profile_page .debit_card_sec .vc_code h2,
.profile_page .debit_card_sec .card_holder_name h1 {
    color: #D6D6D6;
}

.profile_page .debit_card_sec .bank_name h1 {
    font-size: 23px;
    font-family: 'PlusJakartaSans-SemiBold';
}

.profile_page .debit_card_sec .bank_chip_img {
    padding: 18px 0px 21px 0px;
}

.profile_page .debit_card_sec .vc_code {
    padding: 8px 0px 1px 0px;
}

.profile_page .debit_card_sec .card_holder_name {
    padding-top: 7px;
}

.profile_page .debit_card_sec .card_holder_name h1 {
    font-size: 23px;
    font-family: 'PlusJakartaSans-Light';
}

.profile_page .debit_card_sec .card_number h1 {
    font-size: 23px;
    font-family: 'PlusJakartaSans-ExtraLight';
}

.profile_page .debit_card_sec .vc_code h2 {
    font-family: 'PlusJakartaSans-ExtraLight';
}

.profile_page .debit_card_sec .valid_date_sec .valid_date_wrapper i {
    color: #D6D6D6;
}

.profile_page .debit_card_sec .valid_date_sec .valid_date_wrapper span.valid_thur {
    display: block;
    width: 32px;
    font-size: 12px;
    font-weight: 100;
}

.profile_page .debit_card_sec .valid_date_sec .valid_date_wrapper span:last-child {
    font-size: 18px;
}

.profile_page .debit_card_sec .valid_date_sec .valid_date_wrapper {
    display: flex;
    justify-content: end;
    align-items: center;
    column-gap: 3px;
}

.profile_page .debit_card_sec .bank_chip_img img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 10px;
}

.profile_page .debit_card_sec .bank_chip_img {
    height: 70px;
    width: 48px;
}

/* Trip Detail Css*/
.txt_field.custom_time {
    display: flex;
    border-radius: 7px;
    border: 1px solid #C5C5C5;
    background: #FFF;
    padding: 10px 15px;
    margin-top: 10px;
}

.txt_field.custom_time label {
    width: unset;
    margin: 0;
}

.txt_field.custom_time input {
    border: 0;
    padding: 0 15px;
}

.txt_field.custom_time.custom_margin {
    margin-top: 33px;
}

.add_breakfast_section .profile_picture .profile_image {
    width: 100px;
    height: 100px;
    border-radius: 10px;
}

.add_breakfast_section .profile_picture .profile_image .image-input .image-input-wrapper {
    width: 100px;
    height: 100px;
    border-radius: 10px;
}

#kt_app_wrapper .app-main .add_breakfast_section .profile_picture .profile_image .btn.edit_icon {
    width: 20px;
    height: 20px;
}

#kt_app_wrapper .app-main .add_breakfast_section .profile_picture .profile_image .btn.edit_icon i {
    font-size: 10px;
    color: #4A4A4A;
}

.add_breakfast_section .profile_picture .profile_image label.edit_icon {
    right: 25%;
}

.add_breakfast_section .append_items {
    display: flex;
    gap: 20px;
}

.add_breakfast_section .txt_field {
    width: 100%;
}

.add_breakfast_section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.add_breakfast_section .append_items h2 {
    margin: 0 0 10px 0;
}

.add_sub_section button i {
    margin: 0;
    padding: 0;
}

.cms_section .txt_field.custom_select span.select2-selection.select2-selection--single.form-select.form-select-transparent {
    width: 100%;
    border-radius: 7px;
    border: 1px solid #C5C5C5;
    padding: 10px 15px;
}

.onboarded_crew,
.append_crew_detail {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.onboarded_crew .crew_listing {
    border-bottom: 1px solid #E1E1E1;
    padding-bottom: 10px;
}

.onboarded_crew h3 {
    font-size: 16px;
    font-family: 'PlusJakartaSans-SemiBold';
}

.crew_image {
    box-shadow: -2px 3px 5px 0px rgba(0, 0, 0, 0.14);
    width: 35px;
    height: 35px;
}

.crew_image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 5px;
}

.trip_details .txt_field {
    background: none;
}

.trip_details .txt_field select.form-select {
    padding: 10px 15px;
    border-radius: 7px;
    border: 1px solid #C5C5C5;
}

.custom-template .select_template .custom_check_box {
    margin: 10px 0;
}

.google_map iframe {
    width: 100%;
    height: 400px;
    border-radius: 20px;
}

.custom_template {
    gap: 20px;
    flex-wrap: wrap;
    justify-content: space-between;
}

.custom_text .txt_description {
    margin-bottom: 20px;
}

.append_menu {
    display: flex;
    flex-direction: column;
    row-gap: 20px;
}

.add_btn button:has(i) i,
.add_sub_section.add_btn {
    margin: 0;
}

.onboarded_crew_list .custom_check_box label:before {
    height: 20px;
    width: 20px;
}

.onboarded_crew_list .custom_check_box input:checked+label:after {
    top: 10px;
    left: 8px;
    font-size: 12px;
}

.cms_section .custom_flex {
    flex-wrap: wrap;
}

.custom_margin {
    margin-top: 20px;
}

.trips_navigation ul {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 0;
    margin: 0;
    list-style: none;
}

#kt_app_wrapper .app-main .trips_navigation ul li .btn {
    padding: 10px 20px;
}

#kt_app_wrapper .app-main .trips_navigation ul li.tab.active .btn.btn_transparent {
    background: #03525A;
    color: white
}

.template_image {
    width: 280px;
    height: 260px;
}

.template_image img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    border-radius: 10px;
    border: 1px solid silver;
}

span.swiper-pagination-bullet.swiper-pagination-bullet {
    width: 18px;
    height: 18px;
    background: #00000073;
    opacity: unset;
}

span.swiper-pagination-bullet.swiper-pagination-bullet-active {
    background: var(--dark_green_clr);
}

.choose_template .swiper.mySwiper {
    width: 100%;
}

.location_section .custom_check_box {
    border-radius: 7px;
    border: 1px solid #C5C5C5;
    padding: 10px;
    margin-top: 30px;
}

.location_section .custom_check_box label {
    width: 100%;
}

.location_section input:checked+label:after {
    left: 6px;
    top: 9px;
}

#kt_app_wrapper .app-main .btn.add_text i {
    margin: 0;
}

.cms_section .txt_field:has(input.end-point:disabled),
.cms_section .txt_field:has(input.end-point:disabled) input,
.cms_section .txt_field:has(input.end-point:read-only),
.cms_section .txt_field:has(input.end-point:read-only) input {
    background: #e0e0e0;
}

/* Crew Modal */
.txt_field.profile_picture .profile_image {
    height: 100px;
    width: 100px;
    margin-left: 10px;
}

.txt_field.profile_picture .profile_image .image-input .image-input-wrapper {
    height: 100px;
}

.txt_field.profile_picture .profile_image label.edit_icon {
    right: -10px;
}

.txt_field.profile_picture .profile_image span.edit_icon {
    top: 65%;
    transform: unset;
    right: -10%;
}

.txt_field.profile_picture .profile_image label.edit_icon {
    transform: unset;
    top: 0;
}

.txt_field.profile_picture .profile_image label.edit_icon i>span:before {
    left: 10px;
}

#kt_app_wrapper .app-main .txt_field.profile_picture .profile_image .btn.edit_icon i {
    font-size: 10px;
}


/* Custom Select Two */
.custom_cards_design label,
.custom_cards_design .amount span {
    color: var(--black_clr);
    font-family: 'PlusJakartaSans-Bold';
    font-size: 15px;
}

.custom_cards_design .amount label {
    margin-right: 20px;
    margin-bottom: 20px;
}

.custom_select .select2 .custom_multi_select {
    border: 1px solid grey;
    margin: 10px 0;
    min-height: unset !Important;
}

.custom_select .select2 .custom_multi_select ul {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.custom_select .select2 .custom_multi_select ul>li {
    margin: 0 !important;
}

.select_modal {
    width: 100%;
}

.custom_select .select_modal .select2 .custom_multi_select {
    border: 0;
    margin: 0;
}

/*edit page choose section*/
.choose_section {
    display: flex;
    flex-direction: column;
    row-gap: 20px
}

.choose_section .custom_section {
    column-gap: 50px;
}

.choose_section .custom_section .select_section .custom_checkbox_wrapper {
    column-gap: 10px;
    display: flex;
    align-items: center;
}

.choose_section .custom_section .select_section .custom_checkbox_wrapper input[type="checkbox"] {
    border: 2px solid #027F8A;
    width: 20px;
    height: 20px;
    border-radius: 4px;
}

.choose_section .custom_section .select_section .custom_checkbox_wrapper .form-check-input:checked {
    background-color: #002768;
    background-size: 65% 65%;
    border: 0;
}

/*trip management*/
.trip_details .stepper_form_wrapper {
    display: flex;
    flex-direction: column;
    row-gap: 20px;
}

.upload_next_previous_wrapper {
    display: flex;
    justify-content: space-between;
}

.upload_next_previous_wrapper .next_previus_wrapper {
    display: flex;
    column-gap: 5px;
}

.View_map_btn_wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 30px;
    padding-top: 30px;
}

.View_map_btn_wrap h1 {
    margin: 0px;
}

/*dropzone css multiple image upload*/
.client_upload_img .dz-button {
    margin: auto;
}

.client_upload_img .dropzone.dz-clickable {
    border-radius: 7px;
    border: 1px dashed #C5C5C5;
    background: #FFF;
    padding: 15px 15px;
}

.client_upload_img .dz-button i {
    color: #4A4A4A;
    font-size: 16px;
}

.client_upload_img .dz-button h6 {
    color: #32346B;
    font-family: 'PlusJakartaSans-SemiBold';
    font-size: 14px;
    font-weight: 600;
    margin-top: 10px;
}

.client_upload_img .dz-button p {
    color: #4A4A4A;
    font-family: 'PlusJakartaSans-Regular';
    font-size: 10px;
    font-weight: 400;
    margin-top: 5px;
}

.client_upload_img .dz-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.client_upload_img .dz-div {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 100%;
    /* Ensure it takes full width */
}

.client_upload_img .dz-div i {
    color: #4A4A4A;
    font-size: 24px;
    /* Increase icon size for better visibility */
    margin-bottom: 5px;
    /* Add space below the icon */
}

.client_upload_img .dz-div h6 {
    color: #32346B;
    font-family: 'PlusJakartaSans-SemiBold', sans-serif;
    font-size: 14px;
    font-weight: 600;
    margin-top: 5px;
    margin-bottom: 3px;
    /* Add spacing for better look */
}

.client_upload_img .dz-div p {
    color: #4A4A4A;
    font-family: 'PlusJakartaSans-Regular', sans-serif;
    font-size: 10px;
    font-weight: 400;
    margin-top: 5px;
}

.client_upload_img .dz-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.client_upload_img .dz-preview .dz-error-mark {
    display: none;
}

.client_upload_img .dropzone .dz-preview .dz-remove {
    z-index: 999;
}

.client_upload_img .dropzone.dz-clickable.dz-started .dz-error-message {
    display: none;
}

.client_upload_img .dropzone .dz-preview .dz-details .dz-filename {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: unset;
    white-space: unset;
}

.client_upload_img .dropzone .dz-preview.dz-image-preview {
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.75);
}

/**/
/*trips data tables scroll*/
#kt_app_body table.extra_table_class_trips tbody tr td:nth-child(3) {
    word-wrap: break-word;
}

#kt_app_body table.extra_table_class_trips tbody tr td,
#kt_app_body table.extra_table_class_trips thead tr th {
    white-space: unset;
}

#kt_app_body table.custom_table_class_edit tbody tr td:nth-child(3) {
    word-wrap: break-word;
}

#kt_app_body table.custom_table_class_edit tbody tr td {
    white-space: unset;
}

.select_template_label {
    font-family: "PlusJakartaSans-SemiBold";
    color: #4A4A4A;
    font-size: 16px;
    display: flex;
    align-items: center;
    column-gap: 10px;
}

/*/ Switch Toggle Css /*/
.custom_switch_toggle {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.custom_switch_toggle .form-check.form-switch .form-check-input {
    width: 45px;
    height: 25px;
    margin: 0;
    border: 2px solid #4A4A4A;
}

.custom_switch_toggle .form-check.form-switch .form-check-input:checked~label {
    color: #4D5770;
}

.custom_switch_toggle label {
    color: #03525A;
    margin: 0;
    font-size: 18px;
}

.custom_switch_toggle .form-check-input:checked {
    background-color: #03525A;
}

.custom_switch_toggle .form-check.form-switch {
    display: flex;
    align-items: center;
    gap: 15px;
    justify-content: center;
    padding: 0;
    margin: 0;
}

.custom_switch_toggle label i {
    margin-left: 10px;
    color: #03525A;
    font-size: 20px;
}

.custom_image_toggle.custom_cards_design {
    margin: 20px 0;
}

/* upload Pdf */
.upload_pdf .append_type_file input.file-input {
    z-index: 1;
    opacity: 0;
    position: absolute;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    cursor: pointer;
    border-radius: 8px;
}

.upload_pdf .append_type_file {
    border: 1px solid #03525A;
    position: relative;
    width: 145px;
    height: 145px;
    overflow: visible;
    cursor: pointer;
    text-align: center;
    border-radius: 8px;
    margin-top: 10px;
}

.upload_pdf button.add_image {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    color: white;
    font-size: 16px;
    border: 0;
    bottom: 0;
    padding: 0;
    background: #03525A;
    border-radius: 8px;
}

.upload_pdf button.remove_btn {
    border: 0;
    position: absolute;
    right: -15px;
    top: -15px;
    background: black;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.upload_pdf button.remove_btn i {
    color: white;
}

.upload_pdf button.add_image i {
    color: white;
    margin-right: 5px;
}

.upload_pdf .append_type_file:has(.image_structure) button.add_image {
    display: none;
}

.upload_pdf .image_structure {
    height: 145px;
    width: 143px;
}

.upload_pdf .image_structure img {
    height: 100%;
    width: 100%;
    object-fit: cover;
    border-radius: 15px;
}

.upload_pdf .pdf_img {
    height: 142px;
    width: 143px;
}

.upload_pdf .image_structure a.preview_pdf {
    color: #d20000;
    font-size: 16px;
}

/*generat ai*/
#kt_app_wrapper .generate_ai_btn img {
    object-fit: contain;
    height: 20px;
    width: 20px;
    object-position: center;
}

#kt_app_wrapper .generate_ai_btn {
    align-items: center;
    justify-content: center;
    display: flex;
    column-gap: 5px
}

.ai_trip_stop_append_wrapper {
    display: flex;
    column-gap: 10px;
}

.ai_trip_stop_append_wrapper .txt_field {
    flex: 1;
}

.ai_trip_stop_append_wrapper .delete_stop_all_styling {
    width: 10%;
    max-width: 10%;
    border: 0px;
    border-radius: 5px;
    background-color: #6C6C6C;
}

.ai_trip_stop_append_wrapper .delete_stop_all_styling i {
    color: #fff;
    font-size: 14px;
    ont-weight: 400;
}

.stop_whole_wrap {
    display: flex;
    flex-direction: column;
    row-gap: 10px;
}

.ai_trip_stop_append_wrapper .ai_trip_stop_delete {
    background-color: rgba(239, 68, 68, 0.80);
}

.create_trip_ai .modal-dialog.modal-dialog-centered {
    max-width: 60%;
}

.my_trip_details_stop {
    display: flex;
    flex-wrap: wrap;
    align-items: end;
    row-gap: 20px;
    column-gap: 60px;
    justify-content: center;
    justify-content: center;
    margin: 20px 0px 0px 28px;
}

/*.my_trip_details_stop .stop_location{ display:flex;  flex-wrap: wrap;gap:10px;justify-content: center;}*/
.my_trip_box h2 {
    border: 1px solid #03525A;
    padding: 10px 20px;
    border-radius: 10px;
}

.my_trip_box.start h2,
.my_trip_box.end h2 {
    background-color: #03525A;
    ;
    height: fit-content;
    color: #fff
}

.my_trip_custom_column {
    display: none;
}

.my_trip_box.end,
.my_trip_box.start {
    display: flex;
    flex-direction: column;
}

.my_trip_heading {
    font-size: 30px;
}

.my_trip_box.stop_location:before {
    position: absolute;
    content: "---- >";
    bottom: 0;
    top: 29%;
    left: -51px;
    color: #03525A;
    font-weight: 900;
    font-size: 15px;
}

.my_trip_box.end:before {
    position: absolute;
    content: "---- >";
    bottom: 0;
    top: 50%;
    left: -50px;
    color: #03525A;
    font-weight: 900;
    font-size: 15px;
}

.stop_location {position: relative;}
.my_trip_box.end {position: relative;}
.itineraries_modal_body{display:flex;flex-direction:column;row-gap:20px;}

#kt_app_body .modal .btn.btn_dark_green {
    background: var(--dark_green_clr);
    color: var(--white_clr);
    border: 1px solid var(--dark_green_clr);
}

#kt_app_body .modal .btn.btn_dark_green:hover {
    background: transparent;
    border: 1px solid var(--dark_green_clr);
    color: var(--dark_green_clr);
}

#kt_app_body .modal .generate_ai_btn img {
    object-fit: contain;
    height: 20px;
    width: 20px;
    object-position: center;
}

#kt_app_body .modal .btn.btn_green {
    background: var(--green_clr);
    color: #4a4a4a;
    border: 1px solid var(--green_clr);
}

#kt_app_body .modal .btn.btn_transparent {
    background: transparent;
    color: var(--dark_green_clr);
    border: 1px solid var(--dark_green_clr);
    box-shadow: 0px 15px 30px 0px rgba(58, 153, 74, 0.18);
}

#kt_app_body .modal .btn {
    padding: 10px 30px;
    border-radius: 8px;
    font-family: 'PlusJakartaSans-SemiBold';
    line-height: 24px;
    font-size: 14px;
}

body:has(#create_trip_ai.show) .pac-container {
    z-index: 999999;
}

/* Owner Subscription */
.active_subscription {
    display: flex;
    flex-direction: column;
    height: unset;
    gap: 50px;
    padding: 50px 20px;
}

.owner_subscription .basic_packg_sec {
    border-radius: 15px;
    background: #995d037a;
    box-shadow: 0px 7.316px 27.436px 0px rgba(0, 0, 0, 0.15);
    padding: 20px;
    position: relative;
}

.owner_subscription .basic_packg_sec .numbering span {
    border-radius: 50%;
    color: #995d037a;
    background: white;
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
    position: absolute;
    top: -20px;
    left: 0;
    right: 0;
    font-size: 30px;
}

.numbering {
    padding-bottom: 30px;
}

.subscription_wrapper.custom_cards_design {}

.owner_subscription {
    margin-top: 50px;
}

.susbcription_name {
    text-align: center;
    margin: 20px 0;
}

.subscription_listing ul {
    margin: 0;
}

.subscription_detail h1:last-child {
    margin-top: 15px;
}

.subscription_listing ul li:has(ol) ol {
    margin: 20px 0;
}

.subscription_listing ul li {
    font-family: 'PlusJakartaSans-Regular';
    font-size: 16px;
    margin-bottom: 10px;
    letter-spacing: 1px;
}

.owner_subscription .basic_packg_sec.silver_packg {
    background: #d7d7d7;
}

.owner_subscription .basic_packg_sec.premium_packg {
    background: #d1b473f5;
}

.owner_subscription .basic_packg_sec.silver_packg .numbering span {
    color: #d7d7d7;
}

.owner_subscription .basic_packg_sec.premium_packg .numbering span {
    color: #d1b473f5;
}

.package_pricing {
    text-align: center;
    margin: 15px 0;
}

.subscription_plans {
    padding: 15px;
    background: #995d037a;
    border-radius: 10px;
    margin-top: 25px;
    text-align: center;
}

.owner_subscription .basic_packg_sec.silver_packg .subscription_plans {
    background: #bab7b7de;
}

.owner_subscription .basic_packg_sec.premium_packg .subscription_plans {
    background: #e3d2ab91;
    ;
}

.subscription_plans .custom_switch_toggle {
    gap: 15px;
    justify-content: center;
    margin: 10px 0 20px 0;
}

.subscription_plans .custom_switch_toggle img {
    height: 35px;
    width: 30px;
}

.basic_packg_sec .custom_switch_toggle .form-check-input:checked {
    background-color: #995d037a;
}

.subscription_plans .custom_switch_toggle label {
    color: black;
}

#kt_app_wrapper .app-main .active_btn button.btn.btn_transparent {
    border: 1px solid white;
    color: black;
}

#kt_app_wrapper .app-main .active_btn button.btn.btn_transparent:hover {
    background: transparent;
}

.select_subscription_plan {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    gap: 20px;
}

.select_subscription_plan .modify_plan {width: 100%;}
.select_subscription_plan .modify_plan label {margin-bottom: 10px;}

.create_trip_modal.custom_trip_modal .modal-dialog.modal-dialog-centered {
    max-width: 70%;
}

/* Loader Css */
.loader {
    width: 60px;
    height: 60px;
    border: 4px dotted #000000;
    border-style: solid solid dotted dotted;
    border-radius: 50%;
    animation: rotation 2s linear infinite;
    position: relative;
}

.loader::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    border: 4px dotted #06b6c8;
    border-style: solid solid dotted dotted;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    animation: rotationBack 1s linear infinite;
}

@keyframes rotation {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes rotationBack {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(-360deg);
    }
}
