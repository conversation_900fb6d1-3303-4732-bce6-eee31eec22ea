<!--begin::Sidebar-->
<div id="kt_app_sidebar" class="app-sidebar flex-column sidebar_wrapper" data-kt-drawer="true"
    data-kt-drawer-name="app-sidebar" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true"
    data-kt-drawer-width="225px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    <!--begin::Logo-->
    <div class="app-sidebar-logo" id="kt_app_sidebar_logo">
        <!--begin::Logo image-->
        <a href="<?php echo e(url('/dashboard_index')); ?>">
            <img alt="Logo" src="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->logo ?? ''); ?>"
                class="app-sidebar-logo-default" />
            <img alt="Logo" src="<?php echo e(asset('website')); ?>/assets/media/logos/default-small.svg"
                class="h-20px app-sidebar-logo-minimize" />
        </a>
        <!--end::Logo image-->
        <!--begin::Sidebar toggle-->
        <!--begin::Minimized sidebar setup:
     if (isset($_COOKIE["sidebar_minimize_state"]) && $_COOKIE["sidebar_minimize_state"] === "on") {
     1. "src/js/layout/sidebar.js" adds "sidebar_minimize_state" cookie value to save the sidebar minimize state.
     2. Set data-kt-app-sidebar-minimize="on" attribute for body tag.
     3. Set data-kt-toggle-state="active" attribute to the toggle element with "kt_app_sidebar_toggle" id.
     4. Add "active" class to to sidebar toggle element with "kt_app_sidebar_toggle" id.
     }
     -->
        <div id="kt_app_sidebar_toggle"
            class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary h-30px w-30px position-absolute top-50 start-100 translate-middle rotate"
            data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body"
            data-kt-toggle-name="app-sidebar-minimize">
            <i class="ki-duotone ki-black-left-line fs-3 rotate-180">
                <span class="path1"></span>
                <span class="path2"></span>
            </i>
        </div>
        <!--end::Sidebar toggle-->
    </div>
    <!--end::Logo-->

    <!--begin::sidebar menu-->
    <div class="app-sidebar-menu overflow-hidden flex-column-fluid">
        <!--begin::Menu wrapper-->
        <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper">
            <!--begin::Scroll wrapper-->
            <div id="kt_app_sidebar_menu_scroll" class="scroll-y" data-kt-scroll="true" data-kt-scroll-activate="true"
                data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer"
                data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px"
                data-kt-scroll-save-state="true">
                <!--begin::Menu-->
                <div class="menu menu-column menu-rounded menu-sub-indention custom_sidebar_menu"
                    id="#kt_app_sidebar_menu" data-kt-menu="true" data-kt-menu-expand="false">
                    <!--begin:Menu item-->
                    <!--begin:Menu item-->
                    <div class="menu-item ">
                        <div class="menu-content">
                            <a href="<?php echo e(url('dashboard_index')); ?>"
                                class="nav_list <?php echo e(request()->is('dashboard_index*') ? 'active' : ''); ?>"
                                aria-current="page">
                                <div class="sidebar_icon"><i class="fa-solid fa-house"></i></div>
                                Dashboard
                            </a>
                        </div>
                    </div>
                    <?php if(auth()->user()->hasRole('developer')): ?>
                        <div class="menu-item pt-5">
                            <!--begin:Menu content-->
                            <div class="menu-content">
                                <span class="menu-heading fw-bold text-uppercase fs-7">Developer</span>
                            </div>
                            <!--end:Menu content-->
                        </div>
                        <!--end:Menu item-->
                        <!--begin:Menu item-->
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                            <!--begin:Menu link-->
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="ki-duotone ki-abstract-28 fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <span class="menu-title">User Management</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <!--end:Menu link-->
                            <!--begin:Menu sub-->
                            <div class="menu-sub menu-sub-accordion">
                                <!--begin:Menu item-->

                                
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                    <!--begin:Menu link-->

                                    
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('crud-list')): ?>
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">CRUD</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    <?php endif; ?>
                                    <!--end:Menu link-->
                                    <!--begin:Menu sub-->
                                    <div class="menu-sub menu-sub-accordion">
                                        <!--begin:Menu item-->
                                        <div class="menu-item">
                                            <!--begin:Menu link-->
                                            <a class="menu-link" href="<?php echo e(url('crud_generator')); ?>">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">CRUD Generator</span>
                                            </a>
                                            <!--end:Menu link-->
                                        </div>
                                        <!--end:Menu item-->
                                    </div>
                                    <!--end:Menu sub-->
                                </div>
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                    <!--begin:Menu link-->
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('user-list')): ?>
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">Users</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    <?php endif; ?>
                                    <!--end:Menu link-->
                                    <!--begin:Menu sub-->
                                    <div class="menu-sub menu-sub-accordion">
                                        <!--begin:Menu item-->
                                        <div class="menu-item">
                                            <!--begin:Menu link-->
                                            <a class="menu-link" href="<?php echo e(url('users')); ?>">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">Users List</span>
                                            </a>
                                            <!--end:Menu link-->
                                        </div>
                                        <!--end:Menu item-->
                                    </div>
                                    <!--end:Menu sub-->
                                </div>
                                <!--end:Menu item-->
                                <!--begin:Menu item-->
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                    <!--begin:Menu link-->
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('role-list')): ?>
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">Roles</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    <?php endif; ?>
                                    <!--end:Menu link-->
                                    <!--begin:Menu sub-->
                                    <div class="menu-sub menu-sub-accordion">
                                        <!--begin:Menu item-->
                                        <div class="menu-item">
                                            <!--begin:Menu link-->
                                            <a class="menu-link" href="<?php echo e(url('roles')); ?>">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">Roles List</span>
                                            </a>
                                            <!--end:Menu link-->
                                        </div>
                                        <!--end:Menu item-->
                                    </div>
                                    <!--end:Menu sub-->
                                </div>
                                <!--end:Menu item-->
                                <!--begin:Menu item-->
                                <div class="menu-item">
                                    <!--begin:Menu link-->
                                    <a class="menu-link" href="javascript:void(0);">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="menu-title">Permissions</span>
                                    </a>
                                    <!--end:Menu link-->
                                </div>
                                <!--end:Menu item-->
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('settings-list')): ?>
                                    <div class="menu-item">
                                        <!--begin:Menu link-->
                                        <a class="menu-link" href="<?php echo e(url('settings')); ?>">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">Settings</span>
                                        </a>
                                        <!--end:Menu link-->
                                    </div>
                                <?php endif; ?>
                            </div>
                            <!--end:Menu sub-->
                        </div>
                        <hr>
                        <div class="menu-item pt-3">
                            <div class="menu-content">
                                <span class="menu-heading fw-bold text-uppercase fs-7">Menu</span>
                            </div>
                        </div>
                        <?php $__currentLoopData = $crud; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check($item->url . '-list')): ?>
                                <?php if(in_array($item->url, ['homes', 'abouts', 'ourservices', 'contacts', 'pricings'])): ?>
                                    <?php continue; ?>
                                <?php endif; ?>

                                
                                <div class="menu-item">
                                    <!--begin:Menu link-->
                                    <a class="menu-link <?php echo e(request()->is($item->url) ? 'active' : ''); ?>"
                                        href="<?php echo e(url($item->url ?? 'home')); ?>">
                                        <span class="menu-icon">
                                            <i class="ki-duotone ki-abstract-28 fs-2">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </span>
                                        <span
                                            class="menu-title"><?php echo e(preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', $item->name)); ?></span>
                                    </a>
                                    <!--end:Menu link-->
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <hr>
                        <div class="menu-item pt-3">
                            <div class="menu-content">
                                <span class="menu-heading fw-bold text-uppercase fs-7">CMS</span>
                            </div>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link <?php echo e(request()->is('cms/homes') ? 'active' : ''); ?>" href="<?php echo e(url('cms/homes')); ?>">
                                <span class="menu-icon">
                                    <i class="ki-duotone ki-abstract-28 fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <span class="menu-title">Home</span>
                            </a>
                            <a class="menu-link <?php echo e(request()->is('cms/abouts') ? 'active' : ''); ?>" href="<?php echo e(url('cms/abouts')); ?>">
                                <span class="menu-icon">
                                    <i class="ki-duotone ki-abstract-28 fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <span class="menu-title">About</span>
                            </a>
                            <a class="menu-link <?php echo e(request()->is('cms/ourservices') ? 'active' : ''); ?>" href="<?php echo e(url('cms/ourservices')); ?>">
                                <span class="menu-icon">
                                    <i class="ki-duotone ki-abstract-28 fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <span class="menu-title">Our Service</span>
                            </a>
                            <a class="menu-link <?php echo e(request()->is('cms/contacts') ? 'active' : ''); ?>" href="<?php echo e(url('cms/contacts')); ?>">
                                <span class="menu-icon">
                                    <i class="ki-duotone ki-abstract-28 fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <span class="menu-title">Contact</span>
                            </a>
                            <a class="menu-link <?php echo e(request()->is('cms/pricings') ? 'active' : ''); ?>" href="<?php echo e(url('cms/pricings')); ?>">
                                <span class="menu-icon">
                                    <i class="ki-duotone ki-abstract-28 fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <span class="menu-title">Pricing</span>
                            </a>
                        </div>
                    <?php elseif(auth()->user()->hasRole('admin')): ?>
                        <div class="menu-item ">
                            <div class="menu-content">
                                <a href="<?php echo e(route('trips.index')); ?>"
                                    class="nav_list <?php if(request()->route()->getName() == 'trips.index' || request()->route()->getName() == 'trips.show'): ?> active <?php endif; ?>"
                                    aria-current="page">
                                    <div class="sidebar_icon"><i class="fa-solid fa-suitcase-rolling"></i></div>
                                    Trips Management
                                </a>
                            </div>
                        </div>
                        <div class="menu-item ">
                            <div class="menu-content">
                                
                                
                                
                                
                                <!--begin:Menu link-->
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('user-list')): ?>
                                    <a href="<?php echo e(url('users')); ?>"
                                        class="nav_list <?php if(request()->route()->getName() == 'users.index' || request()->route()->getName() == 'users.show'): ?> active <?php endif; ?>"
                                        aria-current="page">
                                        <div class="sidebar_icon"><i class="fa-solid fa-users"></i></div>
                                        User Management
                                        
                                        
                                    </a>
                                <?php endif; ?>
                                <!--end:Menu link-->
                                <!--begin:Menu sub-->
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                <!--end:Menu sub-->
                            </div>
                        </div>
                        <div class="menu-item ">
                            <div class="menu-content">
                                <a href="<?php echo e(route('packages.index')); ?>"
                                    class="nav_list <?php if(request()->route()->getName() == 'packages.index'): ?> active <?php endif; ?>"
                                    aria-current="page">
                                    <div class="sidebar_icon"><i class="fa-solid fa-dollar-sign"></i></div>
                                    Subscription Management
                                </a>
                            </div>
                        </div>
                        <div class="menu-item ">
                            <div class="menu-content">
                                <a href="<?php echo e(route('subscriptions.index')); ?>"
                                    class="nav_list <?php if(request()->route()->getName() == 'subscriptions.index'): ?> active <?php endif; ?>"
                                    aria-current="page">
                                    <div class="sidebar_icon"><i class="fa-solid fa-money-check"></i></div>
                                    Payment Management
                                </a>
                            </div>
                        </div>
                        <div class="menu-item ">
                            <div class="menu-content">
                                <a href="<?php echo e(route('homes.edit', 1)); ?>"
                                    class="nav_list <?php echo e(request()->routeIs(['homes.edit', 'abouts.edit', 'ourservices.edit', 'contacts.edit', 'pricings.edit']) ? 'active' : ''); ?>"
                                    aria-current="page">
                                    <div class="sidebar_icon"><i class="fa-solid fa-graduation-cap"></i></div>
                                    CMS
                                </a>
                            </div>
                        </div>
                    <?php elseif(auth()->user()->hasRole('owner')): ?>
                        <div class="menu-item ">
                            <div class="menu-content">
                                <a href="<?php echo e(route('trips.index')); ?>"
                                    class="nav_list <?php echo e(request()->route()->getName() == 'trips.index' || request()->route()->getName() == 'trips.show' || request()->route()->getName() == 'trips.edit' ? 'active' : ''); ?>"
                                    aria-current="page">
                                    <div class="sidebar_icon"><i class="fa-solid fa-suitcase-rolling"></i></div>
                                    Trip Management
                                </a>
                            </div>
                        </div>
                        
                        <div class="menu-item ">
                            <div class="menu-content">
                                <a href="<?php echo e(route('packages.index')); ?>"
                                    class="nav_list <?php if(request()->route()->getName() == 'packages.index'): ?> active <?php endif; ?>"
                                    aria-current="page">
                                    <div class="sidebar_icon"><i class="fa-solid fa-dollar-sign"></i></div>
                                    Subscription Management
                                </a>
                            </div>
                        </div>
                        <div class="menu-item ">
                            <div class="menu-content">
                                <a href="<?php echo e(route('crews.index')); ?>"
                                    class="nav_list <?php if(request()->route()->getName() == 'crews.index' || request()->route()->getName() == 'crews.show'): ?> active <?php endif; ?>"
                                    aria-current="page">
                                    <div class="sidebar_icon"><i class="fa-solid fa-users"></i></div>
                                    Crew Management
                                </a>
                            </div>
                        </div>
                        <div class="menu-item ">
                            <div class="menu-content">
                                <a href="<?php echo e(route('subscriptions.index')); ?>"
                                    class="nav_list <?php if(request()->route()->getName() == 'subscriptions.index'): ?> active <?php endif; ?>"
                                    aria-current="page">
                                    <div class="sidebar_icon"><i class="fa-solid fa-money-check"></i></div>
                                    Payment Management
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                    <div class="menu-item ">
                        <div class="menu-content logout ">
                            <a href="<?php echo e(url('logout')); ?>" class="<?php echo e(request()->is('logout') ? 'active' : ''); ?>"
                                aria-current="page">
                                <div class="sidebar_icon"><i class="fa-solid fa-home"></i></div>
                                Logout
                            </a>
                        </div>
                    </div>
                </div>

                <!--end::Menu-->
            </div>
            <!--end::Scroll wrapper-->
        </div>
        <!--end::Menu wrapper-->
    </div>
    <!--end::sidebar menu-->
    <!--begin::Footer-->
    <?php if(auth()->user()->hasRole('developer')): ?>
        <div class="app-sidebar-footer flex-column-auto pt-2 pb-6 px-6" id="kt_app_sidebar_footer">
            <a href="<?php echo e(url('html/demo1/dist')); ?>"
                class="btn btn-flex flex-center btn-custom btn-primary overflow-hidden text-nowrap px-0 h-40px w-100"
                data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-dismiss-="click"
                title="200+ in-house components and 3rd-party plugins">
                <span class="btn-label">Docs & Components</span>
                <i class="ki-duotone ki-document btn-icon fs-2 m-0">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i>
            </a>
        </div>
    <?php endif; ?>
    <!--end::Footer-->
</div>
<!--end::Sidebar-->
<?php /**PATH D:\guesttrip\resources\views\theme\layout\sidebar.blade.php ENDPATH**/ ?>