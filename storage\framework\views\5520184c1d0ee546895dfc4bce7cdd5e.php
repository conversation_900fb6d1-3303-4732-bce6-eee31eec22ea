
<?php $__env->startPush('css'); ?>
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <main class="main_section_background">
        
        <section class="hero_section">
            <div class="container custom_container">
                <div class="row">
                    <div class="col-md-6">
                        <div class="hero_content">
                            <?php if(isset($trip->mainHeader)): ?>
                                <h6><?php echo e($trip->mainHeader->kicker ?? ''); ?></h6>
                                <h1><?php echo e($trip->mainHeader->heading ?? ''); ?></h1>
                                <h4><span><?php echo e($trip->mainHeader->sub_heading ?? ''); ?></span></h4>
                                <h5><?php echo e($trip->mainHeader->description ?? ''); ?></h5>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="multi_images_section">
                            <div class="row custom_row">
                                <div class="col-md-7">
                                    <div class="custom_images">
                                        <?php if(isset($trip->mainHeaderImages) && isset($trip->mainHeaderImages[0])): ?>
                                            <img src="<?php echo e(asset('website/' . $trip->mainHeaderImages[0]->image)); ?>"
                                                alt="Image 1">
                                        <?php else: ?>
                                            <img src="<?php echo e(asset('website')); ?>/assets/images/custom_ship_img.png">
                                        <?php endif; ?>

                                    </div>
                                </div>
                                <div class="col-md-5">
                                    <div class="custom_images">
                                        <?php if(isset($trip->mainHeaderImages) && isset($trip->mainHeaderImages[1])): ?>
                                            <img src="<?php echo e(asset('website/' . $trip->mainHeaderImages[1]->image)); ?>"
                                                alt="Image 1">
                                        <?php else: ?>
                                            <img src="<?php echo e(asset('website')); ?>/assets/images/ship_sky_img.png">
                                        <?php endif; ?>

                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="swiper itenarySwiper">
                                        <div class="swiper-wrapper">
                                            <?php if(isset($trip->mainHeaderImages)): ?>
                                                <?php $__currentLoopData = $trip->mainHeaderImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php if($index >= 2): ?>
                                                        <div class="swiper-slide">
                                                            <div class="slider_images">
                                                                <img src="<?php echo e(asset('website/' . $image->image)); ?>"
                                                                    alt="Image <?php echo e($index + 1); ?>">
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                    
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="swiper-pagination"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="itinerary_summary custom_padding">
            <div class="container custom_container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="custom_image_card_content">
                            <?php if(isset($trip->tripItinerarySummary)): ?>
                                <h6><?php echo e($trip->tripItinerarySummary->kicker ?? ''); ?></h6>
                                <h2><?php echo e($trip->tripItinerarySummary->heading ?? ''); ?></h2>
                                <h6><?php echo e($trip->tripItinerarySummary->sub_heading ?? ''); ?></h6>
                            <?php endif; ?>

                            <div class="swiper crewCardSwiper">
                                <div class="swiper-wrapper">
                                    <?php if(isset($trip->itineraries)): ?>
                                        <?php $__currentLoopData = $trip->itineraries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $itinerary): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="swiper-slide">
                                                <div class="custom_img_cards">
                                                    <div class="custom_card">
                                                        <div class="card_img">
                                                            <?php if(isset($itinerary->images) && isset($itinerary->images[0])): ?>
                                                                <img src="<?php echo e(asset('website/' . $itinerary->images[0]->image)); ?>"
                                                                    alt="Image 1">
                                                            <?php else: ?>
                                                                <img
                                                                    src="<?php echo e(asset('website')); ?>/assets/images/mountain_img.png">
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="card_content">
                                                            <h3><?php echo e($itinerary->duration ?? ''); ?></h3>
                                                            <h4><?php echo e($itinerary->start_point ?? ''); ?> -
                                                                <?php echo e($itinerary->end_point ?? ''); ?></h4>
                                                            <h6><?php echo e($itinerary->description ?? ''); ?></h6>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                </div>
                                <div class="swiper-button-next"></div>
                                <div class="swiper-button-prev"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="image_fixed_sec" style="">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-12">
                        <div class="image_fixed_wrapper" style="">
                            <img src="<?php echo e(asset('website')); ?>/assets/images/section_img.png"
                                style="">
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="google_map_sec">
            <div class="container custom_container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="custom_image_card_content">
                            <?php if(isset($trip->tripItineraryMap)): ?>
                                <h6><?php echo e($trip->tripItineraryMap->kicker ?? ''); ?></h6>
                                <h2><?php echo e($trip->tripItineraryMap->heading ?? ''); ?></h2>
                                <h6><?php echo e($trip->tripItineraryMap->sub_heading ?? ''); ?></h6>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="google_map_wrapper">
                            
                            <div id="map" style="width: 100%; height: 500px;"></div>
                            <div class="google_map_modal" id="modals"></div>
                            <div class="dist_time_whea_wrapper">
                                <div class="google_distance_wrapper">
                                    <i class="fa-solid fa-location-arrow"></i>
                                    <div class="">
                                        <h5>Distance</h5>
                                        <h3>300 KM</h3>
                                    </div>
                                </div>
                                <div class="google_distance_wrapper">
                                    <i class="fa-regular fa-clock"></i>
                                    <div class="">
                                        <h5>Time</h5>
                                        <h3>2h 15m</h3>
                                    </div>
                                </div>
                                <div class="google_distance_wrapper">
                                    <i class="fa-solid fa-cloud"></i>
                                    <div class="">
                                        <h5>Weather</h5>
                                        <h3>32 ℃</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <script>
        // Sample Itinerary data
        let itineraries = [{
                "id": 6,
                "start_lat": "24.917218",
                "start_lng": "67.0923866",
                "end_lat": "27.7243563",
                "end_lng": "68.8228082",
                "start_point": "Gulshan-e-Iqbal, Karachi, Pakistan",
                "end_point": "Gadap Town, Karachi, Pakistan"
            },
            {
                "id": 7,
                "start_lat": "24.917218",
                "start_lng": "67.0923866",
                "end_lat": "27.7243563",
                "end_lng": "68.8228082",
                "start_point": "Gadap Town, Karachi, Pakistan",
                "end_point": "Hyderabad, Pakistan"
            },
            {
                "id": 8,
                "start_lat": "24.917218",
                "start_lng": "67.0923866",
                "end_lat": "27.7243563",
                "end_lng": "68.8228082",
                "start_point": "Hyderabad, Pakistan",
                "end_point": "Sukkur, Pakistan"
            }
        ];

        // Initialize the map centered at the first itinerary's start point
        const map = L.map('map').setView([parseFloat(itineraries[0].start_lat), parseFloat(itineraries[0].start_lng)], 8);

        // Set the tile layer (this is the map's background)
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Loop through each itinerary and add markers for start and end points
        itineraries.forEach(itinerary => {
            // Start point coordinates
            const startLatLng = [parseFloat(itinerary.start_lat), parseFloat(itinerary.start_lng)];
            // End point coordinates
            const endLatLng = [parseFloat(itinerary.end_lat), parseFloat(itinerary.end_lng)];

            // Add a marker for the start point with a popup
            L.marker(startLatLng).addTo(map).bindPopup(itinerary.start_point);

            // Add a marker for the end point with a popup
            L.marker(endLatLng).addTo(map).bindPopup(itinerary.end_point);
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master_trip', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\website\packages_templates\VariationOne\itinerary_overviewNEW.blade.php ENDPATH**/ ?>