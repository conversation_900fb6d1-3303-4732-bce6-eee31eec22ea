

<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>

    <section class="content_management">
        <div class="container-fluid">
            <div class="row custom_row">
                <div class="col-md-12">
                    <div class="cms_tabs">
                        <ul class="nav nav-pills" id="pills-tab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">Home</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-company-tab" data-bs-toggle="pill" data-bs-target="#pills-company" type="button" role="tab" aria-controls="pills-company" aria-selected="false">Company</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-pricing-tab" data-bs-toggle="pill" data-bs-target="#pills-pricing" type="button" role="tab" aria-controls="pills-pricing" aria-selected="false">Pricing</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-services-tab" data-bs-toggle="pill" data-bs-target="#pills-services" type="button" role="tab" aria-controls="pills-services" aria-selected="false">Services</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="pills-contact-tab" data-bs-toggle="pill" data-bs-target="#pills-contact" type="button" role="tab" aria-controls="pills-contact" aria-selected="false">Contact Us</button>
                            </li>
                        </ul>
                    </div>
                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab" tabindex="0">
                            <form>
                                <div class="row custom_row">
                                    <div class="col-md-12">
                                        <div class="cms_edit_btn">
                                            <button type="button" id="edit" class="btn btn_dark_green"><i class="fa-solid fa-pen-to-square"></i>Edit</button>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="cms_section custom_cards_design">
                                            <h1>Section 01: Main Header</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Kicker</label>
                                                        <input type="text" class="form-control myinput" value="One Week Itinerary for our Our VIP Client" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" class="form-control myinput" value="Discover Thailand" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="txt_field txt_description">
                                                        <label>Description</label>
                                                        <textarea class="form-control myinput" rows="3" placeholder="" readonly>We’re more connected than ever. More powerful, more creative and more aware with how information is shared and distributed. Yacht IQ builds on years of inventive solutions to bring you a simple yet sophisticated set of applications to help keep you informed and connected while onboard. With over 13 years of experience delivering cutting-edge IT solutions and delivering custom software solutions to UHNW clients, Yacht-IQ is what you need</textarea>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <h3>Background Image</h3>
                                                    <div class="custom_profile_upload custom_flex">
                                                        <div class="profile_picture">
                                                            <div class="profile_image">
                                                                <!--begin::Image input-->
                                                                <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                    <!--begin::Image preview wrapper-->
                                                                    <div class="image-input-wrapper">
                                                                        <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png">
                                                                    </div>
                                                                    <!--end::Image preview wrapper-->

                                                                    <!--begin::Edit button-->
                                                                    <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                           data-kt-image-input-action="change"
                                                                           data-bs-toggle="tooltip"
                                                                           data-bs-dismiss="click"
                                                                           title="Change avatar">
                                                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                        <!--begin::Inputs-->
                                                                        <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                        <input type="hidden" name="avatar_remove" />
                                                                        <!--end::Inputs-->
                                                                    </label>
                                                                    <!--end::Edit button-->

                                                                    <!--begin::Cancel button-->
                                                                    <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                          data-kt-image-input-action="cancel"
                                                                          data-bs-toggle="tooltip"
                                                                          data-bs-dismiss="click"
                                                                          title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                    <!--end::Cancel button-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="cms_section custom_cards_design">
                                            <h1>Section 02 : About Us</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Kicker</label>
                                                        <input type="text" class="form-control myinput" value="One Week Itinerary for our Our VIP Client" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" class="form-control myinput" value="Discover Thailand" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Sub Heading</label>
                                                        <input type="text" class="form-control myinput" value="From 23 Jan 2023 to 30 Jan 2023 on board SUPER YACHT" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-6"></div>
                                                <div class="col-md-12">
                                                    <div class="txt_field txt_description">
                                                        <label>Description</label>
                                                        <textarea class="form-control myinput" rows="3" placeholder="" readonly>With a deep passion for yacht management, we bring years of experience to ensure your journey is seamless and unforgettable. Our expert team handles everything, from meticulous planning to ensuring your yacht is fully prepared for the voyage.</textarea>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <h3>Images</h3>
                                                    <div class="custom_profile_upload custom_flex">
                                                        <div class="profile_picture">
                                                            <div class="profile_image">
                                                                <!--begin::Image input-->
                                                                <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                    <!--begin::Image preview wrapper-->
                                                                    <div class="image-input-wrapper">
                                                                        <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage6.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage6.png">
                                                                    </div>
                                                                    <!--end::Image preview wrapper-->

                                                                    <!--begin::Edit button-->
                                                                    <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                           data-kt-image-input-action="change"
                                                                           data-bs-toggle="tooltip"
                                                                           data-bs-dismiss="click"
                                                                           title="Change avatar">
                                                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                        <!--begin::Inputs-->
                                                                        <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                        <input type="hidden" name="avatar_remove" />
                                                                        <!--end::Inputs-->
                                                                    </label>
                                                                    <!--end::Edit button-->

                                                                    <!--begin::Cancel button-->
                                                                    <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                          data-kt-image-input-action="cancel"
                                                                          data-bs-toggle="tooltip"
                                                                          data-bs-dismiss="click"
                                                                          title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                    <!--end::Cancel button-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="profile_picture">
                                                            <div class="profile_image">
                                                                <!--begin::Image input-->
                                                                <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                    <!--begin::Image preview wrapper-->
                                                                    <div class="image-input-wrapper">
                                                                        <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage4.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage4.png">
                                                                    </div>
                                                                    <!--end::Image preview wrapper-->

                                                                    <!--begin::Edit button-->
                                                                    <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                           data-kt-image-input-action="change"
                                                                           data-bs-toggle="tooltip"
                                                                           data-bs-dismiss="click"
                                                                           title="Change avatar">
                                                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                        <!--begin::Inputs-->
                                                                        <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                        <input type="hidden" name="avatar_remove" />
                                                                        <!--end::Inputs-->
                                                                    </label>
                                                                    <!--end::Edit button-->

                                                                    <!--begin::Cancel button-->
                                                                    <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                          data-kt-image-input-action="cancel"
                                                                          data-bs-toggle="tooltip"
                                                                          data-bs-dismiss="click"
                                                                          title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                    <!--end::Cancel button-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="cms_section custom_cards_design">
                                            <h1>Section 03: Services</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Kicker</label>
                                                        <input type="text" class="form-control myinput" value="One Week Itinerary for our Our VIP Client" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" class="form-control myinput" value="Discover Thailand" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="txt_field txt_description">
                                                        <label>Description</label>
                                                        <textarea class="form-control myinput" rows="3" placeholder="" readonly>With a deep passion for yacht management, we bring years of experience to ensure your journey is seamless and unforgettable. Our expert team handles everything, from meticulous planning to ensuring your yacht is fully prepared for the voyage.</textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="sub_sections">
                                                <h2>Sub Section 01</h2>
                                                <div class="cms_section custom_cards_design">
                                                    <div class="row custom_row">
                                                        <div class="col-md-12">
                                                            <div class="txt_field">
                                                                <label>Name</label>
                                                                <input class="form-control myinput" type="text" value="01" readonly>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <div class="txt_field txt_description">
                                                                <label>Description:</label>
                                                                <textarea class="form-control myinput" rows="4" placeholder="" readonly>After embarking M/Y Super Yacht in Phuket, we set sail at 1pm for the Malacca Strait which is home to one of Thailand’s most famous tourist spots - Phang Nga Bay. The poster child for perfect looking Thailand, Phang Nga Bay is all emerald waters, teetering limestone karsts, colourful islets, and drifting long tail boats. This is the stuff movies are made of and it comes as no surprise that Phang Nga has featured as a filming locale in everything from James Bond to Star Wars. Stretching out for 400 square kilometres and with jaw-dropping scenery, a marine national park, ancient rock paintings, and a ton of amazing adventures, you can drop anchor and never want to leave.</textarea>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <h3>Icon</h3>
                                                            <div class="custom_profile_upload custom_flex">
                                                                <div class="profile_picture">
                                                                    <div class="profile_image">
                                                                        <!--begin::Image input-->
                                                                        <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                            <!--begin::Image preview wrapper-->
                                                                            <div class="image-input-wrapper">
                                                                                <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/icon_img.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/icon_img.png">
                                                                            </div>
                                                                            <!--end::Image preview wrapper-->

                                                                            <!--begin::Edit button-->
                                                                            <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                   data-kt-image-input-action="change"
                                                                                   data-bs-toggle="tooltip"
                                                                                   data-bs-dismiss="click"
                                                                                   title="Change avatar">
                                                                                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                                <!--begin::Inputs-->
                                                                                <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                                <input type="hidden" name="avatar_remove" />
                                                                                <!--end::Inputs-->
                                                                            </label>
                                                                            <!--end::Edit button-->

                                                                            <!--begin::Cancel button-->
                                                                            <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                  data-kt-image-input-action="cancel"
                                                                                  data-bs-toggle="tooltip"
                                                                                  data-bs-dismiss="click"
                                                                                  title="Cancel avatar">
                                                                                <i class="ki-outline ki-cross fs-3"></i>
                                                                            </span>
                                                                            <!--end::Cancel button-->
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="append_sub_section"></div>
                                            <div class="add_sub_section">
                                                <button type="button" class="btn btn_grey add_services"><i class="fa-solid fa-plus"></i>Add More</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="cms_section custom_cards_design">
                                            <h1>Section 04: Call to Action</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Kicker</label>
                                                        <input type="text" class="form-control myinput" value="One Week Itinerary for our Our VIP Client" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" class="form-control myinput" value="Discover Thailand" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Sub Heading</label>
                                                        <input type="text" class="form-control myinput" value="From 23 Jan 2023 to 30 Jan 2023 on board SUPER YACHT" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-6"></div>
                                                <div class="col-md-12">
                                                    <h3>Images</h3>
                                                    <div class="custom_profile_upload custom_flex">
                                                        <div class="profile_picture">
                                                            <div class="profile_image">
                                                                <!--begin::Image input-->
                                                                <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                    <!--begin::Image preview wrapper-->
                                                                    <div class="image-input-wrapper">
                                                                        <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png">
                                                                    </div>
                                                                    <!--end::Image preview wrapper-->

                                                                    <!--begin::Edit button-->
                                                                    <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                           data-kt-image-input-action="change"
                                                                           data-bs-toggle="tooltip"
                                                                           data-bs-dismiss="click"
                                                                           title="Change avatar">
                                                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                        <!--begin::Inputs-->
                                                                        <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                        <input type="hidden" name="avatar_remove" />
                                                                        <!--end::Inputs-->
                                                                    </label>
                                                                    <!--end::Edit button-->

                                                                    <!--begin::Cancel button-->
                                                                    <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                          data-kt-image-input-action="cancel"
                                                                          data-bs-toggle="tooltip"
                                                                          data-bs-dismiss="click"
                                                                          title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                    <!--end::Cancel button-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="cms_section custom_cards_design">
                                            <h1>Section 05: Sign Up Form</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Kicker</label>
                                                        <input type="text" class="form-control myinput" value="One Week Itinerary for our Our VIP Client" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" class="form-control myinput" value="Discover Thailand" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Description</label>
                                                        <textarea rows="5" class="form-control myinput" readonly>From 23 Jan 2023 to 30 Jan 2023 on board SUPER YACHT</textarea>
                                                    </div>
                                                </div>
                                                <div class="col-md-6"></div>
                                                <div class="col-md-12">
                                                    <h3>Images</h3>
                                                    <div class="custom_profile_upload custom_flex">
                                                        <div class="profile_picture">
                                                            <div class="profile_image">
                                                                <!--begin::Image input-->
                                                                <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                    <!--begin::Image preview wrapper-->
                                                                    <div class="image-input-wrapper">
                                                                        <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png">
                                                                    </div>
                                                                    <!--end::Image preview wrapper-->

                                                                    <!--begin::Edit button-->
                                                                    <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                           data-kt-image-input-action="change"
                                                                           data-bs-toggle="tooltip"
                                                                           data-bs-dismiss="click"
                                                                           title="Change avatar">
                                                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                        <!--begin::Inputs-->
                                                                        <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                        <input type="hidden" name="avatar_remove" />
                                                                        <!--end::Inputs-->
                                                                    </label>
                                                                    <!--end::Edit button-->

                                                                    <!--begin::Cancel button-->
                                                                    <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                          data-kt-image-input-action="cancel"
                                                                          data-bs-toggle="tooltip"
                                                                          data-bs-dismiss="click"
                                                                          title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                    <!--end::Cancel button-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="upload_btn">
                                            <button type="submit" class="btn btn_dark_green">Upload</button>
                                            <a href="#home" class="btn btn_transparent">Cancel</a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="pills-company" role="tabpanel" aria-labelledby="pills-company-tab" tabindex="0">
                            <form>
                                <div class="row custom_row">
                                    <div class="col-md-12">
                                        <div class="cms_edit_btn">
                                            <button type="button" id="edit" class="btn btn_dark_green"><i class="fa-solid fa-pen-to-square"></i>Edit</button>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="cms_section custom_cards_design">
                                            <h1>Section 01: Main Header</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" class="form-control myinput" value="Company" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label>Description</label>
                                                        <textarea rows="2" class="form-control myinput" readonly>Yacht IQ. Innovative technology solutions tailored to meet the real-world needs of yacht owners, management companies, and crew.</textarea>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <h3>Images</h3>
                                                    <div class="custom_profile_upload custom_flex">
                                                        <div class="profile_picture">
                                                            <div class="profile_image">
                                                                <!--begin::Image input-->
                                                                <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                    <!--begin::Image preview wrapper-->
                                                                    <div class="image-input-wrapper">
                                                                        <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage3.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage3.png">
                                                                    </div>
                                                                    <!--end::Image preview wrapper-->

                                                                    <!--begin::Edit button-->
                                                                    <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                           data-kt-image-input-action="change"
                                                                           data-bs-toggle="tooltip"
                                                                           data-bs-dismiss="click"
                                                                           title="Change avatar">
                                                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                        <!--begin::Inputs-->
                                                                        <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                        <input type="hidden" name="avatar_remove" />
                                                                        <!--end::Inputs-->
                                                                    </label>
                                                                    <!--end::Edit button-->

                                                                    <!--begin::Cancel button-->
                                                                    <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                          data-kt-image-input-action="cancel"
                                                                          data-bs-toggle="tooltip"
                                                                          data-bs-dismiss="click"
                                                                          title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                    <!--end::Cancel button-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="cms_section custom_cards_design">
                                            <h1>Section 02 : Our Objective</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" class="form-control myinput" value="Our objective" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <textarea rows="3" class="form-control myinput" readonly>Our objective is to redefine industry standards by integrating advanced innovation into intuitive and easy to use technology solutions. We specialise in delivering cutting-edge products for the yachting and luxury hospitality markets, consistently exceeding current expectations of what is possible. Our focus is on developing user-centric solutions that meet the growing demand for modern, high-performance, customizable technology in premium environments.</textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="sub_sections">
                                                <h2>Sub Section 01</h2>
                                                <div class="cms_section custom_cards_design">
                                                    <div class="row custom_row">
                                                        <div class="col-md-12">
                                                            <div class="txt_field txt_description">
                                                                <label>Description</label>
                                                                <textarea class="form-control myinput" rows="4" placeholder="" readonly>We’re more connected than ever. More powerful, more creative and more aware with how information is shared and distributed. Yacht IQ builds on years of inventive solutions to bring you a simple yet sophisticated set of applications to help keep you informed and connected while onboard. With over 13 years of experience delivering cutting-edge IT solutions and delivering custom software solutions to UHNW clients, Yacht-IQ is what you need.</textarea>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <h3>Images</h3>
                                                            <div class="custom_profile_upload custom_flex">
                                                                <div class="profile_picture">
                                                                    <div class="profile_image">
                                                                        <!--begin::Image input-->
                                                                        <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                            <!--begin::Image preview wrapper-->
                                                                            <div class="image-input-wrapper">
                                                                                <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage5.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage5.png">
                                                                            </div>
                                                                            <!--end::Image preview wrapper-->

                                                                            <!--begin::Edit button-->
                                                                            <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                   data-kt-image-input-action="change"
                                                                                   data-bs-toggle="tooltip"
                                                                                   data-bs-dismiss="click"
                                                                                   title="Change avatar">
                                                                                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                                <!--begin::Inputs-->
                                                                                <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                                <input type="hidden" name="avatar_remove" />
                                                                                <!--end::Inputs-->
                                                                            </label>
                                                                            <!--end::Edit button-->

                                                                            <!--begin::Cancel button-->
                                                                            <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                  data-kt-image-input-action="cancel"
                                                                                  data-bs-toggle="tooltip"
                                                                                  data-bs-dismiss="click"
                                                                                  title="Cancel avatar">
                                                                                <i class="ki-outline ki-cross fs-3"></i>
                                                                            </span>
                                                                            <!--end::Cancel button-->
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="append_sub_section"></div>
                                            <div class="add_sub_section">
                                                <button type="button" class="btn btn_grey our_objective"><i class="fa-solid fa-plus"></i>Add More</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="upload_btn">
                                            <button type="submit" class="btn btn_dark_green">Upload</button>
                                            <a href="#home" class="btn btn_transparent">Cancel</a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="pills-pricing" role="tabpanel" aria-labelledby="pills-pricing-tab" tabindex="0">
                            <h1>Pricing</h1>
                        </div>
                        <div class="tab-pane fade" id="pills-services" role="tabpanel" aria-labelledby="pills-services-tab" tabindex="0">
                            <form>
                                <div class="row custom_row">
                                    <div class="col-md-12">
                                        <div class="cms_edit_btn">
                                            <button type="button" id="edit" class="btn btn_dark_green"><i class="fa-solid fa-pen-to-square"></i>Edit</button>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="cms_section custom_cards_design">
                                            <h1>Section 01: Main Header</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" class="form-control myinput" value="Services" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label>Description</label>
                                                        <textarea rows="2" class="form-control myinput" readonly>Enjoy the open waters without worrying about the details. From trip planning to logistics, our dedicated team takes care of everything so you can focus on the journey ahead.</textarea>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <h3>Images</h3>
                                                    <div class="custom_profile_upload custom_flex">
                                                        <div class="profile_picture">
                                                            <div class="profile_image">
                                                                <!--begin::Image input-->
                                                                <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                    <!--begin::Image preview wrapper-->
                                                                    <div class="image-input-wrapper">
                                                                        <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage2.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage2.png">
                                                                    </div>
                                                                    <!--end::Image preview wrapper-->

                                                                    <!--begin::Edit button-->
                                                                    <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                           data-kt-image-input-action="change"
                                                                           data-bs-toggle="tooltip"
                                                                           data-bs-dismiss="click"
                                                                           title="Change avatar">
                                                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                        <!--begin::Inputs-->
                                                                        <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                        <input type="hidden" name="avatar_remove" />
                                                                        <!--end::Inputs-->
                                                                    </label>
                                                                    <!--end::Edit button-->

                                                                    <!--begin::Cancel button-->
                                                                    <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                          data-kt-image-input-action="cancel"
                                                                          data-bs-toggle="tooltip"
                                                                          data-bs-dismiss="click"
                                                                          title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                    <!--end::Cancel button-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="cms_section custom_cards_design">
                                            <h1>Section 02 : Our Services</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Kicker</label>
                                                        <input type="text" class="form-control myinput" value="Our Services" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" class="form-control myinput" value="Tailored Yacht Services" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <textarea rows="3" class="form-control myinput" readonly>Experience personalized yacht services designed to meet your unique needs. From planning to maintenance, we handle every detail so you can enjoy a stress-free journey.</textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="sub_sections">
                                                <h2>Sub Section 01</h2>
                                                <div class="cms_section custom_cards_design">
                                                    <div class="row custom_row">
                                                        <div class="col-md-12">
                                                            <div class="txt_field">
                                                                <label>Name</label>
                                                                <input type="text" class="form-control myinput" value="Yacht Registration" readonly>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <div class="txt_field txt_description">
                                                                <label>Description</label>
                                                                <textarea class="form-control myinput" rows="2" placeholder="" readonly>With a deep passion for yacht management, we bring years of experience to ensure your journey is seamless and unforgettable. Our expert team handles everything, from meticulous planning to ensuring your yacht is fully prepared for the voyage.</textarea>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <h3>Images</h3>
                                                            <div class="custom_profile_upload custom_flex">
                                                                <div class="profile_picture">
                                                                    <div class="profile_image">
                                                                        <!--begin::Image input-->
                                                                        <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                            <!--begin::Image preview wrapper-->
                                                                            <div class="image-input-wrapper">
                                                                                <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage6.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage6.png">
                                                                            </div>
                                                                            <!--end::Image preview wrapper-->

                                                                            <!--begin::Edit button-->
                                                                            <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                   data-kt-image-input-action="change"
                                                                                   data-bs-toggle="tooltip"
                                                                                   data-bs-dismiss="click"
                                                                                   title="Change avatar">
                                                                                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                                <!--begin::Inputs-->
                                                                                <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                                <input type="hidden" name="avatar_remove" />
                                                                                <!--end::Inputs-->
                                                                            </label>
                                                                            <!--end::Edit button-->

                                                                            <!--begin::Cancel button-->
                                                                            <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                  data-kt-image-input-action="cancel"
                                                                                  data-bs-toggle="tooltip"
                                                                                  data-bs-dismiss="click"
                                                                                  title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                            <!--end::Cancel button-->
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="profile_picture">
                                                                    <div class="profile_image">
                                                                        <!--begin::Image input-->
                                                                        <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                            <!--begin::Image preview wrapper-->
                                                                            <div class="image-input-wrapper">
                                                                                <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage4.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage4.png">
                                                                            </div>
                                                                            <!--end::Image preview wrapper-->

                                                                            <!--begin::Edit button-->
                                                                            <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                   data-kt-image-input-action="change"
                                                                                   data-bs-toggle="tooltip"
                                                                                   data-bs-dismiss="click"
                                                                                   title="Change avatar">
                                                                                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                                <!--begin::Inputs-->
                                                                                <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                                <input type="hidden" name="avatar_remove" />
                                                                                <!--end::Inputs-->
                                                                            </label>
                                                                            <!--end::Edit button-->

                                                                            <!--begin::Cancel button-->
                                                                            <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                  data-kt-image-input-action="cancel"
                                                                                  data-bs-toggle="tooltip"
                                                                                  data-bs-dismiss="click"
                                                                                  title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                            <!--end::Cancel button-->
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="sub_sections">
                                                <h2>Sub Section 02</h2>
                                                <div class="cms_section custom_cards_design">
                                                    <div class="row custom_row">
                                                        <div class="col-md-12">
                                                            <div class="txt_field">
                                                                <label>Name</label>
                                                                <input type="text" class="form-control myinput" value="Crew & Staff Management" readonly>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <div class="txt_field txt_description">
                                                                <label>Description</label>
                                                                <textarea class="form-control myinput" rows="2" placeholder="" readonly>With a deep passion for yacht management, we bring years of experience to ensure your journey is seamless and unforgettable. Our expert team handles everything, from meticulous planning to ensuring your yacht is fully prepared for the voyage.</textarea>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <h3>Images</h3>
                                                            <div class="custom_profile_upload custom_flex">
                                                                <div class="profile_picture">
                                                                    <div class="profile_image">
                                                                        <!--begin::Image input-->
                                                                        <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                            <!--begin::Image preview wrapper-->
                                                                            <div class="image-input-wrapper">
                                                                                <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage6.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage6.png">
                                                                            </div>
                                                                            <!--end::Image preview wrapper-->

                                                                            <!--begin::Edit button-->
                                                                            <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                   data-kt-image-input-action="change"
                                                                                   data-bs-toggle="tooltip"
                                                                                   data-bs-dismiss="click"
                                                                                   title="Change avatar">
                                                                                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                                <!--begin::Inputs-->
                                                                                <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                                <input type="hidden" name="avatar_remove" />
                                                                                <!--end::Inputs-->
                                                                            </label>
                                                                            <!--end::Edit button-->

                                                                            <!--begin::Cancel button-->
                                                                            <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                  data-kt-image-input-action="cancel"
                                                                                  data-bs-toggle="tooltip"
                                                                                  data-bs-dismiss="click"
                                                                                  title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                            <!--end::Cancel button-->
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="profile_picture">
                                                                    <div class="profile_image">
                                                                        <!--begin::Image input-->
                                                                        <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                            <!--begin::Image preview wrapper-->
                                                                            <div class="image-input-wrapper">
                                                                                <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage4.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage4.png">
                                                                            </div>
                                                                            <!--end::Image preview wrapper-->

                                                                            <!--begin::Edit button-->
                                                                            <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                   data-kt-image-input-action="change"
                                                                                   data-bs-toggle="tooltip"
                                                                                   data-bs-dismiss="click"
                                                                                   title="Change avatar">
                                                                                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                                <!--begin::Inputs-->
                                                                                <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                                <input type="hidden" name="avatar_remove" />
                                                                                <!--end::Inputs-->
                                                                            </label>
                                                                            <!--end::Edit button-->

                                                                            <!--begin::Cancel button-->
                                                                            <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                  data-kt-image-input-action="cancel"
                                                                                  data-bs-toggle="tooltip"
                                                                                  data-bs-dismiss="click"
                                                                                  title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                            <!--end::Cancel button-->
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="sub_sections">
                                                <h2>Sub Section 03</h2>
                                                <div class="cms_section custom_cards_design">
                                                    <div class="row custom_row">
                                                        <div class="col-md-12">
                                                            <div class="txt_field">
                                                                <label>Name</label>
                                                                <input type="text" class="form-control myinput" value="Luxury Add-Ons & Experiences" readonly>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <div class="txt_field txt_description">
                                                                <label>Description</label>
                                                                <textarea class="form-control myinput" rows="2" placeholder="" readonly>With a deep passion for yacht management, we bring years of experience to ensure your journey is seamless and unforgettable. Our expert team handles everything, from meticulous planning to ensuring your yacht is fully prepared for the voyage.</textarea>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <h3>Images</h3>
                                                            <div class="custom_profile_upload custom_flex">
                                                                <div class="profile_picture">
                                                                    <div class="profile_image">
                                                                        <!--begin::Image input-->
                                                                        <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                            <!--begin::Image preview wrapper-->
                                                                            <div class="image-input-wrapper">
                                                                                <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage6.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage6.png">
                                                                            </div>
                                                                            <!--end::Image preview wrapper-->

                                                                            <!--begin::Edit button-->
                                                                            <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                   data-kt-image-input-action="change"
                                                                                   data-bs-toggle="tooltip"
                                                                                   data-bs-dismiss="click"
                                                                                   title="Change avatar">
                                                                                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                                <!--begin::Inputs-->
                                                                                <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                                <input type="hidden" name="avatar_remove" />
                                                                                <!--end::Inputs-->
                                                                            </label>
                                                                            <!--end::Edit button-->

                                                                            <!--begin::Cancel button-->
                                                                            <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                  data-kt-image-input-action="cancel"
                                                                                  data-bs-toggle="tooltip"
                                                                                  data-bs-dismiss="click"
                                                                                  title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                            <!--end::Cancel button-->
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="profile_picture">
                                                                    <div class="profile_image">
                                                                        <!--begin::Image input-->
                                                                        <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                            <!--begin::Image preview wrapper-->
                                                                            <div class="image-input-wrapper">
                                                                                <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage4.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage4.png">
                                                                            </div>
                                                                            <!--end::Image preview wrapper-->

                                                                            <!--begin::Edit button-->
                                                                            <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                   data-kt-image-input-action="change"
                                                                                   data-bs-toggle="tooltip"
                                                                                   data-bs-dismiss="click"
                                                                                   title="Change avatar">
                                                                                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                                <!--begin::Inputs-->
                                                                                <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                                <input type="hidden" name="avatar_remove" />
                                                                                <!--end::Inputs-->
                                                                            </label>
                                                                            <!--end::Edit button-->

                                                                            <!--begin::Cancel button-->
                                                                            <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                  data-kt-image-input-action="cancel"
                                                                                  data-bs-toggle="tooltip"
                                                                                  data-bs-dismiss="click"
                                                                                  title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                            <!--end::Cancel button-->
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="sub_sections">
                                                <h2>Sub Section 04</h2>
                                                <div class="cms_section custom_cards_design">
                                                    <div class="row custom_row">
                                                        <div class="col-md-12">
                                                            <div class="txt_field">
                                                                <label>Name</label>
                                                                <input type="text" class="form-control myinput" value="24/7 Support & Assistance" readonly>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <div class="txt_field txt_description">
                                                                <label>Description</label>
                                                                <textarea class="form-control myinput" rows="2" placeholder="" readonly>With a deep passion for yacht management, we bring years of experience to ensure your journey is seamless and unforgettable. Our expert team handles everything, from meticulous planning to ensuring your yacht is fully prepared for the voyage.</textarea>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-12">
                                                            <h3>Images</h3>
                                                            <div class="custom_profile_upload custom_flex">
                                                                <div class="profile_picture">
                                                                    <div class="profile_image">
                                                                        <!--begin::Image input-->
                                                                        <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                            <!--begin::Image preview wrapper-->
                                                                            <div class="image-input-wrapper">
                                                                                <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage6.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage6.png">
                                                                            </div>
                                                                            <!--end::Image preview wrapper-->

                                                                            <!--begin::Edit button-->
                                                                            <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                   data-kt-image-input-action="change"
                                                                                   data-bs-toggle="tooltip"
                                                                                   data-bs-dismiss="click"
                                                                                   title="Change avatar">
                                                                                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                                <!--begin::Inputs-->
                                                                                <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                                <input type="hidden" name="avatar_remove" />
                                                                                <!--end::Inputs-->
                                                                            </label>
                                                                            <!--end::Edit button-->

                                                                            <!--begin::Cancel button-->
                                                                            <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                  data-kt-image-input-action="cancel"
                                                                                  data-bs-toggle="tooltip"
                                                                                  data-bs-dismiss="click"
                                                                                  title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                            <!--end::Cancel button-->
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="profile_picture">
                                                                    <div class="profile_image">
                                                                        <!--begin::Image input-->
                                                                        <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                            <!--begin::Image preview wrapper-->
                                                                            <div class="image-input-wrapper">
                                                                                <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage4.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage4.png">
                                                                            </div>
                                                                            <!--end::Image preview wrapper-->

                                                                            <!--begin::Edit button-->
                                                                            <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                   data-kt-image-input-action="change"
                                                                                   data-bs-toggle="tooltip"
                                                                                   data-bs-dismiss="click"
                                                                                   title="Change avatar">
                                                                                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                                <!--begin::Inputs-->
                                                                                <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                                <input type="hidden" name="avatar_remove" />
                                                                                <!--end::Inputs-->
                                                                            </label>
                                                                            <!--end::Edit button-->

                                                                            <!--begin::Cancel button-->
                                                                            <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                  data-kt-image-input-action="cancel"
                                                                                  data-bs-toggle="tooltip"
                                                                                  data-bs-dismiss="click"
                                                                                  title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                            <!--end::Cancel button-->
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="append_sub_section"></div>
                                            <div class="add_sub_section">
                                                <button type="button" class="btn btn_grey add_services_btn"><i class="fa-solid fa-plus"></i>Add More</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="upload_btn">
                                            <button type="submit" class="btn btn_dark_green">Upload</button>
                                            <a href="#home" class="btn btn_transparent">Cancel</a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="pills-contact" role="tabpanel" aria-labelledby="pills-contact-tab" tabindex="0">
                            <form>
                                <div class="row custom_row">
                                    <div class="col-md-12">
                                        <div class="cms_edit_btn">
                                            <button type="button" id="edit" class="btn btn_dark_green"><i class="fa-solid fa-pen-to-square"></i>Edit</button>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="cms_section custom_cards_design">
                                            <h1>Section 01: Sign Up Form</h1>
                                            <div class="row custom_row">
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Kicker</label>
                                                        <input type="text" class="form-control myinput" value="One Week Itinerary for our Our VIP Client" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="txt_field">
                                                        <label>Heading</label>
                                                        <input type="text" class="form-control myinput" value="Discover Thailand" readonly>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <div class="txt_field txt_description">
                                                        <label>Description</label>
                                                        <textarea class="form-control myinput" rows="3" placeholder="" readonly>We’re more connected than ever. More powerful, more creative and more aware with how information is shared and distributed. Yacht IQ builds on years of inventive solutions to bring you a simple yet sophisticated set of applications to help keep you informed and connected while onboard. With over 13 years of experience delivering cutting-edge IT solutions and delivering custom software solutions to UHNW clients, Yacht-IQ is what you need</textarea>
                                                    </div>
                                                </div>
                                                <div class="col-md-12">
                                                    <h3>Images</h3>
                                                    <div class="custom_profile_upload custom_flex">
                                                        <div class="profile_picture">
                                                            <div class="profile_image">
                                                                <!--begin::Image input-->
                                                                <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                    <!--begin::Image preview wrapper-->
                                                                    <div class="image-input-wrapper">
                                                                        <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage3.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage3.png">
                                                                    </div>
                                                                    <!--end::Image preview wrapper-->

                                                                    <!--begin::Edit button-->
                                                                    <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                           data-kt-image-input-action="change"
                                                                           data-bs-toggle="tooltip"
                                                                           data-bs-dismiss="click"
                                                                           title="Change avatar">
                                                                        <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>

                                                                        <!--begin::Inputs-->
                                                                        <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/>
                                                                        <input type="hidden" name="avatar_remove" />
                                                                        <!--end::Inputs-->
                                                                    </label>
                                                                    <!--end::Edit button-->

                                                                    <!--begin::Cancel button-->
                                                                    <span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                          data-kt-image-input-action="cancel"
                                                                          data-bs-toggle="tooltip"
                                                                          data-bs-dismiss="click"
                                                                          title="Cancel avatar">
                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                </span>
                                                                    <!--end::Cancel button-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="upload_btn">
                                            <button type="submit" class="btn btn_dark_green">Upload</button>
                                            <a href="#home" class="btn btn_transparent">Cancel</a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script>
    $(document).ready(function () {
//        readonly to editable input fields
        $(".cms_edit_btn button").click(function(){
            var allInputField = $(this).closest("form").find(".myinput");
            $(allInputField).each(function() {
                var input = $(this);
                input.removeAttr("readonly", !input.attr("readonly"));
                if (input.attr("readonly")) {}
            });
        });

        var count =1;
        var objective =1;
        var services =4;

//        cms home tab services section append jquery
        $(".cms_section .add_sub_section .add_services").click(function () {
            count ++;
            $(this).closest(".cms_section").find(".append_sub_section").append(
                '<div class="sub_sections appended_section"><h2>Sub Section 0'+ count +'</h2> <div class="cms_section custom_cards_design"> <div class="row custom_row"> <div class="col-md-12"> <div class="txt_field"> <label>Name</label> <input class="form-control myinput" type="text" value="01" readonly> </div> </div>' +
                '<div class="col-md-12"> <div class="txt_field txt_description"> <label>Description:</label> <textarea class="form-control myinput" rows="4" placeholder="" readonly>After embarking M/Y Super Yacht in Phuket, we set sail at 1pm for the Malacca Strait which is home to one of Thailand’s most famous tourist spots - Phang Nga Bay. The poster child for perfect looking Thailand, Phang Nga Bay is all emerald waters, teetering limestone karsts, colourful islets, and drifting long tail boats. This is the stuff movies are made of and it comes as no surprise that Phang Nga has featured as a filming locale in everything from James Bond to Star Wars. Stretching out for 400 square kilometres and with jaw-dropping scenery, a marine national park, ancient rock paintings, and a ton of amazing adventures, you can drop anchor and never want to leave.</textarea> </div> </div>' +
                '<div class="col-md-12"> <h3>Icon</h3> <div class="custom_profile_upload custom_flex"> <div class="profile_picture"> <div class="profile_image">' +
                '<div class="image-input image-input-outline" data-kt-image-input="true"><div class="image-input-wrapper"><img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/icon_img.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/icon_img.png"> </div> <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="change"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i> <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/> <input type="hidden" name="avatar_remove" />' +
                '</label>' +
                '<span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="cancel"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Cancel avatar"> <i class="ki-outline ki-cross fs-3"></i> </span>' +
                '</div> </div> </div> </div> </div> </div> </div> </div>')
        })


//        cms company tab objective section append jquery
        $(".cms_section .add_sub_section .our_objective").click(function () {
            objective ++;
            $(this).closest(".cms_section").find(".append_sub_section").append(
                '<div class="sub_sections appended_section"><h2>Sub Section 0'+ objective +'</h2> <div class="cms_section custom_cards_design"> <div class="row custom_row"> <div class="col-md-12"> <div class="txt_field txt_description"> <label>Description</label> <textarea class="form-control myinput" rows="4" placeholder="" readonly>We’re more connected than ever. More powerful, more creative and more aware with how information is shared and distributed. Yacht IQ builds on years of inventive solutions to bring you a simple yet sophisticated set of applications to help keep you informed and connected while onboard. With over 13 years of experience delivering cutting-edge IT solutions and delivering custom software solutions to UHNW clients, Yacht-IQ is what you need.</textarea> </div> </div>' +
                '<div class="col-md-12"><h3>Images</h3> <div class="custom_profile_upload custom_flex"> <div class="profile_picture"> <div class="profile_image">' +
                '<div class="image-input image-input-outline" data-kt-image-input="true"><div class="image-input-wrapper"> <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage5.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage5.png"> </div>' +
                '<label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="change"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i> <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/> <input type="hidden" name="avatar_remove" />' +
                '</label>' +
                '<span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="cancel"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Cancel avatar"> <i class="ki-outline ki-cross fs-3"></i> </span> </div> </div> </div> </div> </div> </div> </div> </div>')
        });

//        cms services tab our services section append jquery
        $(".cms_section .add_sub_section .add_services_btn").click(function () {
            services ++;
            $(this).closest(".cms_section").find(".append_sub_section").append(
                '<div class="sub_sections appended_section"> <h2>Sub Section 0'+ services +'</h2> <div class="cms_section custom_cards_design"> <div class="row custom_row"> <div class="col-md-12"> <div class="txt_field"> <label>Name</label> <input type="text" class="form-control myinput" value="Crew & Staff Management" readonly> </div> </div>' +
                '<div class="col-md-12"> <div class="txt_field txt_description"> <label>Description</label> <textarea class="form-control myinput" rows="2" placeholder="" readonly>With a deep passion for yacht management, we bring years of experience to ensure your journey is seamless and unforgettable. Our expert team handles everything, from meticulous planning to ensuring your yacht is fully prepared for the voyage.</textarea> </div> </div>' +
                '<div class="col-md-12"><h3>Images</h3> <div class="custom_profile_upload custom_flex"> <div class="profile_picture"> <div class="profile_image"> <div class="image-input image-input-outline" data-kt-image-input="true"> <div class="image-input-wrapper"> <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage6.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage6.png"> </div>' +
                '<label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="change"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i> <input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/> <input type="hidden" name="avatar_remove" /> </label>' +
                '<span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="cancel"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Cancel avatar"> <i class="ki-outline ki-cross fs-3"></i> </span> </div> </div> </div> <div class="profile_picture">' +
                '<div class="profile_image"> <div class="image-input image-input-outline" data-kt-image-input="true"> <div class="image-input-wrapper"> <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage4.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage4.png"> </div>'+
                '<label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="change"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Change avatar"> <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>'+
                '<input type="file" name="avatar" accept=".png, .jpg, .jpeg" class="custom_file_input"/><input type="hidden" name="avatar_remove" />' +
                '</label>' +
                '<span class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"data-kt-image-input-action="cancel"data-bs-toggle="tooltip"data-bs-dismiss="click"title="Cancel avatar"> <i class="ki-outline ki-cross fs-3"></i> </span> </div> </div> </div> </div> </div> </div> </div> </div>')
        });

        // Re-initialize the input[type="file"] functionality for newly appended sections
        $(document).on('change', '.appended_section input[type="file"]', function () {
            var reader = new FileReader();
            var $imageInputWrapper = $(this).closest('.appended_section .image-input').find('.image-input-wrapper');

            reader.onload = function (e) {
                $imageInputWrapper.html('<img class="custom_img" src="' + e.target.result + '" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"/>');
            }

            // Load the selected image into the preview
            reader.readAsDataURL(this.files[0]);
        });

        // Add event listener to remove the image when clicking the 'cancel' button
        $(document).on('click', '.appended_section [data-kt-image-input-action="cancel"]', function () {
            var newImg = $(this).closest('.appended_section .image-input').find('.image-input-wrapper img.custom_img');
            var originalSrc = newImg.attr('data-original-src');
            newImg.attr('src', originalSrc);
        });

    })
</script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\dashboard\Admin\content_management.blade.php ENDPATH**/ ?>