

<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>

<section class="content_management">
    <div class="container-fluid">
        <div class="row custom_row">
            <div class="col-md-12">
                <div class="cms_tabs">
                    <?php echo $__env->make('cms_navbar.cms_navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>
                <div>
                    <div>
                        <form action="<?php echo e(route('abouts.update', $about->id)); ?>" method="POST" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            <div class="row custom_row">
                                <div class="col-md-12">
                                    <div class="cms_edit_btn">
                                        <button type="button" id="edit" class="btn btn_dark_green"><i class="fa-solid fa-pen-to-square"></i>Edit</button>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section custom_cards_design">
                                        <h1>Section: Main Header</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-12">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" name="heading" class="form-control myinput <?php $__errorArgs = ['heading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" value="<?php echo e($about->heading); ?>">
                                                    <?php $__errorArgs = ['heading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="invalid-feedback"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="txt_field">
                                                    <label>Description</label>
                                                    <textarea rows="2" name="description" class="form-control myinput <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"><?php echo e($about->description); ?></textarea>
                                                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="invalid-feedback"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <h3>Background Image</h3>
                                                <div class="custom_profile_upload custom_flex">
                                                    <div class="profile_picture">
                                                        <div class="profile_image">
                                                            <!--begin::Image input-->
                                                            <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                <!--begin::Image preview wrapper-->
                                                                <div class="image-input-wrapper">
                                                                    <img class="input_image_field" src="<?php echo e(asset('website/' . $about->image)); ?>" data-original-src="<?php echo e(asset('website/' . $about->image)); ?>">
                                                                </div>
                                                                <!--end::Image preview wrapper-->

                                                                <!--begin::Edit button-->
                                                                <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon position-absolute top-10 end-0" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Change avatar">
                                                                    <i class="ki-duotone ki-pencil fs-6">
                                                                        <span class="path1"></span>
                                                                        <span class="path2"></span>
                                                                    </i>

                                                                    <!--begin::Inputs-->
                                                                    <input type="file" name="main_image" accept=".png, .jpg, .jpeg" class="custom_file_input" />
                                                                    <!--end::Inputs-->
                                                                </label>
                                                                <!--end::Edit button-->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="cms_section custom_cards_design">
                                        <h1>Section: Our Objective</h1>
                                        <div class="row custom_row">
                                            <div class="col-md-12">
                                                <div class="txt_field">
                                                    <label>Heading</label>
                                                    <input type="text" class="form-control myinput <?php $__errorArgs = ['about_heading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="about_heading" value="<?php echo e($about->about_heading); ?>">
                                                    <?php $__errorArgs = ['about_heading'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="invalid-feedback"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="txt_field">
                                                    <label>Description</label>
                                                    <textarea rows="3" class="form-control myinput <?php $__errorArgs = ['about_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="about_description"><?php echo e($about->about_description); ?></textarea>
                                                    <?php $__errorArgs = ['about_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <span class="invalid-feedback"><?php echo e($message); ?></span>
                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="append_sub_section">
                                            <?php $__currentLoopData = $about->about_details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $about_detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="sub_sections" data-id="<?php echo e($about_detail->id); ?>">
                                                    <div class="d-flex justify-content-between">
                                                        <h2>Sub Section</h2>
                                                        <button type="button" class="btn btn-sm delete_subsection" data-id="<?php echo e($about_detail->id); ?>"><i class="fas fa-trash-alt fa-lg" style="color:#e00038; font-size: 20px;"></i></button>
                                                    </div>
                                                    <div class="cms_section custom_cards_design">
                                                        <div class="row custom_row">
                                                            <div class="col-md-12">
                                                                <div class="txt_field txt_description">
                                                                    <label>Description</label>
                                                                    <textarea required class="form-control myinput" rows="4" placeholder="Type Here ...." name="about_detail[<?php echo e($about_detail->id); ?>][description]"><?php echo e($about_detail->description); ?></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-12">
                                                                <h3>Image</h3>
                                                                <div class="custom_profile_upload custom_flex">
                                                                    <div class="profile_picture">
                                                                        <div class="profile_image">
                                                                            <!--begin::Image input-->
                                                                            <div class="image-input image-input-outline" data-kt-image-input="true">
                                                                                <!--begin::Image preview wrapper-->
                                                                                <div class="image-input-wrapper">
                                                                                    <img class="input_image_field" src="<?php echo e(asset('website/' . $about_detail->image)); ?>" data-original-src="<?php echo e(asset('website/' . $about_detail->image)); ?>">
                                                                                </div>
                                                                                <!--end::Image preview wrapper-->

                                                                                <!--begin::Edit button-->
                                                                                <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon position-absolute top-10 end-0" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Change avatar">
                                                                                    <i class="ki-duotone ki-pencil fs-6">
                                                                                        <span class="path1"></span>
                                                                                        <span class="path2"></span>
                                                                                    </i>

                                                                                    <!--begin::Inputs-->
                                                                                    <input type="file" name="about_detail[<?php echo e($about_detail->id); ?>][image]" accept=".png, .jpg, .jpeg" class="custom_file_input" />
                                                                                    <!--end::Inputs-->
                                                                                </label>
                                                                                <!--end::Edit button-->

                                                                                <!--begin::Cancel button-->
                                                                                <!-- <span
                                                                                    class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon"
                                                                                    data-kt-image-input-action="cancel"
                                                                                    data-bs-toggle="tooltip"
                                                                                    data-bs-dismiss="click"
                                                                                    title="Cancel avatar">
                                                                                    <i class="ki-outline ki-cross fs-3"></i>
                                                                                </span> -->
                                                                                <!--end::Cancel button-->
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <div class="add_sub_section">
                                            <button type="button" class="btn btn_grey our_objective"><i class="fa-solid fa-plus"></i>Add More</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="upload_btn">
                                        <button type="submit" class="btn btn_dark_green">Upload</button>
                                        <a href="#home" class="btn btn_transparent">Cancel</a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function () {

            //        readonly to editable input fields
            $(".cms_edit_btn button").click(function () {
                var allInputField = $(this).closest("form").find(".myinput");
                $(allInputField).each(function () {
                    var input = $(this);
                    input.removeAttr("readonly", !input.attr("readonly"));
                    if (input.attr("readonly")) { }
                });
            });

            //  cms company tab objective section append jquery
            $(".cms_section .add_sub_section .our_objective").click(function () {

                var $appendSubSection = $(this).closest(".cms_section").find(".append_sub_section");
                var count = $appendSubSection.find(".sub_sections").length + 1;

                console.log(count);

                $appendSubSection.append(`
                    <div class="sub_sections appended_section">
                        <div class="d-flex justify-content-between">
                            <h2>Sub Section</h2> 
                            <button type="button" class="btn btn-sm delete_subsections" data-id="new_${count}">
                                <i class="fas fa-trash-alt fa-lg" style="color:#1b005d; font-size: 20px;"></i>
                            </button>
                        </div>
                        <div class="cms_section custom_cards_design">
                            <div class="row custom_row">
                                <div class="col-md-12">
                                    <div class="txt_field txt_description">
                                        <label>Description</label> 
                                        <textarea required class="form-control myinput" rows="4" placeholder="Type Here ...." name="about_detail[new_${count}[description]"></textarea> 
                                    </div> 
                                </div>
                                <div class="col-md-12">
                                    <h3>Image</h3>
                                    <div class="custom_profile_upload custom_flex">
                                        <div class="profile_picture">
                                            <div class="profile_image">
                                                <div class="image-input image-input-outline" data-kt-image-input="true">
                                                    <div class="image-input-wrapper">
                                                        <img class="input_image_field" src="<?php echo e(asset('website')); ?>/assets/images/cmsimage5.png" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage5.png"> 
                                                    </div>
                                                    <label class="btn btn-icon btn-circle btn-active-color-primary shadow edit_icon position-absolute top-10 end-0" data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss=" click"title="Change avatar"> 
                                                        <i class="ki-duotone ki-pencil fs-6">
                                                            <span class="path1"></span>
                                                            <span class="path2"></span>
                                                        </i> 
                                                        <input type="file"  name="about_detail[new_${count}][image]" accept=".png, .jpg, .jpeg" class="custom_file_input"/> 
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>`);
            });

            // Re-initialize the input[type="file"] functionality for newly appended sections
            $(document).on('change', '.appended_section input[type="file"]', function () {
                var reader = new FileReader();
                var $imageInputWrapper = $(this).closest('.appended_section .image-input').find('.image-input-wrapper');

                reader.onload = function (e) {
                    $imageInputWrapper.html('<img class="custom_img" src="' + e.target.result + '" data-original-src="<?php echo e(asset('website')); ?>/assets/images/cmsimage1.png"/>');
                }

                // Load the selected image into the preview
                reader.readAsDataURL(this.files[0]);
            });

            // Add event listener to remove the image when clicking the 'cancel' button
            $(document).on('click', '.appended_section [data-kt-image-input-action="cancel"]', function () {
                var newImg = $(this).closest('.appended_section .image-input').find('.image-input-wrapper img.custom_img');
                var originalSrc = newImg.attr('data-original-src');
                newImg.attr('src', originalSrc);
            });


            // Remove the appended section from the DOM
            $(document).on('click', '.delete_subsections', function () {
                var sectionToRemove = $(this).closest('.sub_sections');
                sectionToRemove.remove(); 
            });

             // Remove the section from the Database
            $(document).on('click', '.delete_subsection', function () {
                var subSectionId = $(this).data('id');
                var subSection = $(this).closest('.sub_sections');
                var deleteUrl = "<?php echo e(route('about.detail.delete', ['id' => '__ID__'])); ?>".replace('__ID__', subSectionId);


                showDeleteConfirmation(deleteUrl, subSection, 'Subsection Deleted Successfully!!');
            });

        });
    </script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\guesttrip\resources\views\abouts\edit.blade.php ENDPATH**/ ?>